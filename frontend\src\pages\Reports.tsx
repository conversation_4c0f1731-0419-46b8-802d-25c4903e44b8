import React, { useState } from 'react';
import { useQuery } from 'react-query';
import {
  DocumentTextIcon,
  ChartBarIcon,
  ArrowDownTrayIcon,
  CalendarIcon,
  FunnelIcon,
} from '@heroicons/react/24/outline';
import { reportsApi, analysisApi } from '../services/api';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';
import { ko } from 'date-fns/locale';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  ResponsiveContainer,
} from 'recharts';

const Reports: React.FC = () => {
  const [dateRange, setDateRange] = useState({
    start: format(subDays(new Date(), 30), 'yyyy-MM-dd'),
    end: format(new Date(), 'yyyy-MM-dd'),
  });
  const [reportType, setReportType] = useState<'summary' | 'detailed' | 'taxonomy'>('summary');

  // 분석 요약 데이터
  const { data: analysisSummary, isLoading: summaryLoading } = useQuery(
    ['analysis-summary', dateRange],
    () => reportsApi.getAnalysisSummary({
      start_date: dateRange.start,
      end_date: dateRange.end,
    })
  );

  // 분류 체계 통계
  const { data: taxonomyStats } = useQuery(
    'taxonomy-statistics',
    reportsApi.getTaxonomyStatistics
  );

  // 전체 분석 결과
  const { data: allAnalyses } = useQuery(
    'all-analyses',
    () => analysisApi.getAllAnalysisResults()
  );

  // 차트 색상
  const COLORS = ['#3B82F6', '#EF4444', '#F59E0B', '#10B981', '#8B5CF6', '#F97316'];

  // 심각도별 분포 데이터 준비
  const severityData = React.useMemo(() => {
    if (!analysisSummary?.data?.distributions?.severities) return [];
    
    return Object.entries(analysisSummary.data.distributions.severities).map(([severity, count]) => ({
      name: severity,
      value: count as number,
      color: severity === 'critical' ? '#EF4444' :
             severity === 'high' ? '#F97316' :
             severity === 'medium' ? '#F59E0B' :
             severity === 'low' ? '#3B82F6' : '#6B7280'
    }));
  }, [analysisSummary]);

  // 카테고리별 분포 데이터 준비
  const categoryData = React.useMemo(() => {
    if (!analysisSummary?.data?.distributions?.categories) return [];
    
    return Object.entries(analysisSummary.data.distributions.categories)
      .slice(0, 10) // 상위 10개만
      .map(([category, count]) => ({
        name: category.length > 20 ? `${category.substring(0, 20)}...` : category,
        fullName: category,
        count: count as number,
      }));
  }, [analysisSummary]);

  // 시간별 분석 트렌드 데이터 (더미 데이터)
  const trendData = React.useMemo(() => {
    const days = [];
    for (let i = 6; i >= 0; i--) {
      const date = subDays(new Date(), i);
      days.push({
        date: format(date, 'MM/dd'),
        analyses: Math.floor(Math.random() * 20) + 5,
        verified: Math.floor(Math.random() * 15) + 3,
      });
    }
    return days;
  }, []);

  const handleExport = async (type: 'taxonomy' | 'analysis', format: 'json' = 'json') => {
    try {
      let response;
      if (type === 'taxonomy') {
        response = await reportsApi.exportTaxonomy(format);
      } else {
        response = await reportsApi.exportAnalysisResults(format);
      }
      
      // 파일 다운로드
      const blob = new Blob([response.data], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${type}-export-${format(new Date(), 'yyyy-MM-dd')}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* 헤더 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <DocumentTextIcon className="h-8 w-8 text-primary-600 mr-3" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">리포트</h1>
            <p className="mt-1 text-sm text-gray-500">
              분석 결과와 통계를 확인하고 리포트를 생성하세요.
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => handleExport('taxonomy')}
            className="btn-outline"
          >
            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            분류 체계 내보내기
          </button>
          <button
            onClick={() => handleExport('analysis')}
            className="btn-primary"
          >
            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            분석 결과 내보내기
          </button>
        </div>
      </div>

      {/* 필터 및 설정 */}
      <div className="card">
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="form-label">리포트 유형</label>
              <select
                value={reportType}
                onChange={(e) => setReportType(e.target.value as any)}
                className="form-select"
              >
                <option value="summary">요약 리포트</option>
                <option value="detailed">상세 리포트</option>
                <option value="taxonomy">분류 체계 리포트</option>
              </select>
            </div>
            <div>
              <label className="form-label">시작 날짜</label>
              <input
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                className="form-input"
              />
            </div>
            <div>
              <label className="form-label">종료 날짜</label>
              <input
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                className="form-input"
              />
            </div>
          </div>
        </div>
      </div>

      {/* 요약 통계 */}
      {analysisSummary?.data && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <ChartBarIcon className="h-8 w-8 text-primary-500" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {analysisSummary.data.summary.total_analyses}
                  </div>
                  <div className="text-sm text-gray-500">총 분석</div>
                </div>
              </div>
            </div>
          </div>
          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="h-8 w-8 bg-success-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">✓</span>
                </div>
                <div className="ml-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {analysisSummary.data.summary.completed_analyses}
                  </div>
                  <div className="text-sm text-gray-500">완료된 분석</div>
                </div>
              </div>
            </div>
          </div>
          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="h-8 w-8 bg-warning-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">%</span>
                </div>
                <div className="ml-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {(analysisSummary.data.summary.completion_rate * 100).toFixed(1)}%
                  </div>
                  <div className="text-sm text-gray-500">완료율</div>
                </div>
              </div>
            </div>
          </div>
          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">V</span>
                </div>
                <div className="ml-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {analysisSummary.data.summary.verified_analyses}
                  </div>
                  <div className="text-sm text-gray-500">검증된 분석</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 차트 섹션 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 심각도 분포 파이 차트 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">심각도 분포</h3>
          </div>
          <div className="card-body">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={severityData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {severityData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* 카테고리별 분포 바 차트 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">상위 취약점 카테고리</h3>
          </div>
          <div className="card-body">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={categoryData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="name" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis />
                <Tooltip 
                  formatter={(value, name, props) => [value, props.payload.fullName]}
                />
                <Bar dataKey="count" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* 시간별 트렌드 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">최근 7일 분석 트렌드</h3>
        </div>
        <div className="card-body">
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="analyses" 
                stroke="#3B82F6" 
                strokeWidth={2}
                name="총 분석"
              />
              <Line 
                type="monotone" 
                dataKey="verified" 
                stroke="#10B981" 
                strokeWidth={2}
                name="검증된 분석"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* 분류 체계 통계 */}
      {taxonomyStats?.data && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">분류 체계 통계</h3>
          </div>
          <div className="card-body">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600">
                  {taxonomyStats.data.total_categories}
                </div>
                <div className="text-sm text-gray-500">카테고리</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-secondary-600">
                  {taxonomyStats.data.total_subcategories}
                </div>
                <div className="text-sm text-gray-500">하위 카테고리</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-success-600">
                  {taxonomyStats.data.total_variants}
                </div>
                <div className="text-sm text-gray-500">변형</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-warning-600">
                  {taxonomyStats.data.total_nodes}
                </div>
                <div className="text-sm text-gray-500">전체 노드</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Reports;
