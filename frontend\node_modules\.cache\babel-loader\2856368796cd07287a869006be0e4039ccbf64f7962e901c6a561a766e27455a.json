{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { isServer, isValidTimeout, noop, replaceEqualDeep, shallowEqualObjects, timeUntilStale } from './utils';\nimport { notifyManager } from './notifyManager';\nimport { focusManager } from './focusManager';\nimport { Subscribable } from './subscribable';\nimport { getLogger } from './logger';\nimport { isCancelledError } from './retryer';\nexport var QueryObserver = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(QueryObserver, _Subscribable);\n  function QueryObserver(client, options) {\n    var _this;\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n    _this.options = options;\n    _this.trackedProps = [];\n    _this.selectError = null;\n    _this.bindMethods();\n    _this.setOptions(options);\n    return _this;\n  }\n  var _proto = QueryObserver.prototype;\n  _proto.bindMethods = function bindMethods() {\n    this.remove = this.remove.bind(this);\n    this.refetch = this.refetch.bind(this);\n  };\n  _proto.onSubscribe = function onSubscribe() {\n    if (this.listeners.length === 1) {\n      this.currentQuery.addObserver(this);\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch();\n      }\n      this.updateTimers();\n    }\n  };\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      this.destroy();\n    }\n  };\n  _proto.shouldFetchOnReconnect = function shouldFetchOnReconnect() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnReconnect);\n  };\n  _proto.shouldFetchOnWindowFocus = function shouldFetchOnWindowFocus() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnWindowFocus);\n  };\n  _proto.destroy = function destroy() {\n    this.listeners = [];\n    this.clearTimers();\n    this.currentQuery.removeObserver(this);\n  };\n  _proto.setOptions = function setOptions(options, notifyOptions) {\n    var prevOptions = this.options;\n    var prevQuery = this.currentQuery;\n    this.options = this.client.defaultQueryObserverOptions(options);\n    if (typeof this.options.enabled !== 'undefined' && typeof this.options.enabled !== 'boolean') {\n      throw new Error('Expected enabled to be a boolean');\n    } // Keep previous query key if the user does not supply one\n\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey;\n    }\n    this.updateQuery();\n    var mounted = this.hasListeners(); // Fetch if there are subscribers\n\n    if (mounted && shouldFetchOptionally(this.currentQuery, prevQuery, this.options, prevOptions)) {\n      this.executeFetch();\n    } // Update result\n\n    this.updateResult(notifyOptions); // Update stale interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || this.options.staleTime !== prevOptions.staleTime)) {\n      this.updateStaleTimeout();\n    }\n    var nextRefetchInterval = this.computeRefetchInterval(); // Update refetch interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || nextRefetchInterval !== this.currentRefetchInterval)) {\n      this.updateRefetchInterval(nextRefetchInterval);\n    }\n  };\n  _proto.getOptimisticResult = function getOptimisticResult(options) {\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    return this.createResult(query, defaultedOptions);\n  };\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.currentResult;\n  };\n  _proto.trackResult = function trackResult(result, defaultedOptions) {\n    var _this2 = this;\n    var trackedResult = {};\n    var trackProp = function trackProp(key) {\n      if (!_this2.trackedProps.includes(key)) {\n        _this2.trackedProps.push(key);\n      }\n    };\n    Object.keys(result).forEach(function (key) {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: function get() {\n          trackProp(key);\n          return result[key];\n        }\n      });\n    });\n    if (defaultedOptions.useErrorBoundary || defaultedOptions.suspense) {\n      trackProp('error');\n    }\n    return trackedResult;\n  };\n  _proto.getNextResult = function getNextResult(options) {\n    var _this3 = this;\n    return new Promise(function (resolve, reject) {\n      var unsubscribe = _this3.subscribe(function (result) {\n        if (!result.isFetching) {\n          unsubscribe();\n          if (result.isError && (options == null ? void 0 : options.throwOnError)) {\n            reject(result.error);\n          } else {\n            resolve(result);\n          }\n        }\n      });\n    });\n  };\n  _proto.getCurrentQuery = function getCurrentQuery() {\n    return this.currentQuery;\n  };\n  _proto.remove = function remove() {\n    this.client.getQueryCache().remove(this.currentQuery);\n  };\n  _proto.refetch = function refetch(options) {\n    return this.fetch(_extends({}, options, {\n      meta: {\n        refetchPage: options == null ? void 0 : options.refetchPage\n      }\n    }));\n  };\n  _proto.fetchOptimistic = function fetchOptimistic(options) {\n    var _this4 = this;\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    return query.fetch().then(function () {\n      return _this4.createResult(query, defaultedOptions);\n    });\n  };\n  _proto.fetch = function fetch(fetchOptions) {\n    var _this5 = this;\n    return this.executeFetch(fetchOptions).then(function () {\n      _this5.updateResult();\n      return _this5.currentResult;\n    });\n  };\n  _proto.executeFetch = function executeFetch(fetchOptions) {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery(); // Fetch\n\n    var promise = this.currentQuery.fetch(this.options, fetchOptions);\n    if (!(fetchOptions == null ? void 0 : fetchOptions.throwOnError)) {\n      promise = promise.catch(noop);\n    }\n    return promise;\n  };\n  _proto.updateStaleTimeout = function updateStaleTimeout() {\n    var _this6 = this;\n    this.clearStaleTimeout();\n    if (isServer || this.currentResult.isStale || !isValidTimeout(this.options.staleTime)) {\n      return;\n    }\n    var time = timeUntilStale(this.currentResult.dataUpdatedAt, this.options.staleTime); // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n\n    var timeout = time + 1;\n    this.staleTimeoutId = setTimeout(function () {\n      if (!_this6.currentResult.isStale) {\n        _this6.updateResult();\n      }\n    }, timeout);\n  };\n  _proto.computeRefetchInterval = function computeRefetchInterval() {\n    var _this$options$refetch;\n    return typeof this.options.refetchInterval === 'function' ? this.options.refetchInterval(this.currentResult.data, this.currentQuery) : (_this$options$refetch = this.options.refetchInterval) != null ? _this$options$refetch : false;\n  };\n  _proto.updateRefetchInterval = function updateRefetchInterval(nextInterval) {\n    var _this7 = this;\n    this.clearRefetchInterval();\n    this.currentRefetchInterval = nextInterval;\n    if (isServer || this.options.enabled === false || !isValidTimeout(this.currentRefetchInterval) || this.currentRefetchInterval === 0) {\n      return;\n    }\n    this.refetchIntervalId = setInterval(function () {\n      if (_this7.options.refetchIntervalInBackground || focusManager.isFocused()) {\n        _this7.executeFetch();\n      }\n    }, this.currentRefetchInterval);\n  };\n  _proto.updateTimers = function updateTimers() {\n    this.updateStaleTimeout();\n    this.updateRefetchInterval(this.computeRefetchInterval());\n  };\n  _proto.clearTimers = function clearTimers() {\n    this.clearStaleTimeout();\n    this.clearRefetchInterval();\n  };\n  _proto.clearStaleTimeout = function clearStaleTimeout() {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId);\n      this.staleTimeoutId = undefined;\n    }\n  };\n  _proto.clearRefetchInterval = function clearRefetchInterval() {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId);\n      this.refetchIntervalId = undefined;\n    }\n  };\n  _proto.createResult = function createResult(query, options) {\n    var prevQuery = this.currentQuery;\n    var prevOptions = this.options;\n    var prevResult = this.currentResult;\n    var prevResultState = this.currentResultState;\n    var prevResultOptions = this.currentResultOptions;\n    var queryChange = query !== prevQuery;\n    var queryInitialState = queryChange ? query.state : this.currentQueryInitialState;\n    var prevQueryResult = queryChange ? this.currentResult : this.previousQueryResult;\n    var state = query.state;\n    var dataUpdatedAt = state.dataUpdatedAt,\n      error = state.error,\n      errorUpdatedAt = state.errorUpdatedAt,\n      isFetching = state.isFetching,\n      status = state.status;\n    var isPreviousData = false;\n    var isPlaceholderData = false;\n    var data; // Optimistically set result in fetching state if needed\n\n    if (options.optimisticResults) {\n      var mounted = this.hasListeners();\n      var fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      var fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n      if (fetchOnMount || fetchOptionally) {\n        isFetching = true;\n        if (!dataUpdatedAt) {\n          status = 'loading';\n        }\n      }\n    } // Keep previous data if needed\n\n    if (options.keepPreviousData && !state.dataUpdateCount && (prevQueryResult == null ? void 0 : prevQueryResult.isSuccess) && status !== 'error') {\n      data = prevQueryResult.data;\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt;\n      status = prevQueryResult.status;\n      isPreviousData = true;\n    } // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n      // Memoize select result\n      if (prevResult && state.data === (prevResultState == null ? void 0 : prevResultState.data) && options.select === this.selectFn) {\n        data = this.selectResult;\n      } else {\n        try {\n          this.selectFn = options.select;\n          data = options.select(state.data);\n          if (options.structuralSharing !== false) {\n            data = replaceEqualDeep(prevResult == null ? void 0 : prevResult.data, data);\n          }\n          this.selectResult = data;\n          this.selectError = null;\n        } catch (selectError) {\n          getLogger().error(selectError);\n          this.selectError = selectError;\n        }\n      }\n    } // Use query data\n    else {\n      data = state.data;\n    } // Show placeholder data if needed\n\n    if (typeof options.placeholderData !== 'undefined' && typeof data === 'undefined' && (status === 'loading' || status === 'idle')) {\n      var placeholderData; // Memoize placeholder data\n\n      if ((prevResult == null ? void 0 : prevResult.isPlaceholderData) && options.placeholderData === (prevResultOptions == null ? void 0 : prevResultOptions.placeholderData)) {\n        placeholderData = prevResult.data;\n      } else {\n        placeholderData = typeof options.placeholderData === 'function' ? options.placeholderData() : options.placeholderData;\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData);\n            if (options.structuralSharing !== false) {\n              placeholderData = replaceEqualDeep(prevResult == null ? void 0 : prevResult.data, placeholderData);\n            }\n            this.selectError = null;\n          } catch (selectError) {\n            getLogger().error(selectError);\n            this.selectError = selectError;\n          }\n        }\n      }\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success';\n        data = placeholderData;\n        isPlaceholderData = true;\n      }\n    }\n    if (this.selectError) {\n      error = this.selectError;\n      data = this.selectResult;\n      errorUpdatedAt = Date.now();\n      status = 'error';\n    }\n    var result = {\n      status: status,\n      isLoading: status === 'loading',\n      isSuccess: status === 'success',\n      isError: status === 'error',\n      isIdle: status === 'idle',\n      data: data,\n      dataUpdatedAt: dataUpdatedAt,\n      error: error,\n      errorUpdatedAt: errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount: state.dataUpdateCount > queryInitialState.dataUpdateCount || state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching: isFetching,\n      isRefetching: isFetching && status !== 'loading',\n      isLoadingError: status === 'error' && state.dataUpdatedAt === 0,\n      isPlaceholderData: isPlaceholderData,\n      isPreviousData: isPreviousData,\n      isRefetchError: status === 'error' && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove\n    };\n    return result;\n  };\n  _proto.shouldNotifyListeners = function shouldNotifyListeners(result, prevResult) {\n    if (!prevResult) {\n      return true;\n    }\n    var _this$options = this.options,\n      notifyOnChangeProps = _this$options.notifyOnChangeProps,\n      notifyOnChangePropsExclusions = _this$options.notifyOnChangePropsExclusions;\n    if (!notifyOnChangeProps && !notifyOnChangePropsExclusions) {\n      return true;\n    }\n    if (notifyOnChangeProps === 'tracked' && !this.trackedProps.length) {\n      return true;\n    }\n    var includedProps = notifyOnChangeProps === 'tracked' ? this.trackedProps : notifyOnChangeProps;\n    return Object.keys(result).some(function (key) {\n      var typedKey = key;\n      var changed = result[typedKey] !== prevResult[typedKey];\n      var isIncluded = includedProps == null ? void 0 : includedProps.some(function (x) {\n        return x === key;\n      });\n      var isExcluded = notifyOnChangePropsExclusions == null ? void 0 : notifyOnChangePropsExclusions.some(function (x) {\n        return x === key;\n      });\n      return changed && !isExcluded && (!includedProps || isIncluded);\n    });\n  };\n  _proto.updateResult = function updateResult(notifyOptions) {\n    var prevResult = this.currentResult;\n    this.currentResult = this.createResult(this.currentQuery, this.options);\n    this.currentResultState = this.currentQuery.state;\n    this.currentResultOptions = this.options; // Only notify if something has changed\n\n    if (shallowEqualObjects(this.currentResult, prevResult)) {\n      return;\n    } // Determine which callbacks to trigger\n\n    var defaultNotifyOptions = {\n      cache: true\n    };\n    if ((notifyOptions == null ? void 0 : notifyOptions.listeners) !== false && this.shouldNotifyListeners(this.currentResult, prevResult)) {\n      defaultNotifyOptions.listeners = true;\n    }\n    this.notify(_extends({}, defaultNotifyOptions, notifyOptions));\n  };\n  _proto.updateQuery = function updateQuery() {\n    var query = this.client.getQueryCache().build(this.client, this.options);\n    if (query === this.currentQuery) {\n      return;\n    }\n    var prevQuery = this.currentQuery;\n    this.currentQuery = query;\n    this.currentQueryInitialState = query.state;\n    this.previousQueryResult = this.currentResult;\n    if (this.hasListeners()) {\n      prevQuery == null ? void 0 : prevQuery.removeObserver(this);\n      query.addObserver(this);\n    }\n  };\n  _proto.onQueryUpdate = function onQueryUpdate(action) {\n    var notifyOptions = {};\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true;\n    } else if (action.type === 'error' && !isCancelledError(action.error)) {\n      notifyOptions.onError = true;\n    }\n    this.updateResult(notifyOptions);\n    if (this.hasListeners()) {\n      this.updateTimers();\n    }\n  };\n  _proto.notify = function notify(notifyOptions) {\n    var _this8 = this;\n    notifyManager.batch(function () {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        _this8.options.onSuccess == null ? void 0 : _this8.options.onSuccess(_this8.currentResult.data);\n        _this8.options.onSettled == null ? void 0 : _this8.options.onSettled(_this8.currentResult.data, null);\n      } else if (notifyOptions.onError) {\n        _this8.options.onError == null ? void 0 : _this8.options.onError(_this8.currentResult.error);\n        _this8.options.onSettled == null ? void 0 : _this8.options.onSettled(undefined, _this8.currentResult.error);\n      } // Then trigger the listeners\n\n      if (notifyOptions.listeners) {\n        _this8.listeners.forEach(function (listener) {\n          listener(_this8.currentResult);\n        });\n      } // Then the cache listeners\n\n      if (notifyOptions.cache) {\n        _this8.client.getQueryCache().notify({\n          query: _this8.currentQuery,\n          type: 'observerResultsUpdated'\n        });\n      }\n    });\n  };\n  return QueryObserver;\n}(Subscribable);\nfunction shouldLoadOnMount(query, options) {\n  return options.enabled !== false && !query.state.dataUpdatedAt && !(query.state.status === 'error' && options.retryOnMount === false);\n}\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.dataUpdatedAt > 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\nfunction shouldFetchOn(query, options, field) {\n  if (options.enabled !== false) {\n    var value = typeof field === 'function' ? field(query) : field;\n    return value === 'always' || value !== false && isStale(query, options);\n  }\n  return false;\n}\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return options.enabled !== false && (query !== prevQuery || prevOptions.enabled === false) && (!options.suspense || query.state.status !== 'error') && isStale(query, options);\n}\nfunction isStale(query, options) {\n  return query.isStaleByTime(options.staleTime);\n}", "map": {"version": 3, "names": ["_extends", "_inherits<PERSON><PERSON>e", "isServer", "isValidTimeout", "noop", "replaceEqualDeep", "shallowEqualObjects", "timeUntilStale", "notify<PERSON><PERSON>ger", "focusManager", "Subscribable", "<PERSON><PERSON><PERSON><PERSON>", "isCancelledError", "QueryObserver", "_Subscribable", "client", "options", "_this", "call", "trackedProps", "selectError", "bindMethods", "setOptions", "_proto", "prototype", "remove", "bind", "refetch", "onSubscribe", "listeners", "length", "<PERSON><PERSON><PERSON><PERSON>", "addObserver", "shouldFetchOnMount", "executeFetch", "updateTimers", "onUnsubscribe", "destroy", "shouldFetchOnReconnect", "shouldFetchOn", "refetchOnReconnect", "shouldFetchOnWindowFocus", "refetchOnWindowFocus", "clearTimers", "removeObserver", "notifyOptions", "prevOptions", "prev<PERSON><PERSON><PERSON>", "defaultQueryObserverOptions", "enabled", "Error", "query<PERSON><PERSON>", "updateQuery", "mounted", "hasListeners", "shouldFetchOptionally", "updateResult", "staleTime", "updateStaleTimeout", "nextRefetchInterval", "computeRefetchInterval", "currentRefetchInterval", "updateRefetchInterval", "getOptimisticResult", "defaultedOptions", "query", "get<PERSON><PERSON><PERSON><PERSON>ache", "build", "createResult", "getCurrentResult", "currentResult", "trackResult", "result", "_this2", "trackedResult", "trackProp", "key", "includes", "push", "Object", "keys", "for<PERSON>ach", "defineProperty", "configurable", "enumerable", "get", "useErrorBoundary", "suspense", "getNextResult", "_this3", "Promise", "resolve", "reject", "unsubscribe", "subscribe", "isFetching", "isError", "throwOnError", "error", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fetch", "meta", "refetchPage", "fetchOptimistic", "_this4", "then", "fetchOptions", "_this5", "promise", "catch", "_this6", "clearStaleTimeout", "isStale", "time", "dataUpdatedAt", "timeout", "staleTimeoutId", "setTimeout", "_this$options$refetch", "refetchInterval", "data", "nextInterval", "_this7", "clearRefetchInterval", "refetchIntervalId", "setInterval", "refetchIntervalInBackground", "isFocused", "clearTimeout", "undefined", "clearInterval", "prevResult", "prevResultState", "currentResultState", "prevResultOptions", "currentResultOptions", "query<PERSON>hange", "queryInitialState", "state", "currentQueryInitialState", "prevQueryResult", "previousQueryResult", "errorUpdatedAt", "status", "isPreviousData", "isPlaceholderData", "optimisticResults", "fetchOnMount", "fetchOptionally", "keepPreviousData", "dataUpdateCount", "isSuccess", "select", "selectFn", "selectResult", "structuralSharing", "placeholderData", "Date", "now", "isLoading", "isIdle", "failureCount", "fetchFailureCount", "errorUpdateCount", "isFetched", "isFetchedAfterMount", "isRefetching", "isLoadingError", "isRefetchError", "shouldNotifyListeners", "_this$options", "notifyOnChangeProps", "notifyOnChangePropsExclusions", "includedProps", "some", "<PERSON><PERSON><PERSON>", "changed", "isIncluded", "x", "isExcluded", "defaultNotifyOptions", "cache", "notify", "onQueryUpdate", "action", "type", "onSuccess", "onError", "_this8", "batch", "onSettled", "listener", "shouldLoadOnMount", "retryOnMount", "refetchOnMount", "field", "value", "isStaleByTime"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/node_modules/react-query/es/core/queryObserver.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { isServer, isValidTimeout, noop, replaceEqualDeep, shallowEqualObjects, timeUntilStale } from './utils';\nimport { notifyManager } from './notifyManager';\nimport { focusManager } from './focusManager';\nimport { Subscribable } from './subscribable';\nimport { getLogger } from './logger';\nimport { isCancelledError } from './retryer';\nexport var QueryObserver = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(QueryObserver, _Subscribable);\n\n  function QueryObserver(client, options) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n    _this.options = options;\n    _this.trackedProps = [];\n    _this.selectError = null;\n\n    _this.bindMethods();\n\n    _this.setOptions(options);\n\n    return _this;\n  }\n\n  var _proto = QueryObserver.prototype;\n\n  _proto.bindMethods = function bindMethods() {\n    this.remove = this.remove.bind(this);\n    this.refetch = this.refetch.bind(this);\n  };\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (this.listeners.length === 1) {\n      this.currentQuery.addObserver(this);\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch();\n      }\n\n      this.updateTimers();\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      this.destroy();\n    }\n  };\n\n  _proto.shouldFetchOnReconnect = function shouldFetchOnReconnect() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnReconnect);\n  };\n\n  _proto.shouldFetchOnWindowFocus = function shouldFetchOnWindowFocus() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnWindowFocus);\n  };\n\n  _proto.destroy = function destroy() {\n    this.listeners = [];\n    this.clearTimers();\n    this.currentQuery.removeObserver(this);\n  };\n\n  _proto.setOptions = function setOptions(options, notifyOptions) {\n    var prevOptions = this.options;\n    var prevQuery = this.currentQuery;\n    this.options = this.client.defaultQueryObserverOptions(options);\n\n    if (typeof this.options.enabled !== 'undefined' && typeof this.options.enabled !== 'boolean') {\n      throw new Error('Expected enabled to be a boolean');\n    } // Keep previous query key if the user does not supply one\n\n\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey;\n    }\n\n    this.updateQuery();\n    var mounted = this.hasListeners(); // Fetch if there are subscribers\n\n    if (mounted && shouldFetchOptionally(this.currentQuery, prevQuery, this.options, prevOptions)) {\n      this.executeFetch();\n    } // Update result\n\n\n    this.updateResult(notifyOptions); // Update stale interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || this.options.staleTime !== prevOptions.staleTime)) {\n      this.updateStaleTimeout();\n    }\n\n    var nextRefetchInterval = this.computeRefetchInterval(); // Update refetch interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || nextRefetchInterval !== this.currentRefetchInterval)) {\n      this.updateRefetchInterval(nextRefetchInterval);\n    }\n  };\n\n  _proto.getOptimisticResult = function getOptimisticResult(options) {\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    return this.createResult(query, defaultedOptions);\n  };\n\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.currentResult;\n  };\n\n  _proto.trackResult = function trackResult(result, defaultedOptions) {\n    var _this2 = this;\n\n    var trackedResult = {};\n\n    var trackProp = function trackProp(key) {\n      if (!_this2.trackedProps.includes(key)) {\n        _this2.trackedProps.push(key);\n      }\n    };\n\n    Object.keys(result).forEach(function (key) {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: function get() {\n          trackProp(key);\n          return result[key];\n        }\n      });\n    });\n\n    if (defaultedOptions.useErrorBoundary || defaultedOptions.suspense) {\n      trackProp('error');\n    }\n\n    return trackedResult;\n  };\n\n  _proto.getNextResult = function getNextResult(options) {\n    var _this3 = this;\n\n    return new Promise(function (resolve, reject) {\n      var unsubscribe = _this3.subscribe(function (result) {\n        if (!result.isFetching) {\n          unsubscribe();\n\n          if (result.isError && (options == null ? void 0 : options.throwOnError)) {\n            reject(result.error);\n          } else {\n            resolve(result);\n          }\n        }\n      });\n    });\n  };\n\n  _proto.getCurrentQuery = function getCurrentQuery() {\n    return this.currentQuery;\n  };\n\n  _proto.remove = function remove() {\n    this.client.getQueryCache().remove(this.currentQuery);\n  };\n\n  _proto.refetch = function refetch(options) {\n    return this.fetch(_extends({}, options, {\n      meta: {\n        refetchPage: options == null ? void 0 : options.refetchPage\n      }\n    }));\n  };\n\n  _proto.fetchOptimistic = function fetchOptimistic(options) {\n    var _this4 = this;\n\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    return query.fetch().then(function () {\n      return _this4.createResult(query, defaultedOptions);\n    });\n  };\n\n  _proto.fetch = function fetch(fetchOptions) {\n    var _this5 = this;\n\n    return this.executeFetch(fetchOptions).then(function () {\n      _this5.updateResult();\n\n      return _this5.currentResult;\n    });\n  };\n\n  _proto.executeFetch = function executeFetch(fetchOptions) {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery(); // Fetch\n\n    var promise = this.currentQuery.fetch(this.options, fetchOptions);\n\n    if (!(fetchOptions == null ? void 0 : fetchOptions.throwOnError)) {\n      promise = promise.catch(noop);\n    }\n\n    return promise;\n  };\n\n  _proto.updateStaleTimeout = function updateStaleTimeout() {\n    var _this6 = this;\n\n    this.clearStaleTimeout();\n\n    if (isServer || this.currentResult.isStale || !isValidTimeout(this.options.staleTime)) {\n      return;\n    }\n\n    var time = timeUntilStale(this.currentResult.dataUpdatedAt, this.options.staleTime); // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n\n    var timeout = time + 1;\n    this.staleTimeoutId = setTimeout(function () {\n      if (!_this6.currentResult.isStale) {\n        _this6.updateResult();\n      }\n    }, timeout);\n  };\n\n  _proto.computeRefetchInterval = function computeRefetchInterval() {\n    var _this$options$refetch;\n\n    return typeof this.options.refetchInterval === 'function' ? this.options.refetchInterval(this.currentResult.data, this.currentQuery) : (_this$options$refetch = this.options.refetchInterval) != null ? _this$options$refetch : false;\n  };\n\n  _proto.updateRefetchInterval = function updateRefetchInterval(nextInterval) {\n    var _this7 = this;\n\n    this.clearRefetchInterval();\n    this.currentRefetchInterval = nextInterval;\n\n    if (isServer || this.options.enabled === false || !isValidTimeout(this.currentRefetchInterval) || this.currentRefetchInterval === 0) {\n      return;\n    }\n\n    this.refetchIntervalId = setInterval(function () {\n      if (_this7.options.refetchIntervalInBackground || focusManager.isFocused()) {\n        _this7.executeFetch();\n      }\n    }, this.currentRefetchInterval);\n  };\n\n  _proto.updateTimers = function updateTimers() {\n    this.updateStaleTimeout();\n    this.updateRefetchInterval(this.computeRefetchInterval());\n  };\n\n  _proto.clearTimers = function clearTimers() {\n    this.clearStaleTimeout();\n    this.clearRefetchInterval();\n  };\n\n  _proto.clearStaleTimeout = function clearStaleTimeout() {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId);\n      this.staleTimeoutId = undefined;\n    }\n  };\n\n  _proto.clearRefetchInterval = function clearRefetchInterval() {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId);\n      this.refetchIntervalId = undefined;\n    }\n  };\n\n  _proto.createResult = function createResult(query, options) {\n    var prevQuery = this.currentQuery;\n    var prevOptions = this.options;\n    var prevResult = this.currentResult;\n    var prevResultState = this.currentResultState;\n    var prevResultOptions = this.currentResultOptions;\n    var queryChange = query !== prevQuery;\n    var queryInitialState = queryChange ? query.state : this.currentQueryInitialState;\n    var prevQueryResult = queryChange ? this.currentResult : this.previousQueryResult;\n    var state = query.state;\n    var dataUpdatedAt = state.dataUpdatedAt,\n        error = state.error,\n        errorUpdatedAt = state.errorUpdatedAt,\n        isFetching = state.isFetching,\n        status = state.status;\n    var isPreviousData = false;\n    var isPlaceholderData = false;\n    var data; // Optimistically set result in fetching state if needed\n\n    if (options.optimisticResults) {\n      var mounted = this.hasListeners();\n      var fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      var fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n\n      if (fetchOnMount || fetchOptionally) {\n        isFetching = true;\n\n        if (!dataUpdatedAt) {\n          status = 'loading';\n        }\n      }\n    } // Keep previous data if needed\n\n\n    if (options.keepPreviousData && !state.dataUpdateCount && (prevQueryResult == null ? void 0 : prevQueryResult.isSuccess) && status !== 'error') {\n      data = prevQueryResult.data;\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt;\n      status = prevQueryResult.status;\n      isPreviousData = true;\n    } // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n        // Memoize select result\n        if (prevResult && state.data === (prevResultState == null ? void 0 : prevResultState.data) && options.select === this.selectFn) {\n          data = this.selectResult;\n        } else {\n          try {\n            this.selectFn = options.select;\n            data = options.select(state.data);\n\n            if (options.structuralSharing !== false) {\n              data = replaceEqualDeep(prevResult == null ? void 0 : prevResult.data, data);\n            }\n\n            this.selectResult = data;\n            this.selectError = null;\n          } catch (selectError) {\n            getLogger().error(selectError);\n            this.selectError = selectError;\n          }\n        }\n      } // Use query data\n      else {\n          data = state.data;\n        } // Show placeholder data if needed\n\n\n    if (typeof options.placeholderData !== 'undefined' && typeof data === 'undefined' && (status === 'loading' || status === 'idle')) {\n      var placeholderData; // Memoize placeholder data\n\n      if ((prevResult == null ? void 0 : prevResult.isPlaceholderData) && options.placeholderData === (prevResultOptions == null ? void 0 : prevResultOptions.placeholderData)) {\n        placeholderData = prevResult.data;\n      } else {\n        placeholderData = typeof options.placeholderData === 'function' ? options.placeholderData() : options.placeholderData;\n\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData);\n\n            if (options.structuralSharing !== false) {\n              placeholderData = replaceEqualDeep(prevResult == null ? void 0 : prevResult.data, placeholderData);\n            }\n\n            this.selectError = null;\n          } catch (selectError) {\n            getLogger().error(selectError);\n            this.selectError = selectError;\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success';\n        data = placeholderData;\n        isPlaceholderData = true;\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError;\n      data = this.selectResult;\n      errorUpdatedAt = Date.now();\n      status = 'error';\n    }\n\n    var result = {\n      status: status,\n      isLoading: status === 'loading',\n      isSuccess: status === 'success',\n      isError: status === 'error',\n      isIdle: status === 'idle',\n      data: data,\n      dataUpdatedAt: dataUpdatedAt,\n      error: error,\n      errorUpdatedAt: errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount: state.dataUpdateCount > queryInitialState.dataUpdateCount || state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching: isFetching,\n      isRefetching: isFetching && status !== 'loading',\n      isLoadingError: status === 'error' && state.dataUpdatedAt === 0,\n      isPlaceholderData: isPlaceholderData,\n      isPreviousData: isPreviousData,\n      isRefetchError: status === 'error' && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove\n    };\n    return result;\n  };\n\n  _proto.shouldNotifyListeners = function shouldNotifyListeners(result, prevResult) {\n    if (!prevResult) {\n      return true;\n    }\n\n    var _this$options = this.options,\n        notifyOnChangeProps = _this$options.notifyOnChangeProps,\n        notifyOnChangePropsExclusions = _this$options.notifyOnChangePropsExclusions;\n\n    if (!notifyOnChangeProps && !notifyOnChangePropsExclusions) {\n      return true;\n    }\n\n    if (notifyOnChangeProps === 'tracked' && !this.trackedProps.length) {\n      return true;\n    }\n\n    var includedProps = notifyOnChangeProps === 'tracked' ? this.trackedProps : notifyOnChangeProps;\n    return Object.keys(result).some(function (key) {\n      var typedKey = key;\n      var changed = result[typedKey] !== prevResult[typedKey];\n      var isIncluded = includedProps == null ? void 0 : includedProps.some(function (x) {\n        return x === key;\n      });\n      var isExcluded = notifyOnChangePropsExclusions == null ? void 0 : notifyOnChangePropsExclusions.some(function (x) {\n        return x === key;\n      });\n      return changed && !isExcluded && (!includedProps || isIncluded);\n    });\n  };\n\n  _proto.updateResult = function updateResult(notifyOptions) {\n    var prevResult = this.currentResult;\n    this.currentResult = this.createResult(this.currentQuery, this.options);\n    this.currentResultState = this.currentQuery.state;\n    this.currentResultOptions = this.options; // Only notify if something has changed\n\n    if (shallowEqualObjects(this.currentResult, prevResult)) {\n      return;\n    } // Determine which callbacks to trigger\n\n\n    var defaultNotifyOptions = {\n      cache: true\n    };\n\n    if ((notifyOptions == null ? void 0 : notifyOptions.listeners) !== false && this.shouldNotifyListeners(this.currentResult, prevResult)) {\n      defaultNotifyOptions.listeners = true;\n    }\n\n    this.notify(_extends({}, defaultNotifyOptions, notifyOptions));\n  };\n\n  _proto.updateQuery = function updateQuery() {\n    var query = this.client.getQueryCache().build(this.client, this.options);\n\n    if (query === this.currentQuery) {\n      return;\n    }\n\n    var prevQuery = this.currentQuery;\n    this.currentQuery = query;\n    this.currentQueryInitialState = query.state;\n    this.previousQueryResult = this.currentResult;\n\n    if (this.hasListeners()) {\n      prevQuery == null ? void 0 : prevQuery.removeObserver(this);\n      query.addObserver(this);\n    }\n  };\n\n  _proto.onQueryUpdate = function onQueryUpdate(action) {\n    var notifyOptions = {};\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true;\n    } else if (action.type === 'error' && !isCancelledError(action.error)) {\n      notifyOptions.onError = true;\n    }\n\n    this.updateResult(notifyOptions);\n\n    if (this.hasListeners()) {\n      this.updateTimers();\n    }\n  };\n\n  _proto.notify = function notify(notifyOptions) {\n    var _this8 = this;\n\n    notifyManager.batch(function () {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        _this8.options.onSuccess == null ? void 0 : _this8.options.onSuccess(_this8.currentResult.data);\n        _this8.options.onSettled == null ? void 0 : _this8.options.onSettled(_this8.currentResult.data, null);\n      } else if (notifyOptions.onError) {\n        _this8.options.onError == null ? void 0 : _this8.options.onError(_this8.currentResult.error);\n        _this8.options.onSettled == null ? void 0 : _this8.options.onSettled(undefined, _this8.currentResult.error);\n      } // Then trigger the listeners\n\n\n      if (notifyOptions.listeners) {\n        _this8.listeners.forEach(function (listener) {\n          listener(_this8.currentResult);\n        });\n      } // Then the cache listeners\n\n\n      if (notifyOptions.cache) {\n        _this8.client.getQueryCache().notify({\n          query: _this8.currentQuery,\n          type: 'observerResultsUpdated'\n        });\n      }\n    });\n  };\n\n  return QueryObserver;\n}(Subscribable);\n\nfunction shouldLoadOnMount(query, options) {\n  return options.enabled !== false && !query.state.dataUpdatedAt && !(query.state.status === 'error' && options.retryOnMount === false);\n}\n\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.dataUpdatedAt > 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\n\nfunction shouldFetchOn(query, options, field) {\n  if (options.enabled !== false) {\n    var value = typeof field === 'function' ? field(query) : field;\n    return value === 'always' || value !== false && isStale(query, options);\n  }\n\n  return false;\n}\n\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return options.enabled !== false && (query !== prevQuery || prevOptions.enabled === false) && (!options.suspense || query.state.status !== 'error') && isStale(query, options);\n}\n\nfunction isStale(query, options) {\n  return query.isStaleByTime(options.staleTime);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,QAAQ,EAAEC,cAAc,EAAEC,IAAI,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,cAAc,QAAQ,SAAS;AAC/G,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,SAAS,QAAQ,UAAU;AACpC,SAASC,gBAAgB,QAAQ,WAAW;AAC5C,OAAO,IAAIC,aAAa,GAAG,aAAa,UAAUC,aAAa,EAAE;EAC/Db,cAAc,CAACY,aAAa,EAAEC,aAAa,CAAC;EAE5C,SAASD,aAAaA,CAACE,MAAM,EAAEC,OAAO,EAAE;IACtC,IAAIC,KAAK;IAETA,KAAK,GAAGH,aAAa,CAACI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACxCD,KAAK,CAACF,MAAM,GAAGA,MAAM;IACrBE,KAAK,CAACD,OAAO,GAAGA,OAAO;IACvBC,KAAK,CAACE,YAAY,GAAG,EAAE;IACvBF,KAAK,CAACG,WAAW,GAAG,IAAI;IAExBH,KAAK,CAACI,WAAW,CAAC,CAAC;IAEnBJ,KAAK,CAACK,UAAU,CAACN,OAAO,CAAC;IAEzB,OAAOC,KAAK;EACd;EAEA,IAAIM,MAAM,GAAGV,aAAa,CAACW,SAAS;EAEpCD,MAAM,CAACF,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IAC1C,IAAI,CAACI,MAAM,GAAG,IAAI,CAACA,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACC,OAAO,GAAG,IAAI,CAACA,OAAO,CAACD,IAAI,CAAC,IAAI,CAAC;EACxC,CAAC;EAEDH,MAAM,CAACK,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IAC1C,IAAI,IAAI,CAACC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;MAC/B,IAAI,CAACC,YAAY,CAACC,WAAW,CAAC,IAAI,CAAC;MAEnC,IAAIC,kBAAkB,CAAC,IAAI,CAACF,YAAY,EAAE,IAAI,CAACf,OAAO,CAAC,EAAE;QACvD,IAAI,CAACkB,YAAY,CAAC,CAAC;MACrB;MAEA,IAAI,CAACC,YAAY,CAAC,CAAC;IACrB;EACF,CAAC;EAEDZ,MAAM,CAACa,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC9C,IAAI,CAAC,IAAI,CAACP,SAAS,CAACC,MAAM,EAAE;MAC1B,IAAI,CAACO,OAAO,CAAC,CAAC;IAChB;EACF,CAAC;EAEDd,MAAM,CAACe,sBAAsB,GAAG,SAASA,sBAAsBA,CAAA,EAAG;IAChE,OAAOC,aAAa,CAAC,IAAI,CAACR,YAAY,EAAE,IAAI,CAACf,OAAO,EAAE,IAAI,CAACA,OAAO,CAACwB,kBAAkB,CAAC;EACxF,CAAC;EAEDjB,MAAM,CAACkB,wBAAwB,GAAG,SAASA,wBAAwBA,CAAA,EAAG;IACpE,OAAOF,aAAa,CAAC,IAAI,CAACR,YAAY,EAAE,IAAI,CAACf,OAAO,EAAE,IAAI,CAACA,OAAO,CAAC0B,oBAAoB,CAAC;EAC1F,CAAC;EAEDnB,MAAM,CAACc,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAClC,IAAI,CAACR,SAAS,GAAG,EAAE;IACnB,IAAI,CAACc,WAAW,CAAC,CAAC;IAClB,IAAI,CAACZ,YAAY,CAACa,cAAc,CAAC,IAAI,CAAC;EACxC,CAAC;EAEDrB,MAAM,CAACD,UAAU,GAAG,SAASA,UAAUA,CAACN,OAAO,EAAE6B,aAAa,EAAE;IAC9D,IAAIC,WAAW,GAAG,IAAI,CAAC9B,OAAO;IAC9B,IAAI+B,SAAS,GAAG,IAAI,CAAChB,YAAY;IACjC,IAAI,CAACf,OAAO,GAAG,IAAI,CAACD,MAAM,CAACiC,2BAA2B,CAAChC,OAAO,CAAC;IAE/D,IAAI,OAAO,IAAI,CAACA,OAAO,CAACiC,OAAO,KAAK,WAAW,IAAI,OAAO,IAAI,CAACjC,OAAO,CAACiC,OAAO,KAAK,SAAS,EAAE;MAC5F,MAAM,IAAIC,KAAK,CAAC,kCAAkC,CAAC;IACrD,CAAC,CAAC;;IAGF,IAAI,CAAC,IAAI,CAAClC,OAAO,CAACmC,QAAQ,EAAE;MAC1B,IAAI,CAACnC,OAAO,CAACmC,QAAQ,GAAGL,WAAW,CAACK,QAAQ;IAC9C;IAEA,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAIC,OAAO,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC,CAAC;;IAEnC,IAAID,OAAO,IAAIE,qBAAqB,CAAC,IAAI,CAACxB,YAAY,EAAEgB,SAAS,EAAE,IAAI,CAAC/B,OAAO,EAAE8B,WAAW,CAAC,EAAE;MAC7F,IAAI,CAACZ,YAAY,CAAC,CAAC;IACrB,CAAC,CAAC;;IAGF,IAAI,CAACsB,YAAY,CAACX,aAAa,CAAC,CAAC,CAAC;;IAElC,IAAIQ,OAAO,KAAK,IAAI,CAACtB,YAAY,KAAKgB,SAAS,IAAI,IAAI,CAAC/B,OAAO,CAACiC,OAAO,KAAKH,WAAW,CAACG,OAAO,IAAI,IAAI,CAACjC,OAAO,CAACyC,SAAS,KAAKX,WAAW,CAACW,SAAS,CAAC,EAAE;MACpJ,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC3B;IAEA,IAAIC,mBAAmB,GAAG,IAAI,CAACC,sBAAsB,CAAC,CAAC,CAAC,CAAC;;IAEzD,IAAIP,OAAO,KAAK,IAAI,CAACtB,YAAY,KAAKgB,SAAS,IAAI,IAAI,CAAC/B,OAAO,CAACiC,OAAO,KAAKH,WAAW,CAACG,OAAO,IAAIU,mBAAmB,KAAK,IAAI,CAACE,sBAAsB,CAAC,EAAE;MACvJ,IAAI,CAACC,qBAAqB,CAACH,mBAAmB,CAAC;IACjD;EACF,CAAC;EAEDpC,MAAM,CAACwC,mBAAmB,GAAG,SAASA,mBAAmBA,CAAC/C,OAAO,EAAE;IACjE,IAAIgD,gBAAgB,GAAG,IAAI,CAACjD,MAAM,CAACiC,2BAA2B,CAAChC,OAAO,CAAC;IACvE,IAAIiD,KAAK,GAAG,IAAI,CAAClD,MAAM,CAACmD,aAAa,CAAC,CAAC,CAACC,KAAK,CAAC,IAAI,CAACpD,MAAM,EAAEiD,gBAAgB,CAAC;IAC5E,OAAO,IAAI,CAACI,YAAY,CAACH,KAAK,EAAED,gBAAgB,CAAC;EACnD,CAAC;EAEDzC,MAAM,CAAC8C,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACpD,OAAO,IAAI,CAACC,aAAa;EAC3B,CAAC;EAED/C,MAAM,CAACgD,WAAW,GAAG,SAASA,WAAWA,CAACC,MAAM,EAAER,gBAAgB,EAAE;IAClE,IAAIS,MAAM,GAAG,IAAI;IAEjB,IAAIC,aAAa,GAAG,CAAC,CAAC;IAEtB,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,GAAG,EAAE;MACtC,IAAI,CAACH,MAAM,CAACtD,YAAY,CAAC0D,QAAQ,CAACD,GAAG,CAAC,EAAE;QACtCH,MAAM,CAACtD,YAAY,CAAC2D,IAAI,CAACF,GAAG,CAAC;MAC/B;IACF,CAAC;IAEDG,MAAM,CAACC,IAAI,CAACR,MAAM,CAAC,CAACS,OAAO,CAAC,UAAUL,GAAG,EAAE;MACzCG,MAAM,CAACG,cAAc,CAACR,aAAa,EAAEE,GAAG,EAAE;QACxCO,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE,IAAI;QAChBC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;UAClBV,SAAS,CAACC,GAAG,CAAC;UACd,OAAOJ,MAAM,CAACI,GAAG,CAAC;QACpB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAIZ,gBAAgB,CAACsB,gBAAgB,IAAItB,gBAAgB,CAACuB,QAAQ,EAAE;MAClEZ,SAAS,CAAC,OAAO,CAAC;IACpB;IAEA,OAAOD,aAAa;EACtB,CAAC;EAEDnD,MAAM,CAACiE,aAAa,GAAG,SAASA,aAAaA,CAACxE,OAAO,EAAE;IACrD,IAAIyE,MAAM,GAAG,IAAI;IAEjB,OAAO,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;MAC5C,IAAIC,WAAW,GAAGJ,MAAM,CAACK,SAAS,CAAC,UAAUtB,MAAM,EAAE;QACnD,IAAI,CAACA,MAAM,CAACuB,UAAU,EAAE;UACtBF,WAAW,CAAC,CAAC;UAEb,IAAIrB,MAAM,CAACwB,OAAO,KAAKhF,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACiF,YAAY,CAAC,EAAE;YACvEL,MAAM,CAACpB,MAAM,CAAC0B,KAAK,CAAC;UACtB,CAAC,MAAM;YACLP,OAAO,CAACnB,MAAM,CAAC;UACjB;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAEDjD,MAAM,CAAC4E,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAClD,OAAO,IAAI,CAACpE,YAAY;EAC1B,CAAC;EAEDR,MAAM,CAACE,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAChC,IAAI,CAACV,MAAM,CAACmD,aAAa,CAAC,CAAC,CAACzC,MAAM,CAAC,IAAI,CAACM,YAAY,CAAC;EACvD,CAAC;EAEDR,MAAM,CAACI,OAAO,GAAG,SAASA,OAAOA,CAACX,OAAO,EAAE;IACzC,OAAO,IAAI,CAACoF,KAAK,CAACpG,QAAQ,CAAC,CAAC,CAAC,EAAEgB,OAAO,EAAE;MACtCqF,IAAI,EAAE;QACJC,WAAW,EAAEtF,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACsF;MAClD;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED/E,MAAM,CAACgF,eAAe,GAAG,SAASA,eAAeA,CAACvF,OAAO,EAAE;IACzD,IAAIwF,MAAM,GAAG,IAAI;IAEjB,IAAIxC,gBAAgB,GAAG,IAAI,CAACjD,MAAM,CAACiC,2BAA2B,CAAChC,OAAO,CAAC;IACvE,IAAIiD,KAAK,GAAG,IAAI,CAAClD,MAAM,CAACmD,aAAa,CAAC,CAAC,CAACC,KAAK,CAAC,IAAI,CAACpD,MAAM,EAAEiD,gBAAgB,CAAC;IAC5E,OAAOC,KAAK,CAACmC,KAAK,CAAC,CAAC,CAACK,IAAI,CAAC,YAAY;MACpC,OAAOD,MAAM,CAACpC,YAAY,CAACH,KAAK,EAAED,gBAAgB,CAAC;IACrD,CAAC,CAAC;EACJ,CAAC;EAEDzC,MAAM,CAAC6E,KAAK,GAAG,SAASA,KAAKA,CAACM,YAAY,EAAE;IAC1C,IAAIC,MAAM,GAAG,IAAI;IAEjB,OAAO,IAAI,CAACzE,YAAY,CAACwE,YAAY,CAAC,CAACD,IAAI,CAAC,YAAY;MACtDE,MAAM,CAACnD,YAAY,CAAC,CAAC;MAErB,OAAOmD,MAAM,CAACrC,aAAa;IAC7B,CAAC,CAAC;EACJ,CAAC;EAED/C,MAAM,CAACW,YAAY,GAAG,SAASA,YAAYA,CAACwE,YAAY,EAAE;IACxD;IACA,IAAI,CAACtD,WAAW,CAAC,CAAC,CAAC,CAAC;;IAEpB,IAAIwD,OAAO,GAAG,IAAI,CAAC7E,YAAY,CAACqE,KAAK,CAAC,IAAI,CAACpF,OAAO,EAAE0F,YAAY,CAAC;IAEjE,IAAI,EAAEA,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACT,YAAY,CAAC,EAAE;MAChEW,OAAO,GAAGA,OAAO,CAACC,KAAK,CAACzG,IAAI,CAAC;IAC/B;IAEA,OAAOwG,OAAO;EAChB,CAAC;EAEDrF,MAAM,CAACmC,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACxD,IAAIoD,MAAM,GAAG,IAAI;IAEjB,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAExB,IAAI7G,QAAQ,IAAI,IAAI,CAACoE,aAAa,CAAC0C,OAAO,IAAI,CAAC7G,cAAc,CAAC,IAAI,CAACa,OAAO,CAACyC,SAAS,CAAC,EAAE;MACrF;IACF;IAEA,IAAIwD,IAAI,GAAG1G,cAAc,CAAC,IAAI,CAAC+D,aAAa,CAAC4C,aAAa,EAAE,IAAI,CAAClG,OAAO,CAACyC,SAAS,CAAC,CAAC,CAAC;IACrF;;IAEA,IAAI0D,OAAO,GAAGF,IAAI,GAAG,CAAC;IACtB,IAAI,CAACG,cAAc,GAAGC,UAAU,CAAC,YAAY;MAC3C,IAAI,CAACP,MAAM,CAACxC,aAAa,CAAC0C,OAAO,EAAE;QACjCF,MAAM,CAACtD,YAAY,CAAC,CAAC;MACvB;IACF,CAAC,EAAE2D,OAAO,CAAC;EACb,CAAC;EAED5F,MAAM,CAACqC,sBAAsB,GAAG,SAASA,sBAAsBA,CAAA,EAAG;IAChE,IAAI0D,qBAAqB;IAEzB,OAAO,OAAO,IAAI,CAACtG,OAAO,CAACuG,eAAe,KAAK,UAAU,GAAG,IAAI,CAACvG,OAAO,CAACuG,eAAe,CAAC,IAAI,CAACjD,aAAa,CAACkD,IAAI,EAAE,IAAI,CAACzF,YAAY,CAAC,GAAG,CAACuF,qBAAqB,GAAG,IAAI,CAACtG,OAAO,CAACuG,eAAe,KAAK,IAAI,GAAGD,qBAAqB,GAAG,KAAK;EACvO,CAAC;EAED/F,MAAM,CAACuC,qBAAqB,GAAG,SAASA,qBAAqBA,CAAC2D,YAAY,EAAE;IAC1E,IAAIC,MAAM,GAAG,IAAI;IAEjB,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAC9D,sBAAsB,GAAG4D,YAAY;IAE1C,IAAIvH,QAAQ,IAAI,IAAI,CAACc,OAAO,CAACiC,OAAO,KAAK,KAAK,IAAI,CAAC9C,cAAc,CAAC,IAAI,CAAC0D,sBAAsB,CAAC,IAAI,IAAI,CAACA,sBAAsB,KAAK,CAAC,EAAE;MACnI;IACF;IAEA,IAAI,CAAC+D,iBAAiB,GAAGC,WAAW,CAAC,YAAY;MAC/C,IAAIH,MAAM,CAAC1G,OAAO,CAAC8G,2BAA2B,IAAIrH,YAAY,CAACsH,SAAS,CAAC,CAAC,EAAE;QAC1EL,MAAM,CAACxF,YAAY,CAAC,CAAC;MACvB;IACF,CAAC,EAAE,IAAI,CAAC2B,sBAAsB,CAAC;EACjC,CAAC;EAEDtC,MAAM,CAACY,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IAC5C,IAAI,CAACuB,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACI,qBAAqB,CAAC,IAAI,CAACF,sBAAsB,CAAC,CAAC,CAAC;EAC3D,CAAC;EAEDrC,MAAM,CAACoB,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IAC1C,IAAI,CAACoE,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACY,oBAAoB,CAAC,CAAC;EAC7B,CAAC;EAEDpG,MAAM,CAACwF,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACtD,IAAI,IAAI,CAACK,cAAc,EAAE;MACvBY,YAAY,CAAC,IAAI,CAACZ,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAGa,SAAS;IACjC;EACF,CAAC;EAED1G,MAAM,CAACoG,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;IAC5D,IAAI,IAAI,CAACC,iBAAiB,EAAE;MAC1BM,aAAa,CAAC,IAAI,CAACN,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAGK,SAAS;IACpC;EACF,CAAC;EAED1G,MAAM,CAAC6C,YAAY,GAAG,SAASA,YAAYA,CAACH,KAAK,EAAEjD,OAAO,EAAE;IAC1D,IAAI+B,SAAS,GAAG,IAAI,CAAChB,YAAY;IACjC,IAAIe,WAAW,GAAG,IAAI,CAAC9B,OAAO;IAC9B,IAAImH,UAAU,GAAG,IAAI,CAAC7D,aAAa;IACnC,IAAI8D,eAAe,GAAG,IAAI,CAACC,kBAAkB;IAC7C,IAAIC,iBAAiB,GAAG,IAAI,CAACC,oBAAoB;IACjD,IAAIC,WAAW,GAAGvE,KAAK,KAAKlB,SAAS;IACrC,IAAI0F,iBAAiB,GAAGD,WAAW,GAAGvE,KAAK,CAACyE,KAAK,GAAG,IAAI,CAACC,wBAAwB;IACjF,IAAIC,eAAe,GAAGJ,WAAW,GAAG,IAAI,CAAClE,aAAa,GAAG,IAAI,CAACuE,mBAAmB;IACjF,IAAIH,KAAK,GAAGzE,KAAK,CAACyE,KAAK;IACvB,IAAIxB,aAAa,GAAGwB,KAAK,CAACxB,aAAa;MACnChB,KAAK,GAAGwC,KAAK,CAACxC,KAAK;MACnB4C,cAAc,GAAGJ,KAAK,CAACI,cAAc;MACrC/C,UAAU,GAAG2C,KAAK,CAAC3C,UAAU;MAC7BgD,MAAM,GAAGL,KAAK,CAACK,MAAM;IACzB,IAAIC,cAAc,GAAG,KAAK;IAC1B,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIzB,IAAI,CAAC,CAAC;;IAEV,IAAIxG,OAAO,CAACkI,iBAAiB,EAAE;MAC7B,IAAI7F,OAAO,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;MACjC,IAAI6F,YAAY,GAAG,CAAC9F,OAAO,IAAIpB,kBAAkB,CAACgC,KAAK,EAAEjD,OAAO,CAAC;MACjE,IAAIoI,eAAe,GAAG/F,OAAO,IAAIE,qBAAqB,CAACU,KAAK,EAAElB,SAAS,EAAE/B,OAAO,EAAE8B,WAAW,CAAC;MAE9F,IAAIqG,YAAY,IAAIC,eAAe,EAAE;QACnCrD,UAAU,GAAG,IAAI;QAEjB,IAAI,CAACmB,aAAa,EAAE;UAClB6B,MAAM,GAAG,SAAS;QACpB;MACF;IACF,CAAC,CAAC;;IAGF,IAAI/H,OAAO,CAACqI,gBAAgB,IAAI,CAACX,KAAK,CAACY,eAAe,KAAKV,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACW,SAAS,CAAC,IAAIR,MAAM,KAAK,OAAO,EAAE;MAC9IvB,IAAI,GAAGoB,eAAe,CAACpB,IAAI;MAC3BN,aAAa,GAAG0B,eAAe,CAAC1B,aAAa;MAC7C6B,MAAM,GAAGH,eAAe,CAACG,MAAM;MAC/BC,cAAc,GAAG,IAAI;IACvB,CAAC,CAAC;IAAA,KACG,IAAIhI,OAAO,CAACwI,MAAM,IAAI,OAAOd,KAAK,CAAClB,IAAI,KAAK,WAAW,EAAE;MAC1D;MACA,IAAIW,UAAU,IAAIO,KAAK,CAAClB,IAAI,MAAMY,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACZ,IAAI,CAAC,IAAIxG,OAAO,CAACwI,MAAM,KAAK,IAAI,CAACC,QAAQ,EAAE;QAC9HjC,IAAI,GAAG,IAAI,CAACkC,YAAY;MAC1B,CAAC,MAAM;QACL,IAAI;UACF,IAAI,CAACD,QAAQ,GAAGzI,OAAO,CAACwI,MAAM;UAC9BhC,IAAI,GAAGxG,OAAO,CAACwI,MAAM,CAACd,KAAK,CAAClB,IAAI,CAAC;UAEjC,IAAIxG,OAAO,CAAC2I,iBAAiB,KAAK,KAAK,EAAE;YACvCnC,IAAI,GAAGnH,gBAAgB,CAAC8H,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACX,IAAI,EAAEA,IAAI,CAAC;UAC9E;UAEA,IAAI,CAACkC,YAAY,GAAGlC,IAAI;UACxB,IAAI,CAACpG,WAAW,GAAG,IAAI;QACzB,CAAC,CAAC,OAAOA,WAAW,EAAE;UACpBT,SAAS,CAAC,CAAC,CAACuF,KAAK,CAAC9E,WAAW,CAAC;UAC9B,IAAI,CAACA,WAAW,GAAGA,WAAW;QAChC;MACF;IACF,CAAC,CAAC;IAAA,KACG;MACDoG,IAAI,GAAGkB,KAAK,CAAClB,IAAI;IACnB,CAAC,CAAC;;IAGN,IAAI,OAAOxG,OAAO,CAAC4I,eAAe,KAAK,WAAW,IAAI,OAAOpC,IAAI,KAAK,WAAW,KAAKuB,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,MAAM,CAAC,EAAE;MAChI,IAAIa,eAAe,CAAC,CAAC;;MAErB,IAAI,CAACzB,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACc,iBAAiB,KAAKjI,OAAO,CAAC4I,eAAe,MAAMtB,iBAAiB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACsB,eAAe,CAAC,EAAE;QACxKA,eAAe,GAAGzB,UAAU,CAACX,IAAI;MACnC,CAAC,MAAM;QACLoC,eAAe,GAAG,OAAO5I,OAAO,CAAC4I,eAAe,KAAK,UAAU,GAAG5I,OAAO,CAAC4I,eAAe,CAAC,CAAC,GAAG5I,OAAO,CAAC4I,eAAe;QAErH,IAAI5I,OAAO,CAACwI,MAAM,IAAI,OAAOI,eAAe,KAAK,WAAW,EAAE;UAC5D,IAAI;YACFA,eAAe,GAAG5I,OAAO,CAACwI,MAAM,CAACI,eAAe,CAAC;YAEjD,IAAI5I,OAAO,CAAC2I,iBAAiB,KAAK,KAAK,EAAE;cACvCC,eAAe,GAAGvJ,gBAAgB,CAAC8H,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACX,IAAI,EAAEoC,eAAe,CAAC;YACpG;YAEA,IAAI,CAACxI,WAAW,GAAG,IAAI;UACzB,CAAC,CAAC,OAAOA,WAAW,EAAE;YACpBT,SAAS,CAAC,CAAC,CAACuF,KAAK,CAAC9E,WAAW,CAAC;YAC9B,IAAI,CAACA,WAAW,GAAGA,WAAW;UAChC;QACF;MACF;MAEA,IAAI,OAAOwI,eAAe,KAAK,WAAW,EAAE;QAC1Cb,MAAM,GAAG,SAAS;QAClBvB,IAAI,GAAGoC,eAAe;QACtBX,iBAAiB,GAAG,IAAI;MAC1B;IACF;IAEA,IAAI,IAAI,CAAC7H,WAAW,EAAE;MACpB8E,KAAK,GAAG,IAAI,CAAC9E,WAAW;MACxBoG,IAAI,GAAG,IAAI,CAACkC,YAAY;MACxBZ,cAAc,GAAGe,IAAI,CAACC,GAAG,CAAC,CAAC;MAC3Bf,MAAM,GAAG,OAAO;IAClB;IAEA,IAAIvE,MAAM,GAAG;MACXuE,MAAM,EAAEA,MAAM;MACdgB,SAAS,EAAEhB,MAAM,KAAK,SAAS;MAC/BQ,SAAS,EAAER,MAAM,KAAK,SAAS;MAC/B/C,OAAO,EAAE+C,MAAM,KAAK,OAAO;MAC3BiB,MAAM,EAAEjB,MAAM,KAAK,MAAM;MACzBvB,IAAI,EAAEA,IAAI;MACVN,aAAa,EAAEA,aAAa;MAC5BhB,KAAK,EAAEA,KAAK;MACZ4C,cAAc,EAAEA,cAAc;MAC9BmB,YAAY,EAAEvB,KAAK,CAACwB,iBAAiB;MACrCC,gBAAgB,EAAEzB,KAAK,CAACyB,gBAAgB;MACxCC,SAAS,EAAE1B,KAAK,CAACY,eAAe,GAAG,CAAC,IAAIZ,KAAK,CAACyB,gBAAgB,GAAG,CAAC;MAClEE,mBAAmB,EAAE3B,KAAK,CAACY,eAAe,GAAGb,iBAAiB,CAACa,eAAe,IAAIZ,KAAK,CAACyB,gBAAgB,GAAG1B,iBAAiB,CAAC0B,gBAAgB;MAC7IpE,UAAU,EAAEA,UAAU;MACtBuE,YAAY,EAAEvE,UAAU,IAAIgD,MAAM,KAAK,SAAS;MAChDwB,cAAc,EAAExB,MAAM,KAAK,OAAO,IAAIL,KAAK,CAACxB,aAAa,KAAK,CAAC;MAC/D+B,iBAAiB,EAAEA,iBAAiB;MACpCD,cAAc,EAAEA,cAAc;MAC9BwB,cAAc,EAAEzB,MAAM,KAAK,OAAO,IAAIL,KAAK,CAACxB,aAAa,KAAK,CAAC;MAC/DF,OAAO,EAAEA,OAAO,CAAC/C,KAAK,EAAEjD,OAAO,CAAC;MAChCW,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBF,MAAM,EAAE,IAAI,CAACA;IACf,CAAC;IACD,OAAO+C,MAAM;EACf,CAAC;EAEDjD,MAAM,CAACkJ,qBAAqB,GAAG,SAASA,qBAAqBA,CAACjG,MAAM,EAAE2D,UAAU,EAAE;IAChF,IAAI,CAACA,UAAU,EAAE;MACf,OAAO,IAAI;IACb;IAEA,IAAIuC,aAAa,GAAG,IAAI,CAAC1J,OAAO;MAC5B2J,mBAAmB,GAAGD,aAAa,CAACC,mBAAmB;MACvDC,6BAA6B,GAAGF,aAAa,CAACE,6BAA6B;IAE/E,IAAI,CAACD,mBAAmB,IAAI,CAACC,6BAA6B,EAAE;MAC1D,OAAO,IAAI;IACb;IAEA,IAAID,mBAAmB,KAAK,SAAS,IAAI,CAAC,IAAI,CAACxJ,YAAY,CAACW,MAAM,EAAE;MAClE,OAAO,IAAI;IACb;IAEA,IAAI+I,aAAa,GAAGF,mBAAmB,KAAK,SAAS,GAAG,IAAI,CAACxJ,YAAY,GAAGwJ,mBAAmB;IAC/F,OAAO5F,MAAM,CAACC,IAAI,CAACR,MAAM,CAAC,CAACsG,IAAI,CAAC,UAAUlG,GAAG,EAAE;MAC7C,IAAImG,QAAQ,GAAGnG,GAAG;MAClB,IAAIoG,OAAO,GAAGxG,MAAM,CAACuG,QAAQ,CAAC,KAAK5C,UAAU,CAAC4C,QAAQ,CAAC;MACvD,IAAIE,UAAU,GAAGJ,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACC,IAAI,CAAC,UAAUI,CAAC,EAAE;QAChF,OAAOA,CAAC,KAAKtG,GAAG;MAClB,CAAC,CAAC;MACF,IAAIuG,UAAU,GAAGP,6BAA6B,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,6BAA6B,CAACE,IAAI,CAAC,UAAUI,CAAC,EAAE;QAChH,OAAOA,CAAC,KAAKtG,GAAG;MAClB,CAAC,CAAC;MACF,OAAOoG,OAAO,IAAI,CAACG,UAAU,KAAK,CAACN,aAAa,IAAII,UAAU,CAAC;IACjE,CAAC,CAAC;EACJ,CAAC;EAED1J,MAAM,CAACiC,YAAY,GAAG,SAASA,YAAYA,CAACX,aAAa,EAAE;IACzD,IAAIsF,UAAU,GAAG,IAAI,CAAC7D,aAAa;IACnC,IAAI,CAACA,aAAa,GAAG,IAAI,CAACF,YAAY,CAAC,IAAI,CAACrC,YAAY,EAAE,IAAI,CAACf,OAAO,CAAC;IACvE,IAAI,CAACqH,kBAAkB,GAAG,IAAI,CAACtG,YAAY,CAAC2G,KAAK;IACjD,IAAI,CAACH,oBAAoB,GAAG,IAAI,CAACvH,OAAO,CAAC,CAAC;;IAE1C,IAAIV,mBAAmB,CAAC,IAAI,CAACgE,aAAa,EAAE6D,UAAU,CAAC,EAAE;MACvD;IACF,CAAC,CAAC;;IAGF,IAAIiD,oBAAoB,GAAG;MACzBC,KAAK,EAAE;IACT,CAAC;IAED,IAAI,CAACxI,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAChB,SAAS,MAAM,KAAK,IAAI,IAAI,CAAC4I,qBAAqB,CAAC,IAAI,CAACnG,aAAa,EAAE6D,UAAU,CAAC,EAAE;MACtIiD,oBAAoB,CAACvJ,SAAS,GAAG,IAAI;IACvC;IAEA,IAAI,CAACyJ,MAAM,CAACtL,QAAQ,CAAC,CAAC,CAAC,EAAEoL,oBAAoB,EAAEvI,aAAa,CAAC,CAAC;EAChE,CAAC;EAEDtB,MAAM,CAAC6B,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IAC1C,IAAIa,KAAK,GAAG,IAAI,CAAClD,MAAM,CAACmD,aAAa,CAAC,CAAC,CAACC,KAAK,CAAC,IAAI,CAACpD,MAAM,EAAE,IAAI,CAACC,OAAO,CAAC;IAExE,IAAIiD,KAAK,KAAK,IAAI,CAAClC,YAAY,EAAE;MAC/B;IACF;IAEA,IAAIgB,SAAS,GAAG,IAAI,CAAChB,YAAY;IACjC,IAAI,CAACA,YAAY,GAAGkC,KAAK;IACzB,IAAI,CAAC0E,wBAAwB,GAAG1E,KAAK,CAACyE,KAAK;IAC3C,IAAI,CAACG,mBAAmB,GAAG,IAAI,CAACvE,aAAa;IAE7C,IAAI,IAAI,CAAChB,YAAY,CAAC,CAAC,EAAE;MACvBP,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACH,cAAc,CAAC,IAAI,CAAC;MAC3DqB,KAAK,CAACjC,WAAW,CAAC,IAAI,CAAC;IACzB;EACF,CAAC;EAEDT,MAAM,CAACgK,aAAa,GAAG,SAASA,aAAaA,CAACC,MAAM,EAAE;IACpD,IAAI3I,aAAa,GAAG,CAAC,CAAC;IAEtB,IAAI2I,MAAM,CAACC,IAAI,KAAK,SAAS,EAAE;MAC7B5I,aAAa,CAAC6I,SAAS,GAAG,IAAI;IAChC,CAAC,MAAM,IAAIF,MAAM,CAACC,IAAI,KAAK,OAAO,IAAI,CAAC7K,gBAAgB,CAAC4K,MAAM,CAACtF,KAAK,CAAC,EAAE;MACrErD,aAAa,CAAC8I,OAAO,GAAG,IAAI;IAC9B;IAEA,IAAI,CAACnI,YAAY,CAACX,aAAa,CAAC;IAEhC,IAAI,IAAI,CAACS,YAAY,CAAC,CAAC,EAAE;MACvB,IAAI,CAACnB,YAAY,CAAC,CAAC;IACrB;EACF,CAAC;EAEDZ,MAAM,CAAC+J,MAAM,GAAG,SAASA,MAAMA,CAACzI,aAAa,EAAE;IAC7C,IAAI+I,MAAM,GAAG,IAAI;IAEjBpL,aAAa,CAACqL,KAAK,CAAC,YAAY;MAC9B;MACA,IAAIhJ,aAAa,CAAC6I,SAAS,EAAE;QAC3BE,MAAM,CAAC5K,OAAO,CAAC0K,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGE,MAAM,CAAC5K,OAAO,CAAC0K,SAAS,CAACE,MAAM,CAACtH,aAAa,CAACkD,IAAI,CAAC;QAC/FoE,MAAM,CAAC5K,OAAO,CAAC8K,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGF,MAAM,CAAC5K,OAAO,CAAC8K,SAAS,CAACF,MAAM,CAACtH,aAAa,CAACkD,IAAI,EAAE,IAAI,CAAC;MACvG,CAAC,MAAM,IAAI3E,aAAa,CAAC8I,OAAO,EAAE;QAChCC,MAAM,CAAC5K,OAAO,CAAC2K,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGC,MAAM,CAAC5K,OAAO,CAAC2K,OAAO,CAACC,MAAM,CAACtH,aAAa,CAAC4B,KAAK,CAAC;QAC5F0F,MAAM,CAAC5K,OAAO,CAAC8K,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGF,MAAM,CAAC5K,OAAO,CAAC8K,SAAS,CAAC7D,SAAS,EAAE2D,MAAM,CAACtH,aAAa,CAAC4B,KAAK,CAAC;MAC7G,CAAC,CAAC;;MAGF,IAAIrD,aAAa,CAAChB,SAAS,EAAE;QAC3B+J,MAAM,CAAC/J,SAAS,CAACoD,OAAO,CAAC,UAAU8G,QAAQ,EAAE;UAC3CA,QAAQ,CAACH,MAAM,CAACtH,aAAa,CAAC;QAChC,CAAC,CAAC;MACJ,CAAC,CAAC;;MAGF,IAAIzB,aAAa,CAACwI,KAAK,EAAE;QACvBO,MAAM,CAAC7K,MAAM,CAACmD,aAAa,CAAC,CAAC,CAACoH,MAAM,CAAC;UACnCrH,KAAK,EAAE2H,MAAM,CAAC7J,YAAY;UAC1B0J,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC;EAED,OAAO5K,aAAa;AACtB,CAAC,CAACH,YAAY,CAAC;AAEf,SAASsL,iBAAiBA,CAAC/H,KAAK,EAAEjD,OAAO,EAAE;EACzC,OAAOA,OAAO,CAACiC,OAAO,KAAK,KAAK,IAAI,CAACgB,KAAK,CAACyE,KAAK,CAACxB,aAAa,IAAI,EAAEjD,KAAK,CAACyE,KAAK,CAACK,MAAM,KAAK,OAAO,IAAI/H,OAAO,CAACiL,YAAY,KAAK,KAAK,CAAC;AACvI;AAEA,SAAShK,kBAAkBA,CAACgC,KAAK,EAAEjD,OAAO,EAAE;EAC1C,OAAOgL,iBAAiB,CAAC/H,KAAK,EAAEjD,OAAO,CAAC,IAAIiD,KAAK,CAACyE,KAAK,CAACxB,aAAa,GAAG,CAAC,IAAI3E,aAAa,CAAC0B,KAAK,EAAEjD,OAAO,EAAEA,OAAO,CAACkL,cAAc,CAAC;AACpI;AAEA,SAAS3J,aAAaA,CAAC0B,KAAK,EAAEjD,OAAO,EAAEmL,KAAK,EAAE;EAC5C,IAAInL,OAAO,CAACiC,OAAO,KAAK,KAAK,EAAE;IAC7B,IAAImJ,KAAK,GAAG,OAAOD,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAClI,KAAK,CAAC,GAAGkI,KAAK;IAC9D,OAAOC,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,KAAK,IAAIpF,OAAO,CAAC/C,KAAK,EAAEjD,OAAO,CAAC;EACzE;EAEA,OAAO,KAAK;AACd;AAEA,SAASuC,qBAAqBA,CAACU,KAAK,EAAElB,SAAS,EAAE/B,OAAO,EAAE8B,WAAW,EAAE;EACrE,OAAO9B,OAAO,CAACiC,OAAO,KAAK,KAAK,KAAKgB,KAAK,KAAKlB,SAAS,IAAID,WAAW,CAACG,OAAO,KAAK,KAAK,CAAC,KAAK,CAACjC,OAAO,CAACuE,QAAQ,IAAItB,KAAK,CAACyE,KAAK,CAACK,MAAM,KAAK,OAAO,CAAC,IAAI/B,OAAO,CAAC/C,KAAK,EAAEjD,OAAO,CAAC;AAChL;AAEA,SAASgG,OAAOA,CAAC/C,KAAK,EAAEjD,OAAO,EAAE;EAC/B,OAAOiD,KAAK,CAACoI,aAAa,CAACrL,OAAO,CAACyC,SAAS,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}