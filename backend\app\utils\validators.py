"""
데이터 검증 유틸리티
"""

from typing import Dict, List, Any, Optional
from app.models.vulnerability import VulnerabilityReport, AnalysisRequest
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

def validate_vulnerability_report(data: Dict[str, Any]) -> Dict[str, Any]:
    """취약점 보고서 데이터 검증"""
    errors = []
    warnings = []
    
    # 필수 필드 검증
    required_fields = ['title', 'description']
    for field in required_fields:
        if not data.get(field):
            errors.append(f"필수 필드가 누락되었습니다: {field}")
        elif not isinstance(data[field], str) or len(data[field].strip()) == 0:
            errors.append(f"필드가 비어있거나 유효하지 않습니다: {field}")
    
    # 제목 길이 검증
    if data.get('title') and len(data['title']) > 200:
        warnings.append("제목이 너무 깁니다 (200자 초과)")
    
    # 설명 길이 검증
    if data.get('description') and len(data['description']) > 10000:
        warnings.append("설명이 너무 깁니다 (10000자 초과)")
    
    # 심각도 검증
    valid_severities = ['critical', 'high', 'medium', 'low', 'info']
    if data.get('severity') and data['severity'] not in valid_severities:
        errors.append(f"유효하지 않은 심각도: {data['severity']}")
    
    # 태그 검증
    if data.get('tags'):
        if not isinstance(data['tags'], list):
            errors.append("태그는 리스트 형태여야 합니다")
        else:
            for tag in data['tags']:
                if not isinstance(tag, str):
                    errors.append("모든 태그는 문자열이어야 합니다")
                    break
    
    # 첨부 파일 검증
    if data.get('attachments'):
        if not isinstance(data['attachments'], list):
            errors.append("첨부 파일은 리스트 형태여야 합니다")
        else:
            for attachment in data['attachments']:
                if not isinstance(attachment, str):
                    errors.append("모든 첨부 파일 경로는 문자열이어야 합니다")
                    break
    
    return {
        'is_valid': len(errors) == 0,
        'errors': errors,
        'warnings': warnings
    }

def validate_analysis_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """분석 요청 데이터 검증"""
    errors = []
    warnings = []
    
    # 보고서 데이터 검증
    if not data.get('report'):
        errors.append("보고서 데이터가 필요합니다")
    else:
        report_validation = validate_vulnerability_report(data['report'])
        if not report_validation['is_valid']:
            errors.extend([f"보고서 {error}" for error in report_validation['errors']])
        warnings.extend([f"보고서 {warning}" for warning in report_validation['warnings']])
    
    # AI 모델 검증
    valid_models = ['gpt-4', 'gpt-3.5-turbo', 'llama2', 'custom']
    if data.get('ai_model') and data['ai_model'] not in valid_models:
        warnings.append(f"지원되지 않는 AI 모델일 수 있습니다: {data['ai_model']}")
    
    # 불린 필드 검증
    boolean_fields = ['include_reasoning', 'include_recommendations']
    for field in boolean_fields:
        if field in data and not isinstance(data[field], bool):
            errors.append(f"{field}는 불린 값이어야 합니다")
    
    return {
        'is_valid': len(errors) == 0,
        'errors': errors,
        'warnings': warnings
    }

def sanitize_text_input(text: str, max_length: Optional[int] = None) -> str:
    """텍스트 입력 정제"""
    if not isinstance(text, str):
        return ""
    
    # 기본 정제
    sanitized = text.strip()
    
    # 길이 제한
    if max_length and len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
        logger.warning(f"텍스트가 최대 길이로 잘렸습니다: {max_length}")
    
    return sanitized

def validate_file_upload(file_path: str, allowed_extensions: List[str], max_size: int) -> Dict[str, Any]:
    """파일 업로드 검증"""
    errors = []
    
    # 파일 존재 확인
    import os
    if not os.path.exists(file_path):
        errors.append("파일이 존재하지 않습니다")
        return {'is_valid': False, 'errors': errors}
    
    # 확장자 검증
    file_extension = os.path.splitext(file_path)[1].lower()
    if file_extension not in allowed_extensions:
        errors.append(f"허용되지 않는 파일 형식입니다: {file_extension}")
    
    # 파일 크기 검증
    file_size = os.path.getsize(file_path)
    if file_size > max_size:
        errors.append(f"파일 크기가 너무 큽니다: {file_size} bytes (최대: {max_size} bytes)")
    
    return {
        'is_valid': len(errors) == 0,
        'errors': errors,
        'file_size': file_size,
        'file_extension': file_extension
    }
