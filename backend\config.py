"""
애플리케이션 설정 관리
"""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field

class Settings(BaseSettings):
    """애플리케이션 설정 클래스"""
    
    # 기본 설정
    app_name: str = Field(default="Bugcrowd AI Vulnerability Validator", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    secret_key: str = Field(default="your-secret-key-here", env="SECRET_KEY")
    
    # 서버 설정
    backend_host: str = Field(default="localhost", env="BACKEND_HOST")
    backend_port: int = Field(default=8000, env="BACKEND_PORT")
    frontend_host: str = Field(default="localhost", env="FRONTEND_HOST")
    frontend_port: int = Field(default=3000, env="FRONTEND_PORT")
    
    # 데이터베이스 설정
    database_url: str = Field(default="sqlite:///./vulnerability_db.db", env="DATABASE_URL")
    
    # AI 모델 설정
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-4", env="OPENAI_MODEL")
    
    # Ollama 설정
    ollama_host: str = Field(default="http://localhost:11434", env="OLLAMA_HOST")
    ollama_model: str = Field(default="llama2", env="OLLAMA_MODEL")
    
    # 커스텀 AI API 설정
    custom_ai_api_url: Optional[str] = Field(default=None, env="CUSTOM_AI_API_URL")
    custom_ai_api_key: Optional[str] = Field(default=None, env="CUSTOM_AI_API_KEY")
    
    # 로깅 설정
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="logs/app.log", env="LOG_FILE")
    
    # CORS 설정
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        env="ALLOWED_ORIGINS"
    )
    
    # 파일 업로드 설정
    max_file_size: int = Field(default=10485760, env="MAX_FILE_SIZE")  # 10MB
    upload_dir: str = Field(default="uploads/", env="UPLOAD_DIR")
    
    # 캐시 설정
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    
    # 보안 설정
    jwt_secret_key: str = Field(default="your-jwt-secret-key", env="JWT_SECRET_KEY")
    jwt_algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # 취약점 분류 파일 경로
    taxonomy_file_path: str = Field(default="data/vulnerability-rating-taxonomy.json", env="TAXONOMY_FILE_PATH")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

# 전역 설정 인스턴스
settings = Settings()

def get_settings() -> Settings:
    """설정 인스턴스 반환"""
    return settings
