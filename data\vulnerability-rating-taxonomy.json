{"metadata": {"release_date": "2025-06-23T00:00:00+00:00"}, "content": [{"id": "ai_application_security", "name": "AI Application Security", "type": "category", "children": [{"id": "adversarial_example_injection", "name": "Adversarial Example Injection", "type": "subcategory", "children": [{"id": "ai_misclassification_attacks", "name": "AI Misclassification Attacks", "type": "variant", "priority": 4}]}, {"id": "ai_safety", "name": "AI Safety", "type": "subcategory", "children": [{"id": "misinformation_wrong_factual_data", "name": "Misinformation / Wrong Factual Data", "type": "variant", "priority": 4}]}, {"id": "denial_of_service_dos", "name": "Denial-of-Service (DoS)", "type": "subcategory", "children": [{"id": "application_wide", "name": "Application-Wide", "type": "variant", "priority": 2}, {"id": "tenant_scoped", "name": "Tenant<PERSON><PERSON><PERSON>", "type": "variant", "priority": 4}]}, {"id": "improper_input_handling", "name": "Improper Input Handling", "type": "subcategory", "children": [{"id": "ansi_escape_codes", "name": "ANSI Escape Codes", "type": "variant", "priority": 5}, {"id": "rtl_overrides", "name": "RTL Overrides", "type": "variant", "priority": 5}, {"id": "unicode_confusables", "name": "Unicode Confusables", "type": "variant", "priority": 5}]}, {"id": "improper_output_handling", "name": "Improper Output Handling", "type": "subcategory", "children": [{"id": "cross_site_scripting_xss", "name": "Cross-Site Scripting (XSS)", "type": "variant", "priority": 3}, {"id": "markdown_html_injection", "name": "Markdown/HTML Injection", "type": "variant", "priority": 4}]}, {"id": "insufficient_rate_limiting", "name": "Insufficient Rate Limiting", "type": "subcategory", "children": [{"id": "query_flooding_api_token_abuse", "name": "Query Flooding / API Token Abuse", "type": "variant", "priority": 4}]}, {"id": "model_extraction", "name": "Model Extraction", "type": "subcategory", "children": [{"id": "api_query_based_model_reconstruction", "name": "API Query-Based Model Reconstruction", "type": "variant", "priority": 1}]}, {"id": "prompt_injection", "name": "Prompt Injection", "type": "subcategory", "children": [{"id": "system_prompt_leakage", "name": "System Prompt Leakage", "type": "variant", "priority": 2}]}, {"id": "remote_code_execution", "name": "Remote Code Execution", "type": "subcategory", "children": [{"id": "full_system_compromise", "name": "Full System Compromise", "type": "variant", "priority": 1}, {"id": "sandboxed_container_code_execution", "name": "Sandboxed Container Code Execution", "type": "variant", "priority": 2}]}, {"id": "sensitive_information_disclosure", "name": "Sensitive Information Disclosure", "type": "subcategory", "children": [{"id": "cross_tenant_pii_leakage_exposure", "name": "Cross-Tenant PII Leakage/Exposure", "type": "variant", "priority": 1}, {"id": "key_leak", "name": "Key Leak", "type": "variant", "priority": 1}]}, {"id": "training_data_poisoning", "name": "Training Data Poisoning", "type": "subcategory", "children": [{"id": "backdoor_injection_bias_manipulation", "name": "Backdoor Injection / Bias Manipulation", "type": "variant", "priority": 1}]}, {"id": "vector_and_embedding_weaknesses", "name": "Vector and Embedding Weaknesses", "type": "subcategory", "children": [{"id": "embedding_exfiltration_model_extraction", "name": "Embedding Exfiltration / Model Extraction", "type": "variant", "priority": 2}, {"id": "semantic_indexing", "name": "Semantic Indexing", "type": "variant", "priority": 3}]}]}, {"id": "algorithmic_biases", "name": "Algorithmic Biases", "type": "category", "children": [{"id": "aggregation_bias", "name": "Aggregation Bias", "type": "subcategory", "priority": null}, {"id": "processing_bias", "name": "Processing Bias", "type": "subcategory", "priority": null}]}, {"id": "application_level_denial_of_service_dos", "name": "Application-Level Denial-of-Service (DoS)", "type": "category", "children": [{"id": "app_crash", "name": "App Crash", "type": "subcategory", "children": [{"id": "malformed_android_intents", "name": "Malformed Android Intents", "type": "variant", "priority": 5}, {"id": "malformed_ios_url_schemes", "name": "Malformed iOS URL Schemes", "type": "variant", "priority": 5}]}, {"id": "critical_impact_and_or_easy_difficulty", "name": "Critical Impact and/or Easy Difficulty", "type": "subcategory", "priority": 2}, {"id": "excessive_resource_consumption", "name": "Excessive Resource Consumption", "type": "subcategory", "children": [{"id": "injection_prompt", "name": "Injection (Prompt)", "type": "variant", "priority": null}]}, {"id": "high_impact_and_or_medium_difficulty", "name": "High Impact and/or Medium Difficulty", "type": "subcategory", "priority": 3}]}, {"id": "automotive_security_misconfiguration", "name": "Automotive Security Misconfiguration", "type": "category", "children": [{"id": "abs", "name": "Automatic Braking System (ABS)", "type": "subcategory", "children": [{"id": "unintended_acceleration_brake", "name": "Unintended Acceleration / Brake", "type": "variant", "priority": 3}]}, {"id": "battery_management_system", "name": "Battery Management System", "type": "subcategory", "children": [{"id": "firmware_dump", "name": "Firmware Dump", "type": "variant", "priority": 3}, {"id": "fraudulent_interface", "name": "Fraudulent Interface", "type": "variant", "priority": 4}]}, {"id": "can", "name": "CAN", "type": "subcategory", "children": [{"id": "injection_basic_safety_message", "name": "Injection (Basic Safety Message)", "type": "variant", "priority": 3}, {"id": "injection_battery_management_system", "name": "Injection (Battery Management System)", "type": "variant", "priority": 3}, {"id": "injection_disallowed_messages", "name": "Injection (Disallowed Messages)", "type": "variant", "priority": 4}, {"id": "injection_dos", "name": "Injection (DoS)", "type": "variant", "priority": 4}, {"id": "injection_headlights", "name": "Injection (Headlights)", "type": "variant", "priority": 3}, {"id": "injection_powertrain", "name": "Injection (Powertrain)", "type": "variant", "priority": 3}, {"id": "injection_pyrotechnical_device_deployment_tool", "name": "Injection (Pyrotechnical Device Deployment Tool)", "type": "variant", "priority": 3}, {"id": "injection_sensors", "name": "Injection (Sensors)", "type": "variant", "priority": 3}, {"id": "injection_steering_control", "name": "Injection (Steering Control)", "type": "variant", "priority": 3}, {"id": "injection_vehicle_anti_theft_systems", "name": "Injection (Vehicle Anti-theft Systems)", "type": "variant", "priority": 3}]}, {"id": "gnss_gps", "name": "GNSS / GPS", "type": "subcategory", "children": [{"id": "spoofing", "name": "Spoofing", "type": "variant", "priority": 4}]}, {"id": "immobilizer", "name": "Immobilizer", "type": "subcategory", "children": [{"id": "engine_start", "name": "Engine Start", "type": "variant", "priority": 3}]}, {"id": "infotainment_radio_head_unit", "name": "Infotainment, Radio Head Unit", "type": "subcategory", "children": [{"id": "code_execution_can_bus_pivot", "name": "Code Execution (CAN Bus Pivot)", "type": "variant", "priority": 2}, {"id": "code_execution_no_can_bus_pivot", "name": "Code Execution (No CAN Bus Pivot)", "type": "variant", "priority": 3}, {"id": "default_credentials", "name": "Default Credentials", "type": "variant", "priority": 4}, {"id": "dos_brick", "name": "Denial of Service (DoS / Brick)", "type": "variant", "priority": 4}, {"id": "ota_firmware_manipulation", "name": "OTA Firmware Manipulation", "type": "variant", "priority": 2}, {"id": "sensitive_data_leakage_exposure", "name": "Sensitive data Leakage/Exposure", "type": "variant", "priority": 1}, {"id": "source_code_dump", "name": "Source Code Dump", "type": "variant", "priority": 4}, {"id": "unauthorized_access_to_services", "name": "Unauthorized Access to Services (API / Endpoints)", "type": "variant", "priority": 3}]}, {"id": "rf_hub", "name": "RF Hub", "type": "subcategory", "children": [{"id": "can_injection_interaction", "name": "CAN Injection / Interaction", "type": "variant", "priority": 2}, {"id": "data_leakage_pull_encryption_mechanism", "name": "Data Leakage / Pull Encryption Mechanism", "type": "variant", "priority": 3}, {"id": "key_fob_cloning", "name": "Key Fob Cloning", "type": "variant", "priority": 1}, {"id": "relay", "name": "<PERSON><PERSON>", "type": "variant", "priority": 5}, {"id": "replay", "name": "Replay", "type": "variant", "priority": 5}, {"id": "roll_jam", "name": "Roll Jam", "type": "variant", "priority": 5}, {"id": "unauthorized_access_turn_on", "name": "Unauthorized Access / Turn On", "type": "variant", "priority": 4}]}, {"id": "rsu", "name": "Roadside Unit (RSU)", "type": "subcategory", "children": [{"id": "sybil_attack", "name": "<PERSON><PERSON><PERSON>", "type": "variant", "priority": 4}]}]}, {"id": "blockchain_infrastructure_misconfiguration", "name": "Blockchain Infrastructure Misconfiguration", "type": "category", "children": [{"id": "improper_bridge_validation_and_verification_logic", "name": "Improper Bridge Validation and Verification Logic", "type": "subcategory", "priority": null}]}, {"id": "broken_access_control", "name": "Broken Access Control (BAC)", "type": "category", "children": [{"id": "bypass_of_password_confirmation", "name": "Bypass of Password Confirmation", "type": "subcategory", "children": [{"id": "change_password", "name": "Change Password", "type": "variant", "priority": 4}]}, {"id": "exposed_sensitive_android_intent", "name": "Exposed Sensitive Android Intent", "type": "subcategory", "priority": null}, {"id": "exposed_sensitive_ios_url_scheme", "name": "Exposed Sensitive iOS URL Scheme", "type": "subcategory", "priority": null}, {"id": "idor", "name": "Insecure Direct Object References (IDOR)", "type": "subcategory", "children": [{"id": "modify_sensitive_information_iterable_object_identifiers", "name": "Modify Sensitive Information(Iterable Object Identifiers)", "type": "variant", "priority": 2}, {"id": "modify_view_sensitive_information_guid", "name": "Modify/View Sensitive Information(Complex Object Identifiers GUID/UUID)", "type": "variant", "priority": 4}, {"id": "modify_view_sensitive_information_iterable_object_identifiers", "name": "Modify/View Sensitive Information(Iterable Object Identifiers)", "type": "variant", "priority": 1}, {"id": "view_non_sensitive_information", "name": "View Non-Sensitive Information", "type": "variant", "priority": 5}, {"id": "view_sensitive_information_iterable_object_identifiers", "name": "View Sensitive Information(Iterable Object Identifiers)", "type": "variant", "priority": 3}]}, {"id": "privilege_escalation", "name": "Privilege Escalation", "type": "subcategory", "priority": null}, {"id": "username_enumeration", "name": "Username/Email Enumeration", "type": "subcategory", "children": [{"id": "non_brute_force", "name": "Non-Brute Force", "type": "variant", "priority": 4}]}]}, {"id": "broken_authentication_and_session_management", "name": "Broken Authentication and Session Management", "type": "category", "children": [{"id": "authentication_bypass", "name": "Authentication Bypass", "type": "subcategory", "priority": 1}, {"id": "cleartext_transmission_of_session_token", "name": "Cleartext Transmission of Session Token", "type": "subcategory", "priority": 4}, {"id": "concurrent_logins", "name": "Con<PERSON>", "type": "subcategory", "priority": 5}, {"id": "failure_to_invalidate_session", "name": "Failure to Invalidate Session", "type": "subcategory", "children": [{"id": "all_sessions", "name": "Concurrent Sessions On Logout", "type": "variant", "priority": 5}, {"id": "long_timeout", "name": "Long Timeout", "type": "variant", "priority": 5}, {"id": "on_email_change", "name": "On Email Change", "type": "variant", "priority": 5}, {"id": "on_logout", "name": "On Logout (Client and Server-Side)", "type": "variant", "priority": 4}, {"id": "on_logout_server_side_only", "name": "On Logout (Server-Side Only)", "type": "variant", "priority": 5}, {"id": "on_password_change", "name": "On Password Reset and/or Change", "type": "variant", "priority": 4}, {"id": "on_two_fa_activation_change", "name": "On 2FA Activation/Change", "type": "variant", "priority": 5}, {"id": "permission_change", "name": "On Permission Change", "type": "variant", "priority": null}]}, {"id": "saml_replay", "name": "SAML Replay", "type": "subcategory", "priority": 5}, {"id": "session_fixation", "name": "Session Fixation", "type": "subcategory", "children": [{"id": "local_attack_vector", "name": "Local Attack Vector", "type": "variant", "priority": 5}, {"id": "remote_attack_vector", "name": "Remote Attack Vector", "type": "variant", "priority": 3}]}, {"id": "two_fa_bypass", "name": "Second Factor Authentication (2FA) Bypass", "type": "subcategory", "priority": 3}, {"id": "weak_login_function", "name": "Weak Login Function", "type": "subcategory", "children": [{"id": "not_operational", "name": "Not Operational or Intended Public Access", "type": "variant", "priority": 5}, {"id": "other_plaintext_protocol_no_secure_alternative", "name": "Other Plaintext Protocol with no Secure Alternative", "type": "variant", "priority": 4}, {"id": "over_http", "name": "Over HTTP", "type": "variant", "priority": 4}]}, {"id": "weak_registration_implementation", "name": "Weak Registration Implementation", "type": "subcategory", "children": [{"id": "over_http", "name": "Over HTTP", "type": "variant", "priority": 4}]}]}, {"id": "client_side_injection", "name": "Client-Side Injection", "type": "category", "children": [{"id": "binary_planting", "name": "Binary Planting", "type": "subcategory", "children": [{"id": "no_privilege_escalation", "name": "No Privilege Escalation", "type": "variant", "priority": 5}, {"id": "non_default_folder_privilege_escalation", "name": "Non-Default Folder Privilege Escalation", "type": "variant", "priority": 5}, {"id": "privilege_escalation", "name": "Default Folder Privilege Escalation", "type": "variant", "priority": 3}]}]}, {"id": "cross_site_request_forgery_csrf", "name": "Cross-Site Request Forgery (CSRF)", "type": "category", "children": [{"id": "action_specific", "name": "Action-Specific", "type": "subcategory", "children": [{"id": "authenticated_action", "name": "Authenticated Action", "type": "variant", "priority": null}, {"id": "logout", "name": "Logout", "type": "variant", "priority": 5}, {"id": "unauthenticated_action", "name": "Unauthenticated Action", "type": "variant", "priority": null}]}, {"id": "application_wide", "name": "Application-Wide", "type": "subcategory", "priority": 2}, {"id": "csrf_token_not_unique_per_request", "name": "CSRF Token Not Unique Per Request", "type": "subcategory", "priority": 5}, {"id": "flash_based", "name": "Flash-Based", "type": "subcategory", "priority": 5}]}, {"id": "cross_site_scripting_xss", "name": "Cross-Site Scripting (XSS)", "type": "category", "children": [{"id": "cookie_based", "name": "Cookie-Based", "type": "subcategory", "priority": 5}, {"id": "flash_based", "name": "Flash-Based", "type": "subcategory", "priority": 5}, {"id": "ie_only", "name": "IE-Only", "type": "subcategory", "priority": 5}, {"id": "off_domain", "name": "Off-Domain", "type": "subcategory", "children": [{"id": "data_uri", "name": "Data URI", "type": "variant", "priority": 4}]}, {"id": "referer", "name": "<PERSON><PERSON><PERSON>", "type": "subcategory", "priority": 4}, {"id": "reflected", "name": "Reflected", "type": "subcategory", "children": [{"id": "non_self", "name": "Non-Self", "type": "variant", "priority": 3}, {"id": "self", "name": "Self", "type": "variant", "priority": 5}]}, {"id": "stored", "name": "Stored", "type": "subcategory", "children": [{"id": "non_admin_to_anyone", "name": "Non-Privileged User to Anyone", "type": "variant", "priority": 2}, {"id": "privileged_user_to_no_privilege_elevation", "name": "Privileged User to No Privilege Elevation", "type": "variant", "priority": 4}, {"id": "privileged_user_to_privilege_elevation", "name": "Privileged User to Privilege Elevation", "type": "variant", "priority": 3}, {"id": "self", "name": "Self", "type": "variant", "priority": 5}, {"id": "url_based", "name": "CSRF/URL-Based", "type": "variant", "priority": 3}]}, {"id": "trace_method", "name": "TRACE Method", "type": "subcategory", "priority": 5}, {"id": "universal_uxss", "name": "Universal (UXSS)", "type": "subcategory", "priority": 4}]}, {"id": "cryptographic_weakness", "name": "Cryptographic Weakness", "type": "category", "children": [{"id": "broken_cryptography", "name": "Broken Cryptography", "type": "subcategory", "children": [{"id": "use_of_broken_cryptographic_primitive", "name": "Use of Broken Cryptographic Primitive", "type": "variant", "priority": 3}, {"id": "use_of_vulnerable_cryptographic_library", "name": "Use of Vulnerable Cryptographic Library", "type": "variant", "priority": 4}]}, {"id": "incomplete_cleanup_of_keying_material", "name": "Incomplete Cleanup of Keying Material", "type": "subcategory", "priority": 5}, {"id": "insecure_implementation", "name": "Insecure Implementation", "type": "subcategory", "children": [{"id": "improper_following_of_specification", "name": "Improper Following of Specification (Other)", "type": "variant", "priority": null}, {"id": "missing_cryptographic_step", "name": "Missing Cryptographic Step", "type": "variant", "priority": null}]}, {"id": "insecure_key_generation", "name": "Insecure Key Generation", "type": "subcategory", "children": [{"id": "improper_asymmetric_exponent_selection", "name": "Improper Asymmetric Exponent Selection", "type": "variant", "priority": null}, {"id": "improper_asymmetric_prime_selection", "name": "Improper Asymmetric Prime Selection", "type": "variant", "priority": null}, {"id": "insufficient_key_space", "name": "Insufficient Key Space", "type": "variant", "priority": 3}, {"id": "insufficient_key_stretching", "name": "Insufficient Key Stretching", "type": "variant", "priority": null}, {"id": "key_exchange_without_entity_authentication", "name": "Key Exchage Without Entity Authentication", "type": "variant", "priority": 4}]}, {"id": "insufficient_entropy", "name": "Insufficient Entropy", "type": "subcategory", "children": [{"id": "initialization_vector_reuse", "name": "Initialization Vector (IV) Reuse", "type": "variant", "priority": 5}, {"id": "limited_rng_entropy_source", "name": "Limited Random Number Generator (RNG) Entropy Source", "type": "variant", "priority": 4}, {"id": "predictable_initialization_vector", "name": "Predictable Initialization Vector (IV)", "type": "variant", "priority": 4}, {"id": "predictable_prng_seed", "name": "Predictable Pseudo-Random Number Generator (PRNG) Seed", "type": "variant", "priority": 4}, {"id": "prng_seed_reuse", "name": "Pseudo-Random Number Generator (PRNG) Seed Reuse", "type": "variant", "priority": 5}, {"id": "small_seed_space_in_prng", "name": "Small Seed Space in Pseudo-Random Number Generator (PRNG)", "type": "variant", "priority": 4}, {"id": "use_of_trng_for_nonsecurity_purpose", "name": "Use of True Random Number Generator (TRNG) for Non-Security Purpose", "type": "variant", "priority": 5}]}, {"id": "insufficient_verification_of_data_authenticity", "name": "Insufficient Verification of Data Authenticity", "type": "subcategory", "children": [{"id": "cryptographic_signature", "name": "Cryptographic Signature", "type": "variant", "priority": null}, {"id": "identity_check_value", "name": "Integrity Check Value (ICV)", "type": "variant", "priority": 4}]}, {"id": "key_reuse", "name": "Key Reuse", "type": "subcategory", "children": [{"id": "inter_environment", "name": "Inter-Environment", "type": "variant", "priority": 2}, {"id": "intra_environment", "name": "Intra-Environment", "type": "variant", "priority": 5}, {"id": "lack_of_perfect_forward_secrecy", "name": "Lack of Perfect Forward Secrecy", "type": "variant", "priority": 4}]}, {"id": "side_channel_attack", "name": "Side-Channel Attack", "type": "subcategory", "children": [{"id": "differential_fault_analysis", "name": "Differential Fault Analysis", "type": "variant", "priority": null}, {"id": "emanations_attack", "name": "Emanations Attack", "type": "variant", "priority": 5}, {"id": "padding_oracle_attack", "name": "Padding Oracle Attack", "type": "variant", "priority": 4}, {"id": "power_analysis_attack", "name": "Power Analysis Attack", "type": "variant", "priority": 5}, {"id": "timing_attack", "name": "Timing Attack", "type": "variant", "priority": 4}]}, {"id": "use_of_expired_cryptographic_key_or_cert", "name": "Use of Expired Cryptographic Key (or Certificate)", "type": "subcategory", "priority": 4}, {"id": "weak_hash", "name": "Weak Hash", "type": "subcategory", "children": [{"id": "lack_of_salt", "name": "Lack of Salt", "type": "variant", "priority": null}, {"id": "predictable_hash_collision", "name": "Predictable <PERSON><PERSON> Collis<PERSON>", "type": "variant", "priority": null}, {"id": "use_of_predictable_salt", "name": "Use of Predictable Salt", "type": "variant", "priority": 5}]}]}, {"id": "data_biases", "name": "Data Biases", "type": "category", "children": [{"id": "pre_existing_bias", "name": "Pre-existing Bias", "type": "subcategory", "priority": null}, {"id": "representation_bias", "name": "Representation Bias", "type": "subcategory", "priority": null}]}, {"id": "decentralized_application_misconfiguration", "name": "Decentralized Application Misconfiguration", "type": "category", "children": [{"id": "defi_security", "name": "DeFi Security", "type": "subcategory", "children": [{"id": "flash_loan_attack", "name": "<PERSON> Attack", "type": "variant", "priority": null}, {"id": "function_level_accounting_error", "name": "Function-Level Accounting Error", "type": "variant", "priority": null}, {"id": "improper_implementation_of_governance", "name": "Improper Implementation of Governance", "type": "variant", "priority": null}, {"id": "pricing_oracle_manipulation", "name": "Pricing Oracle Manipulation", "type": "variant", "priority": null}]}, {"id": "improper_authorization", "name": "Improper Authorization", "type": "subcategory", "children": [{"id": "insufficient_signature_validation", "name": "Insufficient Signature Validation", "type": "variant", "priority": null}]}, {"id": "insecure_data_storage", "name": "Insecure Data Storage", "type": "subcategory", "children": [{"id": "plaintext_private_key", "name": "Plaintext Private Key", "type": "variant", "priority": 1}, {"id": "sensitive_information_exposure", "name": "Sensitive Information Exposure", "type": "variant", "priority": null}]}, {"id": "marketplace_security", "name": "Marketplace Security", "type": "subcategory", "children": [{"id": "denial_of_service", "name": "Denial of Service", "type": "variant", "priority": null}, {"id": "improper_validation_and_checks_for_deposits_and_withdrawals", "name": "Improper Validation and Checks For Deposits and Withdrawals", "type": "variant", "priority": null}, {"id": "malicious_order_offer", "name": "Malicious Order Offer", "type": "variant", "priority": 2}, {"id": "miscalculated_accounting_logic", "name": "Miscalculated Accounting Logic", "type": "variant", "priority": null}, {"id": "ofac_bypass", "name": "OFAC Bypass", "type": "variant", "priority": 3}, {"id": "orderbook_manipulation", "name": "Orderbook Manipulation", "type": "variant", "priority": 1}, {"id": "price_or_fee_manipulation", "name": "Price or Fee Manipulation", "type": "variant", "priority": 2}, {"id": "signer_account_takeover", "name": "Signer Account Takeover", "type": "variant", "priority": 1}, {"id": "unauthorized_asset_transfer", "name": "Unauthorized Asset Transfer", "type": "variant", "priority": 1}]}, {"id": "protocol_security_misconfiguration", "name": "Protocol Security Misconfiguration", "type": "subcategory", "children": [{"id": "node_level_denial_of_service", "name": "Node-level Denial of Service", "type": "variant", "priority": 1}]}]}, {"id": "developer_biases", "name": "Developer Biases", "type": "category", "children": [{"id": "implicit_bias", "name": "Implicit <PERSON><PERSON>", "type": "subcategory", "priority": null}]}, {"id": "external_behavior", "name": "External Behavior", "type": "category", "children": [{"id": "browser_feature", "name": "Browser Feature", "type": "subcategory", "children": [{"id": "aggressive_offline_caching", "name": "Aggressive Offline Caching", "type": "variant", "priority": 5}, {"id": "autocomplete_enabled", "name": "Autocomplete Enabled", "type": "variant", "priority": 5}, {"id": "autocorrect_enabled", "name": "Autocorrect Enabled", "type": "variant", "priority": 5}, {"id": "plaintext_password_field", "name": "Plaintext Password Field", "type": "variant", "priority": 5}, {"id": "save_password", "name": "Save Password", "type": "variant", "priority": 5}]}, {"id": "captcha_bypass", "name": "Captcha Bypass", "type": "subcategory", "children": [{"id": "crowdsourcing", "name": "Crowdsourcing", "type": "variant", "priority": 5}]}, {"id": "csv_injection", "name": "CSV Injection", "type": "subcategory", "priority": 5}, {"id": "system_clipboard_leak", "name": "System Clipboard Leak", "type": "subcategory", "children": [{"id": "shared_links", "name": "Shared Links", "type": "variant", "priority": 5}]}, {"id": "user_password_persisted_in_memory", "name": "User Password Persisted in Memory", "type": "subcategory", "priority": 5}]}, {"id": "indicators_of_compromise", "name": "Indicators of Compromise", "type": "category", "priority": null}, {"id": "insecure_data_storage", "name": "Insecure Data Storage", "type": "category", "children": [{"id": "non_sensitive_application_data_stored_unencrypted", "name": "Non-Sensitive Application Data Stored Unencrypted", "type": "subcategory", "priority": 5}, {"id": "screen_caching_enabled", "name": "Screen Caching Enabled", "type": "subcategory", "priority": 5}, {"id": "sensitive_application_data_stored_unencrypted", "name": "Sensitive Application Data Stored Unencrypted", "type": "subcategory", "children": [{"id": "on_external_storage", "name": "On External Storage", "type": "variant", "priority": 4}, {"id": "on_internal_storage", "name": "On Internal Storage", "type": "variant", "priority": 5}]}, {"id": "server_side_credentials_storage", "name": "Server-Side Credentials Storage", "type": "subcategory", "children": [{"id": "plaintext", "name": "Plaintext", "type": "variant", "priority": 4}]}]}, {"id": "insecure_data_transport", "name": "Insecure Data Transport", "type": "category", "children": [{"id": "cleartext_transmission_of_sensitive_data", "name": "Cleartext Transmission of Sensitive Data", "type": "subcategory", "priority": null}, {"id": "executable_download", "name": "Executable Download", "type": "subcategory", "children": [{"id": "no_secure_integrity_check", "name": "No Secure Integrity Check", "type": "variant", "priority": 4}, {"id": "secure_integrity_check", "name": "Secure Integrity Check", "type": "variant", "priority": 5}]}]}, {"id": "insecure_os_firmware", "name": "Insecure OS/Firmware", "type": "category", "children": [{"id": "command_injection", "name": "Command Injection", "type": "subcategory", "priority": 1}, {"id": "data_not_encrypted_at_rest", "name": "Data not encrypted at rest", "type": "subcategory", "children": [{"id": "non_sensitive", "name": "Non sensitive", "type": "variant", "priority": 5}, {"id": "sensitive", "name": "Sensitive", "type": "variant", "priority": null}]}, {"id": "failure_to_remove_sensitive_artifacts_from_disk", "name": "Failure to Remove Sensitive Artifacts from Disk", "type": "subcategory", "priority": null}, {"id": "hardcoded_password", "name": "Hardcoded Password", "type": "subcategory", "children": [{"id": "non_privileged_user", "name": "Non-Privileged User", "type": "variant", "priority": 2}, {"id": "privileged_user", "name": "Privileged User", "type": "variant", "priority": 1}]}, {"id": "kiosk_escape_or_breakout", "name": "Kiosk Escape or Breakout", "type": "subcategory", "priority": null}, {"id": "local_administrator_on_default_environment", "name": "Local Administrator on default environment", "type": "subcategory", "priority": 2}, {"id": "over_permissioned_credentials_on_storage", "name": "Over-Permissioned Credentials on Storage", "type": "subcategory", "priority": 2}, {"id": "poorly_configured_disk_encryption", "name": "Poorly Configured Disk Encryption", "type": "subcategory", "priority": null}, {"id": "poorly_configured_operating_system_security", "name": "Poorly Configured Operating System Security", "type": "subcategory", "priority": null}, {"id": "recovery_of_disk_contains_sensitive_material", "name": "Recovery of Disk Contains Sensitive Material", "type": "subcategory", "priority": null}, {"id": "shared_credentials_on_storage", "name": "Shared Credentials on Storage", "type": "subcategory", "priority": 3}, {"id": "weakness_in_firmware_updates", "name": "Weakness in Firmware Updates", "type": "subcategory", "children": [{"id": "firmware_cannot_be_updated", "name": "Firmware cannot be updated", "type": "variant", "priority": null}, {"id": "firmware_does_not_validate_update_integrity", "name": "Firmware does not validate update integrity", "type": "variant", "priority": 3}, {"id": "firmware_is_not_encrypted", "name": "Firmware is not encrypted", "type": "variant", "priority": 5}]}]}, {"id": "insufficient_security_configurability", "name": "Insufficient Security Configurability", "type": "category", "children": [{"id": "lack_of_notification_email", "name": "Lack of Notification Email", "type": "subcategory", "priority": 5}, {"id": "no_password_policy", "name": "No Password Policy", "type": "subcategory", "priority": 4}, {"id": "password_policy_bypass", "name": "Password Policy Bypass", "type": "subcategory", "priority": 5}, {"id": "verification_of_contact_method_not_required", "name": "Verification of Contact Method not Required", "type": "subcategory", "priority": 5}, {"id": "weak_password_policy", "name": "Weak Password Policy", "type": "subcategory", "priority": 5}, {"id": "weak_password_reset_implementation", "name": "Weak Password Reset Implementation", "type": "subcategory", "children": [{"id": "token_has_long_timed_expiry", "name": "<PERSON><PERSON> Timed Expiry", "type": "variant", "priority": 5}, {"id": "token_is_not_invalidated_after_email_change", "name": "Token is Not Invalidated After Email Change", "type": "variant", "priority": 5}, {"id": "token_is_not_invalidated_after_login", "name": "Token is Not Invalidated After Login", "type": "variant", "priority": 5}, {"id": "token_is_not_invalidated_after_new_token_is_requested", "name": "Token is Not Invalidated After New Token is Requested", "type": "variant", "priority": 5}, {"id": "token_is_not_invalidated_after_password_change", "name": "Token is Not Invalidated After Password Change", "type": "variant", "priority": 5}, {"id": "token_is_not_invalidated_after_use", "name": "Token is Not Invalidated After Use", "type": "variant", "priority": 4}]}, {"id": "weak_registration_implementation", "name": "Weak Registration Implementation", "type": "subcategory", "children": [{"id": "allows_disposable_email_addresses", "name": "Allows Disposable Email Addresses", "type": "variant", "priority": 5}]}, {"id": "weak_two_fa_implementation", "name": "Weak 2FA Implementation", "type": "subcategory", "children": [{"id": "missing_failsafe", "name": "Missing Failsafe", "type": "variant", "priority": 5}, {"id": "old_two_fa_code_is_not_invalidated_after_new_code_is_generated", "name": "Old 2FA Code is Not Invalidated After New Code is Generated", "type": "variant", "priority": 5}, {"id": "two_fa_code_is_not_updated_after_new_code_is_requested", "name": "2FA Code is Not Updated After New Code is Requested", "type": "variant", "priority": 5}, {"id": "two_fa_secret_cannot_be_rotated", "name": "2FA Secret Cannot be Rotated", "type": "variant", "priority": 4}, {"id": "two_fa_secret_remains_obtainable_after_two_fa_is_enabled", "name": "2FA Secret Remains Obtainable After 2FA is Enabled", "type": "variant", "priority": 4}]}]}, {"id": "lack_of_binary_hardening", "name": "Lack of Binary Hardening", "type": "category", "children": [{"id": "lack_of_exploit_mitigations", "name": "Lack of Exploit Mitigations", "type": "subcategory", "priority": 5}, {"id": "lack_of_jailbreak_detection", "name": "Lack of Jailbreak Detection", "type": "subcategory", "priority": 5}, {"id": "lack_of_obfuscation", "name": "Lack of Obfuscation", "type": "subcategory", "priority": 5}, {"id": "runtime_instrumentation_based", "name": "Runtime Instrumentation-Based", "type": "subcategory", "priority": 5}]}, {"id": "misinterpretation_biases", "name": "Misinterpretation Biases", "type": "category", "children": [{"id": "context_ignorance", "name": "Context Ignorance", "type": "subcategory", "priority": null}]}, {"id": "mobile_security_misconfiguration", "name": "Mobile Security Misconfiguration", "type": "category", "children": [{"id": "auto_backup_allowed_by_default", "name": "Auto Backup Allowed by <PERSON><PERSON><PERSON>", "type": "subcategory", "priority": 5}, {"id": "clipboard_enabled", "name": "Clipboard Enabled", "type": "subcategory", "priority": 5}, {"id": "ssl_certificate_pinning", "name": "SSL Certificate Pinning", "type": "subcategory", "children": [{"id": "absent", "name": "Absent", "type": "variant", "priority": 5}, {"id": "defeatable", "name": "Defeatable", "type": "variant", "priority": 5}]}, {"id": "tapjacking", "name": "Tapjacking", "type": "subcategory", "priority": 5}]}, {"id": "network_security_misconfiguration", "name": "Network Security Misconfiguration", "type": "category", "children": [{"id": "telnet_enabled", "name": "Telnet Enabled", "type": "subcategory", "priority": 5}]}, {"id": "physical_security_issues", "name": "Physical Security Issues", "type": "category", "children": [{"id": "bypass_of_physical_access_control", "name": "Bypass of physical access control", "type": "subcategory", "priority": null}, {"id": "weakness_in_physical_access_control", "name": "Weakness in physical access control", "type": "subcategory", "children": [{"id": "cloneable_key", "name": "Cloneable Key", "type": "variant", "priority": null}, {"id": "commonly_keyed_system", "name": "Commonly Keyed System", "type": "variant", "priority": 2}, {"id": "master_key_identification", "name": "Master Key Identification", "type": "variant", "priority": null}]}]}, {"id": "privacy_concerns", "name": "Privacy Concerns", "type": "category", "children": [{"id": "unnecessary_data_collection", "name": "Unnecessary Data Collection", "type": "subcategory", "children": [{"id": "wifi_ssid_password", "name": "WiFi SSID+Password", "type": "variant", "priority": 4}]}]}, {"id": "protocol_specific_misconfiguration", "name": "Protocol Specific Misconfiguration", "type": "category", "children": [{"id": "frontrunning_enabled_attack", "name": "Frontrunning-Enabled Attack", "type": "subcategory", "priority": 2}, {"id": "improper_validation_and_finalization_logic", "name": "Improper Validation and Finalization Logic", "type": "subcategory", "priority": null}, {"id": "misconfigured_staking_logic", "name": "Misconfigured Staking Logic", "type": "subcategory", "priority": null}, {"id": "sandwich_enabled_attack", "name": "Sandwich-Enabled Attack", "type": "subcategory", "priority": 2}]}, {"id": "sensitive_data_exposure", "name": "Sensitive Data Exposure", "type": "category", "children": [{"id": "disclosure_of_known_public_information", "name": "Disclosure of Known Public Information", "type": "subcategory", "priority": 5}, {"id": "disclosure_of_secrets", "name": "Disclosure of Secrets", "type": "subcategory", "children": [{"id": "data_traffic_spam", "name": "Data/Traffic Spam", "type": "variant", "priority": 5}, {"id": "for_internal_asset", "name": "For Internal Asset", "type": "variant", "priority": 3}, {"id": "for_publicly_accessible_asset", "name": "For Publicly Accessible Asset", "type": "variant", "priority": 1}, {"id": "intentionally_public_sample_or_invalid", "name": "Intentionally Public, Sample or Invalid", "type": "variant", "priority": 5}, {"id": "non_corporate_user", "name": "Non-Corporate User", "type": "variant", "priority": 5}, {"id": "pay_per_use_abuse", "name": "Pay-Per-Use Abuse", "type": "variant", "priority": 4}, {"id": "pii_leakage_exposure", "name": "PII Leakage/Exposure", "type": "variant", "priority": null}]}, {"id": "exif_geolocation_data_not_stripped_from_uploaded_images", "name": "EXIF Geolocation Data Not Stripped From Uploaded Images", "type": "subcategory", "children": [{"id": "automatic_user_enumeration", "name": "Automatic User Enumeration", "type": "variant", "priority": 3}, {"id": "manual_user_enumeration", "name": "Manual User Enumeration", "type": "variant", "priority": 4}]}, {"id": "graphql_introspection_enabled", "name": "GraphQL Introspection Enabled", "type": "subcategory", "priority": 5}, {"id": "internal_ip_disclosure", "name": "Internal IP Disclosure", "type": "subcategory", "priority": 5}, {"id": "json_hijacking", "name": "JSON Hijacking", "type": "subcategory", "priority": 5}, {"id": "mixed_content", "name": "Mixed Content (HTTPS Sourcing HTTP)", "type": "subcategory", "priority": 5}, {"id": "non_sensitive_token_in_url", "name": "Non-Sensitive Token in URL", "type": "subcategory", "priority": 5}, {"id": "sensitive_data_hardcoded", "name": "Sensitive Data Hardcoded", "type": "subcategory", "children": [{"id": "file_paths", "name": "File Paths", "type": "variant", "priority": 5}, {"id": "oauth_secret", "name": "OAuth Secret", "type": "variant", "priority": 5}]}, {"id": "sensitive_token_in_url", "name": "Sensitive Token in URL", "type": "subcategory", "children": [{"id": "in_the_background", "name": "In the Background", "type": "variant", "priority": 5}, {"id": "on_password_reset", "name": "On Password Reset", "type": "variant", "priority": 5}, {"id": "user_facing", "name": "User Facing", "type": "variant", "priority": 4}]}, {"id": "token_leakage_via_referer", "name": "Token Leakage via Referer", "type": "subcategory", "children": [{"id": "over_http", "name": "Over HTTP", "type": "variant", "priority": 4}, {"id": "password_reset_token", "name": "Password Reset Token", "type": "variant", "priority": 5}, {"id": "trusted_third_party", "name": "Trusted 3rd Party", "type": "variant", "priority": 5}, {"id": "untrusted_third_party", "name": "Untrusted 3rd Party", "type": "variant", "priority": 4}]}, {"id": "via_localstorage_sessionstorage", "name": "Via localStorage/sessionStorage", "type": "subcategory", "children": [{"id": "non_sensitive_token", "name": "Non-Sensitive Token", "type": "variant", "priority": 5}, {"id": "sensitive_token", "name": "Sensitive Token", "type": "variant", "priority": 4}]}, {"id": "visible_detailed_error_page", "name": "Visible Detailed Error/Debug Page", "type": "subcategory", "children": [{"id": "descriptive_stack_trace", "name": "Descriptive Stack Trace", "type": "variant", "priority": 5}, {"id": "detailed_server_configuration", "name": "Detailed Server Configuration", "type": "variant", "priority": 4}, {"id": "full_path_disclosure", "name": "Full Path Disclosure", "type": "variant", "priority": 5}]}, {"id": "weak_password_reset_implementation", "name": "Weak Password Reset Implementation", "type": "subcategory", "children": [{"id": "password_reset_token_sent_over_http", "name": "Password Reset Token Sent Over HTTP", "type": "variant", "priority": 4}, {"id": "token_leakage_via_host_header_poisoning", "name": "<PERSON><PERSON> via Host Head<PERSON> Poisoning", "type": "variant", "priority": 2}]}, {"id": "xssi", "name": "Cross Site Script Inclusion (XSSI)", "type": "subcategory", "priority": null}]}, {"id": "server_security_misconfiguration", "name": "Server Security Misconfiguration", "type": "category", "children": [{"id": "bitsquatting", "name": "Bitsquatting", "type": "subcategory", "priority": 5}, {"id": "cache_deception", "name": "<PERSON><PERSON>", "type": "subcategory", "priority": null}, {"id": "cache_poisoning", "name": "<PERSON><PERSON>", "type": "subcategory", "priority": null}, {"id": "<PERSON><PERSON>a", "name": "CAPTCHA", "type": "subcategory", "children": [{"id": "brute_force", "name": "Brute Force", "type": "variant", "priority": 5}, {"id": "implementation_vulnerability", "name": "Implementation Vulnerability", "type": "variant", "priority": 4}, {"id": "missing", "name": "Missing", "type": "variant", "priority": 5}]}, {"id": "clickjacking", "name": "Clickjacking", "type": "subcategory", "children": [{"id": "form_input", "name": "Form Input", "type": "variant", "priority": 5}, {"id": "non_sensitive_action", "name": "Non-Sensitive Action", "type": "variant", "priority": 5}, {"id": "sensitive_action", "name": "Sensitive Click-Based Action", "type": "variant", "priority": 4}]}, {"id": "cookie_scoped_to_parent_domain", "name": "<PERSON><PERSON>oped to Parent Domain", "type": "subcategory", "priority": 5}, {"id": "dbms_misconfiguration", "name": "Database Management System (DBMS) Misconfiguration", "type": "subcategory", "children": [{"id": "excessively_privileged_user_dba", "name": "Excessively Privileged User / DBA", "type": "variant", "priority": 4}]}, {"id": "directory_listing_enabled", "name": "Directory Listing Enabled", "type": "subcategory", "children": [{"id": "non_sensitive_data_exposure", "name": "Non-Sensitive Data Exposure", "type": "variant", "priority": 5}, {"id": "sensitive_data_exposure", "name": "Sensitive Data Exposure", "type": "variant", "priority": null}]}, {"id": "email_verification_bypass", "name": "Email Verification Bypass", "type": "subcategory", "priority": 5}, {"id": "exposed_admin_portal", "name": "Exposed Admin Portal", "type": "subcategory", "children": [{"id": "to_internet", "name": "To Internet", "type": "variant", "priority": 5}]}, {"id": "fingerprinting_banner_disclosure", "name": "Fingerprinting/Banner Disclosure", "type": "subcategory", "priority": 5}, {"id": "insecure_ssl", "name": "Insecure SSL", "type": "subcategory", "children": [{"id": "certificate_error", "name": "Certificate Error", "type": "variant", "priority": 5}, {"id": "insecure_cipher_suite", "name": "Insecure Cipher Suite", "type": "variant", "priority": 5}, {"id": "lack_of_forward_secrecy", "name": "Lack of Forward Secrecy", "type": "variant", "priority": 5}]}, {"id": "lack_of_password_confirmation", "name": "Lack of Password Confirmation", "type": "subcategory", "children": [{"id": "change_email_address", "name": "Change Email Address", "type": "variant", "priority": 5}, {"id": "change_password", "name": "Change Password", "type": "variant", "priority": 5}, {"id": "delete_account", "name": "Delete Account", "type": "variant", "priority": 4}, {"id": "manage_two_fa", "name": "Manage 2FA", "type": "variant", "priority": 5}]}, {"id": "lack_of_security_headers", "name": "Lack of Security Headers", "type": "subcategory", "children": [{"id": "cache_control_for_a_non_sensitive_page", "name": "Cache-Control for a Non-Sensitive Page", "type": "variant", "priority": 5}, {"id": "cache_control_for_a_sensitive_page", "name": "<PERSON><PERSON>-Control for a Sensitive Page", "type": "variant", "priority": 4}, {"id": "content_security_policy", "name": "Content-Security-Policy", "type": "variant", "priority": 5}, {"id": "content_security_policy_report_only", "name": "Content-Security-Policy-Report-Only", "type": "variant", "priority": 5}, {"id": "public_key_pins", "name": "Public-Key-Pins", "type": "variant", "priority": 5}, {"id": "strict_transport_security", "name": "Strict-Transport-Security", "type": "variant", "priority": 5}, {"id": "x_content_security_policy", "name": "X-Content-Security-Policy", "type": "variant", "priority": 5}, {"id": "x_content_type_options", "name": "X-Content-Type-Options", "type": "variant", "priority": 5}, {"id": "x_frame_options", "name": "X-Frame-Options", "type": "variant", "priority": 5}, {"id": "x_webkit_csp", "name": "X-Webkit-CSP", "type": "variant", "priority": 5}, {"id": "x_xss_protection", "name": "X-XSS-Protection", "type": "variant", "priority": 5}]}, {"id": "mail_server_misconfiguration", "name": "Mail Server Misconfiguration", "type": "subcategory", "children": [{"id": "email_spoofing_on_non_email_domain", "name": "Email Spoofing on Non-Email Domain", "type": "variant", "priority": 5}, {"id": "email_spoofing_to_inbox_due_to_missing_or_misconfigured_dmarc_on_email_domain", "name": "Email Spoofing to Inbox due to Missing or Misconfigured DMARC on Email Domain", "type": "variant", "priority": 4}, {"id": "email_spoofing_to_spam_folder", "name": "Email Spoofing to Spam Folder", "type": "variant", "priority": 5}, {"id": "missing_or_misconfigured_spf_and_or_dkim", "name": "Missing or Misconfigured SPF and/or DKIM", "type": "variant", "priority": 5}, {"id": "no_spoofing_protection_on_email_domain", "name": "No Spoofing Protection on Email Domain", "type": "variant", "priority": 3}]}, {"id": "misconfigured_dns", "name": "Misconfigured DNS", "type": "subcategory", "children": [{"id": "missing_caa_record", "name": "Missing Certification Authority Authorization (CAA) Record", "type": "variant", "priority": 5}, {"id": "subdomain_takeover", "name": "Subdomain Takeover", "type": "variant", "priority": 3}, {"id": "zone_transfer", "name": "Zone Transfer", "type": "variant", "priority": 4}]}, {"id": "missing_dnssec", "name": "Missing DNSSEC", "type": "subcategory", "priority": 5}, {"id": "missing_secure_or_httponly_cookie_flag", "name": "Missing Secure or HTTPOnly <PERSON>", "type": "subcategory", "children": [{"id": "non_session_cookie", "name": "Non-Session <PERSON><PERSON>", "type": "variant", "priority": 5}, {"id": "session_token", "name": "Session Token", "type": "variant", "priority": 4}]}, {"id": "missing_subresource_integrity", "name": "Missing Subresource Integrity", "type": "subcategory", "priority": 5}, {"id": "no_rate_limiting_on_form", "name": "No Rate Limiting on Form", "type": "subcategory", "children": [{"id": "change_password", "name": "Change Password", "type": "variant", "priority": 5}, {"id": "email_triggering", "name": "Email-Triggering", "type": "variant", "priority": 4}, {"id": "login", "name": "<PERSON><PERSON>", "type": "variant", "priority": 4}, {"id": "registration", "name": "Registration", "type": "variant", "priority": 4}, {"id": "sms_triggering", "name": "SMS-Triggering", "type": "variant", "priority": 4}]}, {"id": "oauth_misconfiguration", "name": "OAuth Misconfiguration", "type": "subcategory", "children": [{"id": "account_squatting", "name": "Account Squatting", "type": "variant", "priority": 4}, {"id": "account_takeover", "name": "Account Takeover", "type": "variant", "priority": 2}, {"id": "insecure_redirect_uri", "name": "Insecure Redirect URI", "type": "variant", "priority": null}, {"id": "missing_state_parameter", "name": "Missing/Broken State Parameter", "type": "variant", "priority": null}]}, {"id": "path_traversal", "name": "Path Traversal", "type": "subcategory", "priority": null}, {"id": "potentially_unsafe_http_method_enabled", "name": "Potentially Unsafe HTTP Method Enabled", "type": "subcategory", "children": [{"id": "options", "name": "OPTIONS", "type": "variant", "priority": 5}, {"id": "trace", "name": "TRACE", "type": "variant", "priority": 5}]}, {"id": "race_condition", "name": "Race Condition", "type": "subcategory", "priority": null}, {"id": "request_smuggling", "name": "HTTP Request Smuggling", "type": "subcategory", "priority": null}, {"id": "rfd", "name": "Reflected File Download (RFD)", "type": "subcategory", "priority": 5}, {"id": "same_site_scripting", "name": "Same-Site Scripting", "type": "subcategory", "priority": 5}, {"id": "server_side_request_forgery_ssrf", "name": "Server-Side Request Forgery (SSRF)", "type": "subcategory", "children": [{"id": "external_dns_query_only", "name": "External - DNS Query Only", "type": "variant", "priority": 5}, {"id": "external_low_impact", "name": "External - Low impact", "type": "variant", "priority": 5}, {"id": "internal_high_impact", "name": "Internal High Impact", "type": "variant", "priority": 2}, {"id": "internal_scan_and_or_medium_impact", "name": "Internal Scan and/or Medium Impact", "type": "variant", "priority": 3}]}, {"id": "software_package_takeover", "name": "Software Package Takeover", "type": "subcategory", "priority": null}, {"id": "ssl_attack_breach_poodle_etc", "name": "SSL Attack (BREACH, POODLE etc.)", "type": "subcategory", "priority": null}, {"id": "unsafe_cross_origin_resource_sharing", "name": "Unsafe Cross-Origin Resource Sharing", "type": "subcategory", "priority": null}, {"id": "unsafe_file_upload", "name": "Unsafe File Upload", "type": "subcategory", "children": [{"id": "file_extension_filter_bypass", "name": "File Extension Filter Bypass", "type": "variant", "priority": 5}, {"id": "no_antivirus", "name": "No Antivirus", "type": "variant", "priority": 5}, {"id": "no_size_limit", "name": "No Size Limit", "type": "variant", "priority": 5}]}, {"id": "username_enumeration", "name": "Username/Email Enumeration", "type": "subcategory", "children": [{"id": "brute_force", "name": "Brute Force", "type": "variant", "priority": 5}]}, {"id": "using_default_credentials", "name": "Using Default Credentials", "type": "subcategory", "priority": 1}, {"id": "waf_bypass", "name": "Web Application Firewall (WAF) Bypass", "type": "subcategory", "children": [{"id": "direct_server_access", "name": "Direct Server Access", "type": "variant", "priority": 4}]}]}, {"id": "server_side_injection", "name": "Server-Side Injection", "type": "category", "children": [{"id": "content_spoofing", "name": "Content Spoofing", "type": "subcategory", "children": [{"id": "email_html_injection", "name": "Email HTML Injection", "type": "variant", "priority": 4}, {"id": "email_hyperlink_injection_based_on_email_provider", "name": "Email Hyperlink Injection Based on Email Provider", "type": "variant", "priority": 5}, {"id": "external_authentication_injection", "name": "External Authentication Injection", "type": "variant", "priority": 4}, {"id": "flash_based_external_authentication_injection", "name": "Flash Based External Authentication Injection", "type": "variant", "priority": 5}, {"id": "homograph_idn_based", "name": "Homograph/IDN-Based", "type": "variant", "priority": 5}, {"id": "html_content_injection", "name": "HTML Content Injection", "type": "variant", "priority": 5}, {"id": "iframe_injection", "name": "iframe Injection", "type": "variant", "priority": 3}, {"id": "impersonation_via_broken_link_hijacking", "name": "Impersonation via Broken Link Hijacking", "type": "variant", "priority": 4}, {"id": "rtlo", "name": "Right-to-Left Override (RTLO)", "type": "variant", "priority": 5}, {"id": "text_injection", "name": "Text Injection", "type": "variant", "priority": 5}]}, {"id": "file_inclusion", "name": "File Inclusion", "type": "subcategory", "children": [{"id": "local", "name": "Local", "type": "variant", "priority": 1}]}, {"id": "http_response_manipulation", "name": "HTTP Response Manipulation", "type": "subcategory", "children": [{"id": "response_splitting_crlf", "name": "Response Splitting (CRLF)", "type": "variant", "priority": 3}]}, {"id": "ldap_injection", "name": "LDAP Injection", "type": "subcategory", "priority": null}, {"id": "parameter_pollution", "name": "Parameter Pollution", "type": "subcategory", "children": [{"id": "social_media_sharing_buttons", "name": "Social Media Sharing Buttons", "type": "variant", "priority": 5}]}, {"id": "remote_code_execution_rce", "name": "Remote Code Execution (RCE)", "type": "subcategory", "priority": 1}, {"id": "sql_injection", "name": "SQL Injection", "type": "subcategory", "priority": 1}, {"id": "ssti", "name": "Server-Side Template Injection (SSTI)", "type": "subcategory", "children": [{"id": "basic", "name": "Basic", "type": "variant", "priority": 4}, {"id": "custom", "name": "Custom", "type": "variant", "priority": null}]}, {"id": "xml_external_entity_injection_xxe", "name": "XML External Entity Injection (XXE)", "type": "subcategory", "priority": 1}]}, {"id": "smart_contract_misconfiguration", "name": "Smart Contract Misconfiguration", "type": "category", "children": [{"id": "bypass_of_function_modifiers_and_checks", "name": "Bypass of Function Modifiers and Checks", "type": "subcategory", "priority": null}, {"id": "function_level_denial_of_service", "name": "Function-level Denial of Service", "type": "subcategory", "priority": 3}, {"id": "improper_decimals_implementation", "name": "Improper Decimals Implementation", "type": "subcategory", "priority": 4}, {"id": "improper_fee_implementation", "name": "Improper Fee Implementation", "type": "subcategory", "priority": 3}, {"id": "improper_use_of_modifier", "name": "Improper Use of Modifier", "type": "subcategory", "priority": 4}, {"id": "inaccurate_rounding_calculation", "name": "Inaccurate Rounding Calculation", "type": "subcategory", "priority": null}, {"id": "integer_overflow_underflow", "name": "Integer Overflow / Underflow", "type": "subcategory", "priority": 2}, {"id": "irreversible_function_call", "name": "Irreversible Function Call", "type": "subcategory", "priority": 3}, {"id": "malicious_superuser_risk", "name": "Malicious Superuser Risk", "type": "subcategory", "priority": 3}, {"id": "reentrancy_attack", "name": "Reentrancy Attack", "type": "subcategory", "priority": 1}, {"id": "smart_contract_owner_takeover", "name": "Smart Contract Owner Takeover", "type": "subcategory", "priority": 1}, {"id": "unauthorized_smart_contract_approval", "name": "Unauthorized Smart Contract Approval", "type": "subcategory", "priority": 2}, {"id": "unauthorized_transfer_of_funds", "name": "Unauthorized Transfer of Funds", "type": "subcategory", "priority": 1}, {"id": "uninitialized_variables", "name": "Uninitialized Variables", "type": "subcategory", "priority": 1}]}, {"id": "societal_biases", "name": "Societal Biases", "type": "category", "children": [{"id": "confirmation_bias", "name": "Confirmation Bias", "type": "subcategory", "priority": null}, {"id": "systemic_bias", "name": "Systemic Bias", "type": "subcategory", "priority": null}]}, {"id": "unvalidated_redirects_and_forwards", "name": "Unvalidated Redirects and Forwards", "type": "category", "children": [{"id": "lack_of_security_speed_bump_page", "name": "Lack of Security Speed Bump Page", "type": "subcategory", "priority": 5}, {"id": "open_redirect", "name": "Open Redirect", "type": "subcategory", "children": [{"id": "flash_based", "name": "Flash-Based", "type": "variant", "priority": 5}, {"id": "get_based", "name": "GET-Based", "type": "variant", "priority": 4}, {"id": "header_based", "name": "Header-Based", "type": "variant", "priority": 5}, {"id": "post_based", "name": "POST-Based", "type": "variant", "priority": 5}]}, {"id": "tabnabbing", "name": "Tabnabbing", "type": "subcategory", "priority": 5}]}, {"id": "using_components_with_known_vulnerabilities", "name": "Using Components with Known Vulnerabilities", "type": "category", "children": [{"id": "captcha_bypass", "name": "Captcha Bypass", "type": "subcategory", "children": [{"id": "ocr_optical_character_recognition", "name": "OCR (Optical Character Recognition)", "type": "variant", "priority": 5}]}, {"id": "outdated_software_version", "name": "Outdated Software Version", "type": "subcategory", "priority": 5}, {"id": "rosetta_flash", "name": "<PERSON><PERSON> Flash", "type": "subcategory", "priority": 5}]}, {"id": "zero_knowledge_security_misconfiguration", "name": "Zero Knowledge Security Misconfiguration", "type": "category", "children": [{"id": "deanonymization_of_data", "name": "Deanonymization of Data", "type": "subcategory", "priority": 1}, {"id": "improper_proof_validation_and_finalization_logic", "name": "Improper Proof Validation and Finalization Logic", "type": "subcategory", "priority": 1}, {"id": "misconfigured_trusted_setup", "name": "Misconfigured Trusted Setup", "type": "subcategory", "priority": null}, {"id": "mismatching_bit_lengths", "name": "Mismatching Bit Lengths", "type": "subcategory", "priority": null}, {"id": "missing_constraint", "name": "Missing Constraint", "type": "subcategory", "priority": null}, {"id": "missing_range_check", "name": "Missing Range Check", "type": "subcategory", "priority": null}]}]}