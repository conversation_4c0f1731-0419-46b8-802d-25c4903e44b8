"""
취약점 분류 체계 모델
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum

class NodeType(str, Enum):
    """노드 타입 열거형"""
    CATEGORY = "category"
    SUBCATEGORY = "subcategory"
    VARIANT = "variant"

class TaxonomyNode(BaseModel):
    """취약점 분류 체계 노드"""
    id: str = Field(..., description="노드 고유 ID")
    name: str = Field(..., description="노드 이름")
    type: NodeType = Field(..., description="노드 타입")
    priority: Optional[int] = Field(None, description="우선순위 (1-5, 1이 가장 높음)")
    children: List['TaxonomyNode'] = Field(default_factory=list, description="하위 노드들")
    parent_id: Optional[str] = Field(None, description="상위 노드 ID")
    
    class Config:
        use_enum_values = True

class VulnerabilityTaxonomy(BaseModel):
    """전체 취약점 분류 체계"""
    metadata: Dict[str, Any] = Field(..., description="메타데이터")
    content: List[TaxonomyNode] = Field(..., description="분류 체계 내용")
    
    def find_node_by_id(self, node_id: str) -> Optional[TaxonomyNode]:
        """ID로 노드 찾기"""
        def search_recursive(nodes: List[TaxonomyNode]) -> Optional[TaxonomyNode]:
            for node in nodes:
                if node.id == node_id:
                    return node
                if node.children:
                    result = search_recursive(node.children)
                    if result:
                        return result
            return None
        
        return search_recursive(self.content)
    
    def find_nodes_by_type(self, node_type: NodeType) -> List[TaxonomyNode]:
        """타입으로 노드들 찾기"""
        result = []
        
        def search_recursive(nodes: List[TaxonomyNode]):
            for node in nodes:
                if node.type == node_type:
                    result.append(node)
                if node.children:
                    search_recursive(node.children)
        
        search_recursive(self.content)
        return result
    
    def find_nodes_by_priority(self, priority: int) -> List[TaxonomyNode]:
        """우선순위로 노드들 찾기"""
        result = []
        
        def search_recursive(nodes: List[TaxonomyNode]):
            for node in nodes:
                if node.priority == priority:
                    result.append(node)
                if node.children:
                    search_recursive(node.children)
        
        search_recursive(self.content)
        return result
    
    def get_path_to_node(self, node_id: str) -> List[str]:
        """노드까지의 경로 반환"""
        def find_path(nodes: List[TaxonomyNode], target_id: str, current_path: List[str]) -> Optional[List[str]]:
            for node in nodes:
                new_path = current_path + [node.id]
                if node.id == target_id:
                    return new_path
                if node.children:
                    result = find_path(node.children, target_id, new_path)
                    if result:
                        return result
            return None
        
        path = find_path(self.content, node_id, [])
        return path if path else []
    
    def get_all_categories(self) -> List[TaxonomyNode]:
        """모든 카테고리 반환"""
        return [node for node in self.content if node.type == NodeType.CATEGORY]
    
    def get_subcategories_by_category(self, category_id: str) -> List[TaxonomyNode]:
        """특정 카테고리의 하위 카테고리들 반환"""
        category = self.find_node_by_id(category_id)
        if not category:
            return []
        
        return [child for child in category.children if child.type == NodeType.SUBCATEGORY]
    
    def get_variants_by_subcategory(self, subcategory_id: str) -> List[TaxonomyNode]:
        """특정 하위 카테고리의 변형들 반환"""
        subcategory = self.find_node_by_id(subcategory_id)
        if not subcategory:
            return []
        
        return [child for child in subcategory.children if child.type == NodeType.VARIANT]

# 순환 참조 해결을 위한 모델 업데이트
TaxonomyNode.model_rebuild()
