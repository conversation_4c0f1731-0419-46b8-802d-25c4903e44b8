{"ast": null, "code": "export default function nice(domain, interval) {\n  domain = domain.slice();\n  var i0 = 0,\n    i1 = domain.length - 1,\n    x0 = domain[i0],\n    x1 = domain[i1],\n    t;\n  if (x1 < x0) {\n    t = i0, i0 = i1, i1 = t;\n    t = x0, x0 = x1, x1 = t;\n  }\n  domain[i0] = interval.floor(x0);\n  domain[i1] = interval.ceil(x1);\n  return domain;\n}", "map": {"version": 3, "names": ["nice", "domain", "interval", "slice", "i0", "i1", "length", "x0", "x1", "t", "floor", "ceil"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/node_modules/d3-scale/src/nice.js"], "sourcesContent": ["export default function nice(domain, interval) {\n  domain = domain.slice();\n\n  var i0 = 0,\n      i1 = domain.length - 1,\n      x0 = domain[i0],\n      x1 = domain[i1],\n      t;\n\n  if (x1 < x0) {\n    t = i0, i0 = i1, i1 = t;\n    t = x0, x0 = x1, x1 = t;\n  }\n\n  domain[i0] = interval.floor(x0);\n  domain[i1] = interval.ceil(x1);\n  return domain;\n}\n"], "mappings": "AAAA,eAAe,SAASA,IAAIA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC7CD,MAAM,GAAGA,MAAM,CAACE,KAAK,CAAC,CAAC;EAEvB,IAAIC,EAAE,GAAG,CAAC;IACNC,EAAE,GAAGJ,MAAM,CAACK,MAAM,GAAG,CAAC;IACtBC,EAAE,GAAGN,MAAM,CAACG,EAAE,CAAC;IACfI,EAAE,GAAGP,MAAM,CAACI,EAAE,CAAC;IACfI,CAAC;EAEL,IAAID,EAAE,GAAGD,EAAE,EAAE;IACXE,CAAC,GAAGL,EAAE,EAAEA,EAAE,GAAGC,EAAE,EAAEA,EAAE,GAAGI,CAAC;IACvBA,CAAC,GAAGF,EAAE,EAAEA,EAAE,GAAGC,EAAE,EAAEA,EAAE,GAAGC,CAAC;EACzB;EAEAR,MAAM,CAACG,EAAE,CAAC,GAAGF,QAAQ,CAACQ,KAAK,CAACH,EAAE,CAAC;EAC/BN,MAAM,CAACI,EAAE,CAAC,GAAGH,QAAQ,CAACS,IAAI,CAACH,EAAE,CAAC;EAC9B,OAAOP,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}