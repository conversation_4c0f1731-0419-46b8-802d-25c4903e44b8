{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aidesk\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Layout from './components/Layout';\nimport Dashboard from './pages/Dashboard';\nimport VulnerabilityAnalysis from './pages/VulnerabilityAnalysis';\nimport AnalysisResults from './pages/AnalysisResults';\nimport TaxonomyBrowser from './pages/TaxonomyBrowser';\nimport Reports from './pages/Reports';\nimport Settings from './pages/Settings';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/analysis\",\n          element: /*#__PURE__*/_jsxDEV(VulnerabilityAnalysis, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/results\",\n          element: /*#__PURE__*/_jsxDEV(AnalysisResults, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/results/:analysisId\",\n          element: /*#__PURE__*/_jsxDEV(AnalysisResults, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 55\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/taxonomy\",\n          element: /*#__PURE__*/_jsxDEV(TaxonomyBrowser, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/reports\",\n          element: /*#__PURE__*/_jsxDEV(Reports, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/settings\",\n          element: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Layout", "Dashboard", "VulnerabilityAnalysis", "AnalysisResults", "TaxonomyBrowser", "Reports", "Settings", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Layout from './components/Layout';\nimport Dashboard from './pages/Dashboard';\nimport VulnerabilityAnalysis from './pages/VulnerabilityAnalysis';\nimport AnalysisResults from './pages/AnalysisResults';\nimport TaxonomyBrowser from './pages/TaxonomyBrowser';\nimport Reports from './pages/Reports';\nimport Settings from './pages/Settings';\n\nfunction App() {\n  return (\n    <Router>\n      <Layout>\n        <Routes>\n          <Route path=\"/\" element={<Dashboard />} />\n          <Route path=\"/analysis\" element={<VulnerabilityAnalysis />} />\n          <Route path=\"/results\" element={<AnalysisResults />} />\n          <Route path=\"/results/:analysisId\" element={<AnalysisResults />} />\n          <Route path=\"/taxonomy\" element={<TaxonomyBrowser />} />\n          <Route path=\"/reports\" element={<Reports />} />\n          <Route path=\"/settings\" element={<Settings />} />\n        </Routes>\n      </Layout>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,qBAAqB,MAAM,+BAA+B;AACjE,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,QAAQ,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACX,MAAM;IAAAa,QAAA,eACLF,OAAA,CAACR,MAAM;MAAAU,QAAA,eACLF,OAAA,CAACV,MAAM;QAAAY,QAAA,gBACLF,OAAA,CAACT,KAAK;UAACY,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEJ,OAAA,CAACP,SAAS;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CR,OAAA,CAACT,KAAK;UAACY,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEJ,OAAA,CAACN,qBAAqB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DR,OAAA,CAACT,KAAK;UAACY,IAAI,EAAC,UAAU;UAACC,OAAO,eAAEJ,OAAA,CAACL,eAAe;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvDR,OAAA,CAACT,KAAK;UAACY,IAAI,EAAC,sBAAsB;UAACC,OAAO,eAAEJ,OAAA,CAACL,eAAe;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnER,OAAA,CAACT,KAAK;UAACY,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEJ,OAAA,CAACJ,eAAe;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxDR,OAAA,CAACT,KAAK;UAACY,IAAI,EAAC,UAAU;UAACC,OAAO,eAAEJ,OAAA,CAACH,OAAO;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CR,OAAA,CAACT,KAAK;UAACY,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEJ,OAAA,CAACF,QAAQ;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb;AAACC,EAAA,GAhBQR,GAAG;AAkBZ,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}