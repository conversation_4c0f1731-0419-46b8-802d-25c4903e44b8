# 환경 설정 예시 파일
# 실제 사용시 .env 파일로 복사하여 사용하세요

# 애플리케이션 설정
APP_NAME=Bugcrowd AI Vulnerability Validator
APP_VERSION=1.0.0
DEBUG=true
SECRET_KEY=your-secret-key-here

# 서버 설정
BACKEND_HOST=localhost
BACKEND_PORT=8000
FRONTEND_HOST=localhost
FRONTEND_PORT=3000

# 데이터베이스 설정
DATABASE_URL=sqlite:///./vulnerability_db.db
# PostgreSQL 사용시: postgresql://user:password@localhost/dbname

# AI 모델 설정
# OpenAI API (선택사항)
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4

# Ollama 설정 (로컬 LLM)
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL=llama2

# 커스텀 AI API 설정
CUSTOM_AI_API_URL=http://localhost:8080/api/v1/chat
CUSTOM_AI_API_KEY=your-custom-api-key

# 로깅 설정
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# CORS 설정
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 파일 업로드 설정
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=uploads/

# 캐시 설정
REDIS_URL=redis://localhost:6379/0

# 보안 설정
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
