"""
AI 분석 관련 API 엔드포인트
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from app.models.vulnerability import (
    AnalysisRequest, 
    AnalysisResponse, 
    AnalysisResult,
    AnalysisStatus
)
from app.services.vulnerability_service import VulnerabilityService
from app.services.taxonomy_service import TaxonomyService
from app.utils.validators import validate_analysis_request
from app.utils.logger import setup_logger

logger = setup_logger(__name__)
router = APIRouter()

# 의존성 주입을 위한 서비스 인스턴스
def get_taxonomy_service() -> TaxonomyService:
    return TaxonomyService()

def get_vulnerability_service(
    taxonomy_service: TaxonomyService = Depends(get_taxonomy_service)
) -> VulnerabilityService:
    return VulnerabilityService(taxonomy_service)

async def perform_ai_analysis(
    analysis_id: str,
    report_data: Dict[str, Any],
    ai_model: str,
    service: VulnerabilityService
):
    """백그라운드에서 AI 분석 수행"""
    try:
        # 분석 상태를 진행 중으로 변경
        service.update_analysis_result(analysis_id, {
            'status': AnalysisStatus.IN_PROGRESS
        })

        # AI 분석 엔진 초기화
        from ai_engine.analyzer import VulnerabilityAnalyzer, AnalysisConfig
        from ai_engine.models.ai_models import AIModelManager

        # 분류 체계 데이터 로드
        taxonomy_service = get_taxonomy_service()
        taxonomy = taxonomy_service.get_taxonomy()
        taxonomy_data = []
        if taxonomy:
            for category in taxonomy.content:
                taxonomy_data.append({
                    'id': category.id,
                    'name': category.name,
                    'type': category.type,
                    'priority': category.priority
                })

        # AI 분석기 초기화
        analyzer = VulnerabilityAnalyzer(taxonomy_data)

        # 분석 설정
        config = AnalysisConfig(
            model_name=ai_model if ai_model else None,
            temperature=0.3,
            max_tokens=2000,
            include_reasoning=True,
            include_recommendations=True,
            confidence_threshold=0.5
        )

        # AI 분석 실행
        analysis_result = await analyzer.analyze_vulnerability(report_data, config)

        if analysis_result.success:
            # 성공적인 분석 결과 처리
            update_data = {
                'status': AnalysisStatus.COMPLETED,
                'analysis_confidence': analysis_result.confidence,
                'reasoning': analysis_result.parsed_analysis.metadata.get('analysis_notes', '') if analysis_result.parsed_analysis else '',
                'recommendations': analysis_result.recommendations
            }

            # 분류 결과 처리
            if analysis_result.classification.get('primary_category'):
                primary_cat = analysis_result.classification['primary_category']
                update_data['primary_category'] = {
                    'id': primary_cat.get('id', ''),
                    'name': primary_cat.get('name', ''),
                    'type': primary_cat.get('type', 'category'),
                    'confidence_score': primary_cat.get('confidence_score', 0.0)
                }

            # 예측된 카테고리들
            if analysis_result.classification.get('secondary_categories'):
                update_data['predicted_categories'] = analysis_result.classification['secondary_categories']

            # 심각도 평가
            if analysis_result.severity_assessment.get('severity'):
                update_data['predicted_severity'] = analysis_result.severity_assessment['severity']
                update_data['severity_confidence'] = analysis_result.severity_assessment.get('confidence', 0.0)

            # 자동 검증 로직 실행
            verification_result = await perform_automatic_verification(
                analysis_result, report_data, taxonomy_service
            )

            if verification_result['auto_verified']:
                update_data['is_verified'] = True
                update_data['verified_by'] = 'AI_AUTO_VERIFICATION'
                update_data['verification_notes'] = verification_result['verification_notes']

        else:
            # 분석 실패 처리
            update_data = {
                'status': AnalysisStatus.FAILED,
                'reasoning': f'분석 실패: {"; ".join(analysis_result.errors)}'
            }

        # 분석 결과 업데이트
        service.update_analysis_result(analysis_id, update_data)

        logger.info(f"AI 분석 완료: {analysis_id}, 성공: {analysis_result.success}")

    except Exception as e:
        logger.error(f"AI 분석 중 오류: {str(e)}")
        # 분석 실패 상태로 변경
        service.update_analysis_result(analysis_id, {
            'status': AnalysisStatus.FAILED,
            'reasoning': f'분석 중 오류 발생: {str(e)}'
        })

async def perform_automatic_verification(
    analysis_result,
    report_data: Dict[str, Any],
    taxonomy_service
) -> Dict[str, Any]:
    """자동 검증 로직"""
    try:
        verification_score = 0.0
        verification_notes = []

        # 1. 신뢰도 기반 검증
        if analysis_result.confidence >= 0.8:
            verification_score += 0.3
            verification_notes.append("높은 AI 신뢰도 (≥80%)")
        elif analysis_result.confidence >= 0.6:
            verification_score += 0.2
            verification_notes.append("중간 AI 신뢰도 (≥60%)")

        # 2. 키워드 일치도 검증
        if analysis_result.processed_text and analysis_result.processed_text.keywords:
            keyword_relevance = calculate_keyword_relevance(
                analysis_result.processed_text.keywords,
                analysis_result.classification
            )
            if keyword_relevance >= 0.7:
                verification_score += 0.2
                verification_notes.append("키워드 일치도 높음")
            elif keyword_relevance >= 0.5:
                verification_score += 0.1
                verification_notes.append("키워드 일치도 중간")

        # 3. 분류 일관성 검증
        consistency_score = verify_classification_consistency(
            analysis_result.classification, taxonomy_service
        )
        verification_score += consistency_score * 0.2
        if consistency_score >= 0.8:
            verification_notes.append("분류 일관성 높음")

        # 4. 심각도 일관성 검증
        severity_consistency = verify_severity_consistency(
            analysis_result.severity_assessment,
            analysis_result.classification
        )
        verification_score += severity_consistency * 0.2
        if severity_consistency >= 0.8:
            verification_notes.append("심각도 평가 일관성 높음")

        # 5. 텍스트 품질 검증
        text_quality = assess_text_quality(report_data)
        verification_score += text_quality * 0.1
        if text_quality >= 0.8:
            verification_notes.append("입력 텍스트 품질 높음")

        # 자동 검증 임계값 (0.7 이상)
        auto_verified = verification_score >= 0.7

        return {
            'auto_verified': auto_verified,
            'verification_score': verification_score,
            'verification_notes': f"자동 검증 점수: {verification_score:.2f} - {'; '.join(verification_notes)}"
        }

    except Exception as e:
        logger.error(f"자동 검증 중 오류: {str(e)}")
        return {
            'auto_verified': False,
            'verification_score': 0.0,
            'verification_notes': f"자동 검증 실패: {str(e)}"
        }

def calculate_keyword_relevance(keywords: List[str], classification: Dict[str, Any]) -> float:
    """키워드와 분류의 관련성 계산"""
    if not keywords or not classification.get('primary_category'):
        return 0.0

    primary_category = classification['primary_category']
    category_name = primary_category.get('name', '').lower()
    category_id = primary_category.get('id', '').lower()

    keyword_str = ' '.join(keywords).lower()

    # 카테고리 이름과 키워드 매칭
    name_words = category_name.replace('-', ' ').replace('_', ' ').split()
    id_words = category_id.replace('-', ' ').replace('_', ' ').split()

    all_category_words = set(name_words + id_words)
    matched_words = sum(1 for word in all_category_words if word in keyword_str)

    if len(all_category_words) == 0:
        return 0.0

    return min(matched_words / len(all_category_words), 1.0)

def verify_classification_consistency(classification: Dict[str, Any], taxonomy_service) -> float:
    """분류 일관성 검증"""
    try:
        if not classification.get('primary_category'):
            return 0.0

        primary_category = classification['primary_category']
        category_id = primary_category.get('id')

        if not category_id:
            return 0.0

        # 분류 체계에서 해당 카테고리 확인
        taxonomy_node = taxonomy_service.get_category_by_id(category_id)
        if not taxonomy_node:
            return 0.0

        # 카테고리 정보 일치 확인
        name_match = taxonomy_node.name == primary_category.get('name')
        type_match = taxonomy_node.type == primary_category.get('type')

        consistency_score = 0.0
        if name_match:
            consistency_score += 0.5
        if type_match:
            consistency_score += 0.5

        return consistency_score

    except Exception:
        return 0.0

def verify_severity_consistency(severity_assessment: Dict[str, Any], classification: Dict[str, Any]) -> float:
    """심각도 일관성 검증"""
    try:
        predicted_severity = severity_assessment.get('severity')
        if not predicted_severity:
            return 0.0

        primary_category = classification.get('primary_category', {})
        category_priority = primary_category.get('priority')

        if not category_priority:
            return 0.5  # 우선순위 정보가 없으면 중간 점수

        # 우선순위와 심각도 매핑 검증
        severity_priority_map = {
            'critical': [1],
            'high': [1, 2],
            'medium': [2, 3, 4],
            'low': [4, 5],
            'info': [5]
        }

        expected_priorities = severity_priority_map.get(predicted_severity, [])
        if category_priority in expected_priorities:
            return 1.0
        else:
            # 인접한 우선순위면 부분 점수
            for priority in expected_priorities:
                if abs(category_priority - priority) <= 1:
                    return 0.7
            return 0.3

    except Exception:
        return 0.0

def assess_text_quality(report_data: Dict[str, Any]) -> float:
    """텍스트 품질 평가"""
    try:
        quality_score = 0.0

        # 제목 품질
        title = report_data.get('title', '')
        if len(title) >= 10:
            quality_score += 0.2

        # 설명 품질
        description = report_data.get('description', '')
        if len(description) >= 50:
            quality_score += 0.3
        if len(description) >= 200:
            quality_score += 0.2

        # 재현 단계 존재
        if report_data.get('steps_to_reproduce'):
            quality_score += 0.2

        # 개념 증명 존재
        if report_data.get('proof_of_concept'):
            quality_score += 0.1

        return min(quality_score, 1.0)

    except Exception:
        return 0.0

@router.post("/analyze", response_model=AnalysisResponse)
async def analyze_vulnerability(
    request: AnalysisRequest,
    background_tasks: BackgroundTasks,
    service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """취약점 AI 분석 요청"""
    try:
        # 요청 데이터 검증
        request_dict = request.dict()
        validation_result = validate_analysis_request(request_dict)
        
        if not validation_result['is_valid']:
            raise HTTPException(
                status_code=400,
                detail={
                    "message": "분석 요청 데이터가 유효하지 않습니다",
                    "errors": validation_result['errors']
                }
            )
        
        # 보고서 생성 (임시)
        report = service.create_report(request.report.dict())
        
        # 분석 결과 생성
        ai_model = request.ai_model or "gpt-4"
        analysis_result = service.create_analysis_result(report.id, ai_model)
        
        # 백그라운드에서 AI 분석 수행
        background_tasks.add_task(
            perform_ai_analysis,
            analysis_result.id,
            request.report.dict(),
            ai_model,
            service
        )
        
        logger.info(f"AI 분석 요청 접수: {analysis_result.id}")
        
        return AnalysisResponse(
            success=True,
            message="분석 요청이 접수되었습니다",
            analysis_id=analysis_result.id,
            result=analysis_result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"분석 요청 처리 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.get("/results/{analysis_id}", response_model=AnalysisResult)
async def get_analysis_result(
    analysis_id: str,
    service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """분석 결과 조회"""
    try:
        result = service.get_analysis_result(analysis_id)
        if not result:
            raise HTTPException(status_code=404, detail="분석 결과를 찾을 수 없습니다")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"분석 결과 조회 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.get("/results", response_model=List[AnalysisResult])
async def get_all_analysis_results(
    status: Optional[str] = None,
    service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """모든 분석 결과 조회"""
    try:
        results = service.get_all_analysis_results()
        
        # 상태 필터링
        if status:
            results = [r for r in results if r.status == status]
        
        return results
        
    except Exception as e:
        logger.error(f"분석 결과 목록 조회 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.put("/results/{analysis_id}/verify")
async def verify_analysis_result(
    analysis_id: str,
    verification_data: Dict[str, Any],
    service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """분석 결과 검증"""
    try:
        # 분석 결과 존재 확인
        result = service.get_analysis_result(analysis_id)
        if not result:
            raise HTTPException(status_code=404, detail="분석 결과를 찾을 수 없습니다")
        
        # 검증 데이터 업데이트
        from datetime import datetime
        update_data = {
            'is_verified': True,
            'verified_at': datetime.now(),
            'verified_by': verification_data.get('verified_by'),
            'verification_notes': verification_data.get('notes')
        }
        
        updated_result = service.update_analysis_result(analysis_id, update_data)
        if not updated_result:
            raise HTTPException(status_code=500, detail="검증 정보 업데이트 실패")
        
        logger.info(f"분석 결과 검증됨: {analysis_id}")
        return {"message": "분석 결과가 검증되었습니다"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"분석 결과 검증 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.post("/batch-analyze")
async def batch_analyze_vulnerabilities(
    requests: List[AnalysisRequest],
    background_tasks: BackgroundTasks,
    service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """배치 취약점 분석"""
    try:
        if len(requests) > 10:  # 배치 크기 제한
            raise HTTPException(
                status_code=400,
                detail="배치 크기는 최대 10개까지 가능합니다"
            )
        
        analysis_ids = []
        
        for request in requests:
            # 각 요청에 대해 분석 시작
            request_dict = request.dict()
            validation_result = validate_analysis_request(request_dict)
            
            if validation_result['is_valid']:
                # 보고서 생성
                report = service.create_report(request.report.dict())
                
                # 분석 결과 생성
                ai_model = request.ai_model or "gpt-4"
                analysis_result = service.create_analysis_result(report.id, ai_model)
                analysis_ids.append(analysis_result.id)
                
                # 백그라운드 분석 추가
                background_tasks.add_task(
                    perform_ai_analysis,
                    analysis_result.id,
                    request.report.dict(),
                    ai_model,
                    service
                )
        
        logger.info(f"배치 분석 요청 접수: {len(analysis_ids)}개")
        
        return {
            "success": True,
            "message": f"{len(analysis_ids)}개의 분석 요청이 접수되었습니다",
            "analysis_ids": analysis_ids
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"배치 분석 요청 처리 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")
