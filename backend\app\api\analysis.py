"""
AI 분석 관련 API 엔드포인트
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from app.models.vulnerability import (
    AnalysisRequest, 
    AnalysisResponse, 
    AnalysisResult,
    AnalysisStatus
)
from app.services.vulnerability_service import VulnerabilityService
from app.services.taxonomy_service import TaxonomyService
from app.utils.validators import validate_analysis_request
from app.utils.logger import setup_logger

logger = setup_logger(__name__)
router = APIRouter()

# 의존성 주입을 위한 서비스 인스턴스
def get_taxonomy_service() -> TaxonomyService:
    return TaxonomyService()

def get_vulnerability_service(
    taxonomy_service: TaxonomyService = Depends(get_taxonomy_service)
) -> VulnerabilityService:
    return VulnerabilityService(taxonomy_service)

async def perform_ai_analysis(
    analysis_id: str,
    report_data: Dict[str, Any],
    ai_model: str,
    service: VulnerabilityService
):
    """백그라운드에서 AI 분석 수행"""
    try:
        # 분석 상태를 진행 중으로 변경
        service.update_analysis_result(analysis_id, {
            'status': AnalysisStatus.IN_PROGRESS
        })
        
        # TODO: 실제 AI 분석 로직 구현
        # 현재는 더미 결과 반환
        import time
        import random
        
        # 분석 시뮬레이션 (실제로는 AI 엔진 호출)
        time.sleep(2)  # 분석 시간 시뮬레이션
        
        # 더미 분석 결과
        predicted_categories = [
            {
                "id": "cross_site_scripting_xss",
                "name": "Cross-Site Scripting (XSS)",
                "type": "category",
                "priority": 3,
                "confidence_score": 0.85
            }
        ]
        
        # 분석 결과 업데이트
        service.update_analysis_result(analysis_id, {
            'status': AnalysisStatus.COMPLETED,
            'predicted_categories': predicted_categories,
            'primary_category': predicted_categories[0] if predicted_categories else None,
            'predicted_severity': 'medium',
            'severity_confidence': 0.8,
            'analysis_confidence': 0.85,
            'reasoning': '입력된 취약점 설명에서 XSS 관련 키워드와 패턴이 발견되었습니다.',
            'recommendations': [
                '입력 데이터 검증 강화',
                '출력 데이터 이스케이프 처리',
                'Content Security Policy 적용'
            ]
        })
        
        logger.info(f"AI 분석 완료: {analysis_id}")
        
    except Exception as e:
        logger.error(f"AI 분석 중 오류: {str(e)}")
        # 분석 실패 상태로 변경
        service.update_analysis_result(analysis_id, {
            'status': AnalysisStatus.FAILED,
            'reasoning': f'분석 중 오류 발생: {str(e)}'
        })

@router.post("/analyze", response_model=AnalysisResponse)
async def analyze_vulnerability(
    request: AnalysisRequest,
    background_tasks: BackgroundTasks,
    service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """취약점 AI 분석 요청"""
    try:
        # 요청 데이터 검증
        request_dict = request.dict()
        validation_result = validate_analysis_request(request_dict)
        
        if not validation_result['is_valid']:
            raise HTTPException(
                status_code=400,
                detail={
                    "message": "분석 요청 데이터가 유효하지 않습니다",
                    "errors": validation_result['errors']
                }
            )
        
        # 보고서 생성 (임시)
        report = service.create_report(request.report.dict())
        
        # 분석 결과 생성
        ai_model = request.ai_model or "gpt-4"
        analysis_result = service.create_analysis_result(report.id, ai_model)
        
        # 백그라운드에서 AI 분석 수행
        background_tasks.add_task(
            perform_ai_analysis,
            analysis_result.id,
            request.report.dict(),
            ai_model,
            service
        )
        
        logger.info(f"AI 분석 요청 접수: {analysis_result.id}")
        
        return AnalysisResponse(
            success=True,
            message="분석 요청이 접수되었습니다",
            analysis_id=analysis_result.id,
            result=analysis_result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"분석 요청 처리 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.get("/results/{analysis_id}", response_model=AnalysisResult)
async def get_analysis_result(
    analysis_id: str,
    service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """분석 결과 조회"""
    try:
        result = service.get_analysis_result(analysis_id)
        if not result:
            raise HTTPException(status_code=404, detail="분석 결과를 찾을 수 없습니다")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"분석 결과 조회 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.get("/results", response_model=List[AnalysisResult])
async def get_all_analysis_results(
    status: Optional[str] = None,
    service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """모든 분석 결과 조회"""
    try:
        results = service.get_all_analysis_results()
        
        # 상태 필터링
        if status:
            results = [r for r in results if r.status == status]
        
        return results
        
    except Exception as e:
        logger.error(f"분석 결과 목록 조회 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.put("/results/{analysis_id}/verify")
async def verify_analysis_result(
    analysis_id: str,
    verification_data: Dict[str, Any],
    service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """분석 결과 검증"""
    try:
        # 분석 결과 존재 확인
        result = service.get_analysis_result(analysis_id)
        if not result:
            raise HTTPException(status_code=404, detail="분석 결과를 찾을 수 없습니다")
        
        # 검증 데이터 업데이트
        from datetime import datetime
        update_data = {
            'is_verified': True,
            'verified_at': datetime.now(),
            'verified_by': verification_data.get('verified_by'),
            'verification_notes': verification_data.get('notes')
        }
        
        updated_result = service.update_analysis_result(analysis_id, update_data)
        if not updated_result:
            raise HTTPException(status_code=500, detail="검증 정보 업데이트 실패")
        
        logger.info(f"분석 결과 검증됨: {analysis_id}")
        return {"message": "분석 결과가 검증되었습니다"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"분석 결과 검증 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.post("/batch-analyze")
async def batch_analyze_vulnerabilities(
    requests: List[AnalysisRequest],
    background_tasks: BackgroundTasks,
    service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """배치 취약점 분석"""
    try:
        if len(requests) > 10:  # 배치 크기 제한
            raise HTTPException(
                status_code=400,
                detail="배치 크기는 최대 10개까지 가능합니다"
            )
        
        analysis_ids = []
        
        for request in requests:
            # 각 요청에 대해 분석 시작
            request_dict = request.dict()
            validation_result = validate_analysis_request(request_dict)
            
            if validation_result['is_valid']:
                # 보고서 생성
                report = service.create_report(request.report.dict())
                
                # 분석 결과 생성
                ai_model = request.ai_model or "gpt-4"
                analysis_result = service.create_analysis_result(report.id, ai_model)
                analysis_ids.append(analysis_result.id)
                
                # 백그라운드 분석 추가
                background_tasks.add_task(
                    perform_ai_analysis,
                    analysis_result.id,
                    request.report.dict(),
                    ai_model,
                    service
                )
        
        logger.info(f"배치 분석 요청 접수: {len(analysis_ids)}개")
        
        return {
            "success": True,
            "message": f"{len(analysis_ids)}개의 분석 요청이 접수되었습니다",
            "analysis_ids": analysis_ids
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"배치 분석 요청 처리 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")
