{"ast": null, "code": "var array = Array.prototype;\nexport var slice = array.slice;\nexport var map = array.map;", "map": {"version": 3, "names": ["array", "Array", "prototype", "slice", "map"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/node_modules/d3-array/src/array.js"], "sourcesContent": ["var array = Array.prototype;\n\nexport var slice = array.slice;\nexport var map = array.map;\n"], "mappings": "AAAA,IAAIA,KAAK,GAAGC,KAAK,CAACC,SAAS;AAE3B,OAAO,IAAIC,KAAK,GAAGH,KAAK,CAACG,KAAK;AAC9B,OAAO,IAAIC,GAAG,GAAGJ,KAAK,CAACI,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}