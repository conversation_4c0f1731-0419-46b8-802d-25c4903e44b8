import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import {
  BeakerIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';
import { analysisApi, VulnerabilityReport, AnalysisRequest } from '../services/api';

interface FormData extends VulnerabilityReport {
  ai_model?: string;
  include_reasoning: boolean;
  include_recommendations: boolean;
}

const VulnerabilityAnalysis: React.FC = () => {
  const navigate = useNavigate();
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    reset,
  } = useForm<FormData>({
    defaultValues: {
      include_reasoning: true,
      include_recommendations: true,
      severity: 'medium',
      tags: [],
    },
  });

  // AI 분석 뮤테이션
  const analysisMutation = useMutation(analysisApi.analyzeVulnerability, {
    onSuccess: (response) => {
      const { data } = response;
      if (data.success) {
        toast.success('분석이 시작되었습니다!');
        if (data.analysis_id) {
          navigate(`/results/${data.analysis_id}`);
        } else {
          navigate('/results');
        }
      } else {
        toast.error(data.message || '분석 요청에 실패했습니다.');
      }
      setIsAnalyzing(false);
    },
    onError: (error: any) => {
      console.error('Analysis error:', error);
      toast.error(error.response?.data?.detail || '분석 중 오류가 발생했습니다.');
      setIsAnalyzing(false);
    },
  });

  const onSubmit = async (data: FormData) => {
    setIsAnalyzing(true);
    
    const analysisRequest: AnalysisRequest = {
      report: {
        title: data.title,
        description: data.description,
        severity: data.severity,
        affected_system: data.affected_system,
        vulnerability_type: data.vulnerability_type,
        attack_vector: data.attack_vector,
        impact: data.impact,
        steps_to_reproduce: data.steps_to_reproduce,
        proof_of_concept: data.proof_of_concept,
        reporter: data.reporter,
        tags: data.tags || [],
      },
      ai_model: data.ai_model,
      include_reasoning: data.include_reasoning,
      include_recommendations: data.include_recommendations,
    };

    analysisMutation.mutate(analysisRequest);
  };

  const severityOptions = [
    { value: 'critical', label: 'Critical', color: 'text-red-600' },
    { value: 'high', label: 'High', color: 'text-orange-600' },
    { value: 'medium', label: 'Medium', color: 'text-yellow-600' },
    { value: 'low', label: 'Low', color: 'text-blue-600' },
    { value: 'info', label: 'Info', color: 'text-gray-600' },
  ];

  const aiModelOptions = [
    { value: '', label: '자동 선택 (권장)' },
    { value: 'gpt-4', label: 'GPT-4' },
    { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
    { value: 'ollama', label: 'Ollama (로컬)' },
    { value: 'custom', label: '커스텀 모델' },
  ];

  return (
    <div className="max-w-4xl mx-auto">
      {/* 헤더 */}
      <div className="mb-8">
        <div className="flex items-center">
          <BeakerIcon className="h-8 w-8 text-primary-600 mr-3" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">취약점 분석</h1>
            <p className="mt-1 text-sm text-gray-500">
              AI를 활용하여 취약점을 자동으로 분석하고 분류합니다.
            </p>
          </div>
        </div>
      </div>

      {/* 분석 폼 */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-medium text-gray-900 flex items-center">
              <DocumentTextIcon className="h-5 w-5 mr-2" />
              취약점 정보
            </h2>
          </div>
          <div className="card-body space-y-6">
            {/* 제목 */}
            <div>
              <label className="form-label">
                제목 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                className="form-input"
                placeholder="취약점 제목을 입력하세요"
                {...register('title', { 
                  required: '제목은 필수입니다',
                  minLength: { value: 5, message: '제목은 최소 5자 이상이어야 합니다' }
                })}
              />
              {errors.title && (
                <p className="form-error">{errors.title.message}</p>
              )}
            </div>

            {/* 설명 */}
            <div>
              <label className="form-label">
                상세 설명 <span className="text-red-500">*</span>
              </label>
              <textarea
                rows={6}
                className="form-textarea"
                placeholder="취약점에 대한 상세한 설명을 입력하세요..."
                {...register('description', { 
                  required: '설명은 필수입니다',
                  minLength: { value: 20, message: '설명은 최소 20자 이상이어야 합니다' }
                })}
              />
              {errors.description && (
                <p className="form-error">{errors.description.message}</p>
              )}
            </div>

            {/* 심각도 및 영향받는 시스템 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="form-label">심각도 (예상)</label>
                <select className="form-select" {...register('severity')}>
                  {severityOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="form-label">영향받는 시스템</label>
                <input
                  type="text"
                  className="form-input"
                  placeholder="예: 웹 애플리케이션, API 서버"
                  {...register('affected_system')}
                />
              </div>
            </div>

            {/* 취약점 유형 및 공격 벡터 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="form-label">취약점 유형</label>
                <input
                  type="text"
                  className="form-input"
                  placeholder="예: XSS, SQL Injection, CSRF"
                  {...register('vulnerability_type')}
                />
              </div>
              <div>
                <label className="form-label">공격 벡터</label>
                <input
                  type="text"
                  className="form-input"
                  placeholder="예: Network, Local, Physical"
                  {...register('attack_vector')}
                />
              </div>
            </div>

            {/* 영향도 */}
            <div>
              <label className="form-label">영향도</label>
              <textarea
                rows={3}
                className="form-textarea"
                placeholder="이 취약점이 시스템에 미치는 영향을 설명하세요..."
                {...register('impact')}
              />
            </div>

            {/* 재현 단계 */}
            <div>
              <label className="form-label">재현 단계</label>
              <textarea
                rows={4}
                className="form-textarea"
                placeholder="취약점을 재현하는 단계별 방법을 설명하세요..."
                {...register('steps_to_reproduce')}
              />
            </div>

            {/* 개념 증명 */}
            <div>
              <label className="form-label">개념 증명 (PoC)</label>
              <textarea
                rows={4}
                className="form-textarea"
                placeholder="페이로드, 스크립트, 또는 증명 코드를 입력하세요..."
                {...register('proof_of_concept')}
              />
            </div>

            {/* 보고자 */}
            <div>
              <label className="form-label">보고자</label>
              <input
                type="text"
                className="form-input"
                placeholder="보고자 이름 또는 ID"
                {...register('reporter')}
              />
            </div>
          </div>
        </div>

        {/* AI 분석 설정 */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-medium text-gray-900 flex items-center">
              <BeakerIcon className="h-5 w-5 mr-2" />
              AI 분석 설정
            </h2>
          </div>
          <div className="card-body space-y-6">
            {/* AI 모델 선택 */}
            <div>
              <label className="form-label">AI 모델</label>
              <select className="form-select" {...register('ai_model')}>
                {aiModelOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <p className="mt-1 text-sm text-gray-500">
                자동 선택 시 가장 적합한 모델이 사용됩니다.
              </p>
            </div>

            {/* 분석 옵션 */}
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  {...register('include_reasoning')}
                />
                <label className="ml-2 block text-sm text-gray-900">
                  분석 근거 포함
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  {...register('include_recommendations')}
                />
                <label className="ml-2 block text-sm text-gray-900">
                  보안 권장사항 포함
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* 제출 버튼 */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            className="btn-outline"
            onClick={() => reset()}
            disabled={isAnalyzing}
          >
            초기화
          </button>
          <button
            type="submit"
            className="btn-primary"
            disabled={isAnalyzing}
          >
            {isAnalyzing ? (
              <>
                <div className="loading-spinner mr-2" />
                분석 중...
              </>
            ) : (
              <>
                <BeakerIcon className="h-4 w-4 mr-2" />
                AI 분석 시작
              </>
            )}
          </button>
        </div>
      </form>

      {/* 도움말 */}
      <div className="mt-8 card">
        <div className="card-body">
          <div className="flex">
            <InformationCircleIcon className="h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h3 className="text-sm font-medium text-gray-900">분석 팁</h3>
              <div className="mt-2 text-sm text-gray-500">
                <ul className="list-disc list-inside space-y-1">
                  <li>상세한 설명과 재현 단계를 제공할수록 더 정확한 분석 결과를 얻을 수 있습니다.</li>
                  <li>개념 증명(PoC) 코드나 페이로드를 포함하면 분류 정확도가 향상됩니다.</li>
                  <li>분석은 보통 1-3분 정도 소요되며, 복잡한 취약점의 경우 더 오래 걸릴 수 있습니다.</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VulnerabilityAnalysis;
