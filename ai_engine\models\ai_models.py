"""
AI 모델 관리 클래스들
"""

import os
import json
import httpx
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

@dataclass
class AIResponse:
    """AI 응답 데이터 클래스"""
    content: str
    confidence: float
    model_used: str
    tokens_used: Optional[int] = None
    processing_time: Optional[float] = None

class BaseAIModel(ABC):
    """AI 모델 기본 클래스"""
    
    def __init__(self, model_name: str, config: Dict[str, Any]):
        self.model_name = model_name
        self.config = config
    
    @abstractmethod
    async def generate_response(self, prompt: str, **kwargs) -> AIResponse:
        """응답 생성"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """모델 사용 가능 여부 확인"""
        pass

class OpenAIModel(BaseAIModel):
    """OpenAI GPT 모델"""
    
    def __init__(self, model_name: str = "gpt-4", config: Optional[Dict[str, Any]] = None):
        super().__init__(model_name, config or {})
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.base_url = "https://api.openai.com/v1"
    
    async def generate_response(self, prompt: str, **kwargs) -> AIResponse:
        """OpenAI API를 통한 응답 생성"""
        if not self.api_key:
            raise ValueError("OpenAI API 키가 설정되지 않았습니다")
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model_name,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": kwargs.get("temperature", 0.3),
            "max_tokens": kwargs.get("max_tokens", 2000)
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=60.0
            )
            
            if response.status_code != 200:
                raise Exception(f"OpenAI API 오류: {response.status_code} - {response.text}")
            
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            tokens_used = result.get("usage", {}).get("total_tokens")
            
            return AIResponse(
                content=content,
                confidence=0.8,  # OpenAI는 신뢰도를 직접 제공하지 않음
                model_used=self.model_name,
                tokens_used=tokens_used
            )
    
    def is_available(self) -> bool:
        """OpenAI 모델 사용 가능 여부"""
        return bool(self.api_key)

class OllamaModel(BaseAIModel):
    """Ollama 로컬 LLM 모델"""
    
    def __init__(self, model_name: str = "llama2", config: Optional[Dict[str, Any]] = None):
        super().__init__(model_name, config or {})
        self.base_url = os.getenv("OLLAMA_HOST", "http://localhost:11434")
    
    async def generate_response(self, prompt: str, **kwargs) -> AIResponse:
        """Ollama API를 통한 응답 생성"""
        data = {
            "model": self.model_name,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": kwargs.get("temperature", 0.3),
                "num_predict": kwargs.get("max_tokens", 2000)
            }
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/generate",
                json=data,
                timeout=120.0
            )
            
            if response.status_code != 200:
                raise Exception(f"Ollama API 오류: {response.status_code} - {response.text}")
            
            result = response.json()
            content = result.get("response", "")
            
            return AIResponse(
                content=content,
                confidence=0.7,  # Ollama 기본 신뢰도
                model_used=self.model_name
            )
    
    def is_available(self) -> bool:
        """Ollama 모델 사용 가능 여부 확인"""
        try:
            import httpx
            with httpx.Client() as client:
                response = client.get(f"{self.base_url}/api/tags", timeout=5.0)
                if response.status_code == 200:
                    models = response.json().get("models", [])
                    return any(model["name"].startswith(self.model_name) for model in models)
            return False
        except:
            return False

class CustomAIModel(BaseAIModel):
    """커스텀 AI API 모델"""
    
    def __init__(self, model_name: str = "custom", config: Optional[Dict[str, Any]] = None):
        super().__init__(model_name, config or {})
        self.api_url = os.getenv("CUSTOM_AI_API_URL")
        self.api_key = os.getenv("CUSTOM_AI_API_KEY")
    
    async def generate_response(self, prompt: str, **kwargs) -> AIResponse:
        """커스텀 AI API를 통한 응답 생성"""
        if not self.api_url:
            raise ValueError("커스텀 AI API URL이 설정되지 않았습니다")
        
        headers = {"Content-Type": "application/json"}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
        
        data = {
            "prompt": prompt,
            "model": self.model_name,
            "temperature": kwargs.get("temperature", 0.3),
            "max_tokens": kwargs.get("max_tokens", 2000)
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                self.api_url,
                headers=headers,
                json=data,
                timeout=60.0
            )
            
            if response.status_code != 200:
                raise Exception(f"커스텀 AI API 오류: {response.status_code} - {response.text}")
            
            result = response.json()
            content = result.get("response", result.get("content", ""))
            confidence = result.get("confidence", 0.6)
            
            return AIResponse(
                content=content,
                confidence=confidence,
                model_used=self.model_name
            )
    
    def is_available(self) -> bool:
        """커스텀 AI 모델 사용 가능 여부"""
        return bool(self.api_url)

class AIModelManager:
    """AI 모델 관리자"""
    
    def __init__(self):
        self.models: Dict[str, BaseAIModel] = {}
        self._initialize_models()
    
    def _initialize_models(self):
        """사용 가능한 모델들 초기화"""
        # OpenAI 모델들
        if os.getenv("OPENAI_API_KEY"):
            self.models["gpt-4"] = OpenAIModel("gpt-4")
            self.models["gpt-3.5-turbo"] = OpenAIModel("gpt-3.5-turbo")
        
        # Ollama 모델들
        ollama_model = os.getenv("OLLAMA_MODEL", "llama2")
        self.models["ollama"] = OllamaModel(ollama_model)
        
        # 커스텀 모델
        if os.getenv("CUSTOM_AI_API_URL"):
            self.models["custom"] = CustomAIModel()
    
    def get_model(self, model_name: str) -> Optional[BaseAIModel]:
        """모델 인스턴스 반환"""
        return self.models.get(model_name)
    
    def get_available_models(self) -> List[str]:
        """사용 가능한 모델 목록 반환"""
        available = []
        for name, model in self.models.items():
            if model.is_available():
                available.append(name)
        return available
    
    def get_best_available_model(self) -> Optional[BaseAIModel]:
        """가장 좋은 사용 가능한 모델 반환"""
        # 우선순위: GPT-4 > GPT-3.5 > Custom > Ollama
        priority_order = ["gpt-4", "gpt-3.5-turbo", "custom", "ollama"]
        
        for model_name in priority_order:
            model = self.models.get(model_name)
            if model and model.is_available():
                return model
        
        return None
    
    async def generate_response(self, prompt: str, model_name: Optional[str] = None, **kwargs) -> AIResponse:
        """응답 생성 (모델 자동 선택 또는 지정)"""
        if model_name:
            model = self.get_model(model_name)
            if not model:
                raise ValueError(f"모델을 찾을 수 없습니다: {model_name}")
            if not model.is_available():
                raise ValueError(f"모델을 사용할 수 없습니다: {model_name}")
        else:
            model = self.get_best_available_model()
            if not model:
                raise ValueError("사용 가능한 AI 모델이 없습니다")
        
        return await model.generate_response(prompt, **kwargs)
