{"name": "bugcrowd-ai-frontend", "version": "1.0.0", "description": "Bugcrowd AI 취약점 검증 툴 프론트엔드", "private": true, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^27.5.2", "@types/lodash": "^4.14.202", "@types/node": "^16.18.68", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "classnames": "^2.3.2", "date-fns": "^2.30.0", "lodash": "^4.17.21", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "recharts": "^2.8.0", "tailwindcss": "^3.3.6", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.3"}, "proxy": "http://localhost:8000"}