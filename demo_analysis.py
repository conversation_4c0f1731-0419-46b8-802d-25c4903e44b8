"""
AI 분석 엔진 데모 스크립트
실제 분석 과정을 보여줍니다
"""

import sys
import json
import asyncio
from pathlib import Path

# 백엔드 경로 추가
sys.path.insert(0, "backend")

async def demo_vulnerability_analysis():
    """취약점 분석 데모"""
    print("🛡️  Bugcrowd AI 취약점 분석 데모")
    print("=" * 50)
    
    # 1. 샘플 취약점 데이터
    sample_vulnerability = {
        "title": "Cross-Site Scripting in Search Form",
        "description": """
        The search functionality on the main page is vulnerable to Cross-Site Scripting (XSS) attacks.
        When a user enters malicious JavaScript code in the search box, it gets executed in the browser
        without proper sanitization or encoding.
        """,
        "steps_to_reproduce": """
        1. Navigate to the main search page
        2. Enter the following payload in the search box: <script>alert('XSS')</script>
        3. Submit the search form
        4. Observe that the JavaScript alert is executed
        """,
        "proof_of_concept": "<script>alert('XSS Vulnerability Found!')</script>",
        "impact": "Allows attackers to execute arbitrary JavaScript code in victim browsers",
        "affected_system": "Web Application - Search Module",
        "vulnerability_type": "Cross-Site Scripting",
        "attack_vector": "Network"
    }
    
    print("📝 입력된 취약점 정보:")
    print(f"제목: {sample_vulnerability['title']}")
    print(f"설명: {sample_vulnerability['description'][:100]}...")
    print()
    
    # 2. 텍스트 전처리
    print("🔍 1단계: 텍스트 전처리")
    try:
        from ai_engine.utils.text_processor import TextProcessor
        
        processor = TextProcessor()
        
        # 모든 텍스트 필드 결합
        combined_text = " ".join([
            sample_vulnerability.get("title", ""),
            sample_vulnerability.get("description", ""),
            sample_vulnerability.get("steps_to_reproduce", ""),
            sample_vulnerability.get("proof_of_concept", "")
        ])
        
        processed = processor.process_vulnerability_text(combined_text)
        
        print(f"✅ 원본 텍스트 길이: {len(processed.original)}")
        print(f"✅ 정제된 텍스트 길이: {len(processed.cleaned)}")
        print(f"✅ 추출된 키워드: {processed.keywords[:5]}...")
        print(f"✅ 복잡도 점수: {processed.metadata.get('complexity_score', 0):.2f}")
        print()
        
    except Exception as e:
        print(f"❌ 텍스트 전처리 실패: {e}")
        return
    
    # 3. 분류 체계 로드
    print("📚 2단계: 분류 체계 로드")
    try:
        from app.services.taxonomy_service import TaxonomyService
        
        taxonomy_service = TaxonomyService()
        taxonomy = taxonomy_service.get_taxonomy()
        
        if taxonomy and taxonomy.content:
            print(f"✅ 분류 체계 로드 성공: {len(taxonomy.content)}개 카테고리")
            
            # XSS 관련 카테고리 찾기
            xss_categories = [cat for cat in taxonomy.content if 'xss' in cat.name.lower() or 'script' in cat.name.lower()]
            if xss_categories:
                print(f"✅ XSS 관련 카테고리 발견: {xss_categories[0].name}")
        else:
            print("❌ 분류 체계 로드 실패")
            return
        print()
        
    except Exception as e:
        print(f"❌ 분류 체계 로드 실패: {e}")
        return
    
    # 4. 키워드 기반 분류 (AI 모델 없이)
    print("🤖 3단계: 키워드 기반 분류")
    try:
        from ai_engine.analyzer import VulnerabilityAnalyzer
        
        # 분류 체계 데이터 준비
        taxonomy_data = []
        for category in taxonomy.content[:10]:  # 상위 10개만
            taxonomy_data.append({
                'id': category.id,
                'name': category.name,
                'type': category.type,
                'priority': category.priority
            })
        
        analyzer = VulnerabilityAnalyzer(taxonomy_data)
        
        # 키워드 기반 분류
        classification = analyzer._classify_by_keywords(processed.keywords)
        print(f"✅ 예측된 분류: {classification['name']}")
        print(f"✅ 신뢰도: {classification['confidence_score']:.2f}")
        
        # 심각도 추정
        severity = analyzer._estimate_severity_by_keywords(processed.keywords)
        print(f"✅ 예측된 심각도: {severity}")
        print()
        
    except Exception as e:
        print(f"❌ 키워드 기반 분류 실패: {e}")
        return
    
    # 5. 자동 검증 시뮬레이션
    print("✅ 4단계: 자동 검증")
    try:
        # 신뢰도 기반 검증 점수 계산
        verification_score = 0.0
        verification_notes = []
        
        # 키워드 일치도
        if 'xss' in [k.lower() for k in processed.keywords]:
            verification_score += 0.3
            verification_notes.append("XSS 키워드 발견")
        
        if 'script' in [k.lower() for k in processed.keywords]:
            verification_score += 0.2
            verification_notes.append("Script 키워드 발견")
        
        # 분류 신뢰도
        if classification['confidence_score'] >= 0.6:
            verification_score += 0.3
            verification_notes.append("높은 분류 신뢰도")
        
        # 텍스트 품질
        if processed.metadata.get('complexity_score', 0) >= 0.5:
            verification_score += 0.2
            verification_notes.append("충분한 텍스트 품질")
        
        auto_verified = verification_score >= 0.7
        
        print(f"✅ 검증 점수: {verification_score:.2f}")
        print(f"✅ 자동 검증: {'통과' if auto_verified else '수동 검토 필요'}")
        print(f"✅ 검증 근거: {', '.join(verification_notes)}")
        print()
        
    except Exception as e:
        print(f"❌ 자동 검증 실패: {e}")
        return
    
    # 6. 최종 결과
    print("📊 최종 분석 결과")
    print("=" * 30)
    
    result = {
        "vulnerability_title": sample_vulnerability["title"],
        "predicted_category": {
            "name": classification["name"],
            "confidence": classification["confidence_score"]
        },
        "predicted_severity": severity,
        "keywords_found": processed.keywords[:5],
        "verification": {
            "auto_verified": auto_verified,
            "score": verification_score,
            "notes": verification_notes
        },
        "recommendations": [
            "입력 데이터 검증 및 정제 구현",
            "출력 데이터 HTML 인코딩 적용",
            "Content Security Policy (CSP) 헤더 설정",
            "XSS 방지 라이브러리 사용"
        ]
    }
    
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    return result

async def demo_api_workflow():
    """API 워크플로우 데모"""
    print("\n🌐 API 워크플로우 데모")
    print("=" * 50)
    
    try:
        import httpx
        
        # 1. 헬스 체크
        print("1. 백엔드 서버 상태 확인...")
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8000/health")
            if response.status_code == 200:
                print("✅ 백엔드 서버 정상 작동")
            else:
                print("❌ 백엔드 서버 오류")
                return
        
        # 2. 분류 체계 조회
        print("2. 분류 체계 통계 조회...")
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8000/api/v1/reports/taxonomy/statistics")
            if response.status_code == 200:
                stats = response.json()
                print(f"✅ 총 카테고리: {stats.get('total_categories', 0)}개")
                print(f"✅ 총 하위 카테고리: {stats.get('total_subcategories', 0)}개")
            else:
                print("❌ 분류 체계 조회 실패")
        
        # 3. 분석 요청 시뮬레이션 (실제 AI 호출 없이)
        print("3. 분석 요청 시뮬레이션...")
        sample_request = {
            "report": {
                "title": "XSS in search form",
                "description": "Cross-site scripting vulnerability in search functionality"
            },
            "ai_model": "gpt-4",
            "include_reasoning": True,
            "include_recommendations": True
        }
        
        print("✅ 분석 요청 데이터 준비 완료")
        print(f"   - 제목: {sample_request['report']['title']}")
        print(f"   - AI 모델: {sample_request['ai_model']}")
        
    except Exception as e:
        print(f"❌ API 워크플로우 데모 실패: {e}")

async def main():
    """메인 함수"""
    try:
        # 1. 분석 엔진 데모
        await demo_vulnerability_analysis()
        
        # 2. API 워크플로우 데모
        await demo_api_workflow()
        
        print("\n🎉 데모 완료!")
        print("\n📖 사용법:")
        print("1. 웹 브라우저에서 http://localhost:3000 접속")
        print("2. '취약점 분석' 메뉴에서 취약점 정보 입력")
        print("3. 'AI 분석 시작' 버튼 클릭")
        print("4. '분석 결과' 메뉴에서 결과 확인")
        
    except Exception as e:
        print(f"❌ 데모 실행 중 오류: {e}")

if __name__ == "__main__":
    asyncio.run(main())
