"""
FastAPI 메인 애플리케이션
"""

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import os
from dotenv import load_dotenv

from app.api import vulnerability, analysis, reports
from app.utils.logger import setup_logger

# 환경 변수 로드
load_dotenv()

# 로거 설정
logger = setup_logger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """애플리케이션 시작/종료 시 실행되는 함수"""
    logger.info("애플리케이션 시작")
    yield
    logger.info("애플리케이션 종료")

# FastAPI 앱 생성
app = FastAPI(
    title="Bugcrowd AI 취약점 검증 툴",
    description="AI를 활용한 자동 취약점 검증 시스템",
    version="1.0.0",
    lifespan=lifespan
)

# CORS 설정
allowed_origins = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000").split(",")
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 정적 파일 서빙
if os.path.exists("static"):
    app.mount("/static", StaticFiles(directory="static"), name="static")

# API 라우터 등록
app.include_router(vulnerability.router, prefix="/api/v1/vulnerability", tags=["vulnerability"])
app.include_router(analysis.router, prefix="/api/v1/analysis", tags=["analysis"])
app.include_router(reports.router, prefix="/api/v1/reports", tags=["reports"])

@app.get("/")
async def root():
    """루트 엔드포인트"""
    return {
        "message": "Bugcrowd AI 취약점 검증 툴 API",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """헬스 체크 엔드포인트"""
    return {"status": "healthy", "message": "서비스가 정상적으로 실행 중입니다."}

if __name__ == "__main__":
    import uvicorn
    
    host = os.getenv("BACKEND_HOST", "localhost")
    port = int(os.getenv("BACKEND_PORT", 8000))
    debug = os.getenv("DEBUG", "false").lower() == "true"
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info"
    )
