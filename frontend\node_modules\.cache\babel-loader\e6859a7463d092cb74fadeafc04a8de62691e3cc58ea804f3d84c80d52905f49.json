{"ast": null, "code": "// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nexport class Adder {\n  constructor() {\n    this._partials = new Float64Array(32);\n    this._n = 0;\n  }\n  add(x) {\n    const p = this._partials;\n    let i = 0;\n    for (let j = 0; j < this._n && j < 32; j++) {\n      const y = p[j],\n        hi = x + y,\n        lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n      if (lo) p[i++] = lo;\n      x = hi;\n    }\n    p[i] = x;\n    this._n = i + 1;\n    return this;\n  }\n  valueOf() {\n    const p = this._partials;\n    let n = this._n,\n      x,\n      y,\n      lo,\n      hi = 0;\n    if (n > 0) {\n      hi = p[--n];\n      while (n > 0) {\n        x = hi;\n        y = p[--n];\n        hi = x + y;\n        lo = y - (hi - x);\n        if (lo) break;\n      }\n      if (n > 0 && (lo < 0 && p[n - 1] < 0 || lo > 0 && p[n - 1] > 0)) {\n        y = lo * 2;\n        x = hi + y;\n        if (y == x - hi) hi = x;\n      }\n    }\n    return hi;\n  }\n}\nexport function fsum(values, valueof) {\n  const adder = new Adder();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        adder.add(value);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        adder.add(value);\n      }\n    }\n  }\n  return +adder;\n}\nexport function fcumsum(values, valueof) {\n  const adder = new Adder();\n  let index = -1;\n  return Float64Array.from(values, valueof === undefined ? v => adder.add(+v || 0) : v => adder.add(+valueof(v, ++index, values) || 0));\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "constructor", "_partials", "Float64Array", "_n", "add", "x", "p", "i", "j", "y", "hi", "lo", "Math", "abs", "valueOf", "n", "fsum", "values", "valueof", "adder", "undefined", "value", "index", "fcumsum", "from", "v"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/node_modules/d3-array/src/fsum.js"], "sourcesContent": ["// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nexport class Adder {\n  constructor() {\n    this._partials = new Float64Array(32);\n    this._n = 0;\n  }\n  add(x) {\n    const p = this._partials;\n    let i = 0;\n    for (let j = 0; j < this._n && j < 32; j++) {\n      const y = p[j],\n        hi = x + y,\n        lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n      if (lo) p[i++] = lo;\n      x = hi;\n    }\n    p[i] = x;\n    this._n = i + 1;\n    return this;\n  }\n  valueOf() {\n    const p = this._partials;\n    let n = this._n, x, y, lo, hi = 0;\n    if (n > 0) {\n      hi = p[--n];\n      while (n > 0) {\n        x = hi;\n        y = p[--n];\n        hi = x + y;\n        lo = y - (hi - x);\n        if (lo) break;\n      }\n      if (n > 0 && ((lo < 0 && p[n - 1] < 0) || (lo > 0 && p[n - 1] > 0))) {\n        y = lo * 2;\n        x = hi + y;\n        if (y == x - hi) hi = x;\n      }\n    }\n    return hi;\n  }\n}\n\nexport function fsum(values, valueof) {\n  const adder = new Adder();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        adder.add(value);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        adder.add(value);\n      }\n    }\n  }\n  return +adder;\n}\n\nexport function fcumsum(values, valueof) {\n  const adder = new Adder();\n  let index = -1;\n  return Float64Array.from(values, valueof === undefined\n      ? v => adder.add(+v || 0)\n      : v => adder.add(+valueof(v, ++index, values) || 0)\n  );\n}\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,KAAK,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,SAAS,GAAG,IAAIC,YAAY,CAAC,EAAE,CAAC;IACrC,IAAI,CAACC,EAAE,GAAG,CAAC;EACb;EACAC,GAAGA,CAACC,CAAC,EAAE;IACL,MAAMC,CAAC,GAAG,IAAI,CAACL,SAAS;IACxB,IAAIM,CAAC,GAAG,CAAC;IACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACL,EAAE,IAAIK,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC1C,MAAMC,CAAC,GAAGH,CAAC,CAACE,CAAC,CAAC;QACZE,EAAE,GAAGL,CAAC,GAAGI,CAAC;QACVE,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACR,CAAC,CAAC,GAAGO,IAAI,CAACC,GAAG,CAACJ,CAAC,CAAC,GAAGJ,CAAC,IAAIK,EAAE,GAAGD,CAAC,CAAC,GAAGA,CAAC,IAAIC,EAAE,GAAGL,CAAC,CAAC;MAC9D,IAAIM,EAAE,EAAEL,CAAC,CAACC,CAAC,EAAE,CAAC,GAAGI,EAAE;MACnBN,CAAC,GAAGK,EAAE;IACR;IACAJ,CAAC,CAACC,CAAC,CAAC,GAAGF,CAAC;IACR,IAAI,CAACF,EAAE,GAAGI,CAAC,GAAG,CAAC;IACf,OAAO,IAAI;EACb;EACAO,OAAOA,CAAA,EAAG;IACR,MAAMR,CAAC,GAAG,IAAI,CAACL,SAAS;IACxB,IAAIc,CAAC,GAAG,IAAI,CAACZ,EAAE;MAAEE,CAAC;MAAEI,CAAC;MAAEE,EAAE;MAAED,EAAE,GAAG,CAAC;IACjC,IAAIK,CAAC,GAAG,CAAC,EAAE;MACTL,EAAE,GAAGJ,CAAC,CAAC,EAAES,CAAC,CAAC;MACX,OAAOA,CAAC,GAAG,CAAC,EAAE;QACZV,CAAC,GAAGK,EAAE;QACND,CAAC,GAAGH,CAAC,CAAC,EAAES,CAAC,CAAC;QACVL,EAAE,GAAGL,CAAC,GAAGI,CAAC;QACVE,EAAE,GAAGF,CAAC,IAAIC,EAAE,GAAGL,CAAC,CAAC;QACjB,IAAIM,EAAE,EAAE;MACV;MACA,IAAII,CAAC,GAAG,CAAC,KAAMJ,EAAE,GAAG,CAAC,IAAIL,CAAC,CAACS,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAMJ,EAAE,GAAG,CAAC,IAAIL,CAAC,CAACS,CAAC,GAAG,CAAC,CAAC,GAAG,CAAE,CAAC,EAAE;QACnEN,CAAC,GAAGE,EAAE,GAAG,CAAC;QACVN,CAAC,GAAGK,EAAE,GAAGD,CAAC;QACV,IAAIA,CAAC,IAAIJ,CAAC,GAAGK,EAAE,EAAEA,EAAE,GAAGL,CAAC;MACzB;IACF;IACA,OAAOK,EAAE;EACX;AACF;AAEA,OAAO,SAASM,IAAIA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACpC,MAAMC,KAAK,GAAG,IAAIpB,KAAK,CAAC,CAAC;EACzB,IAAImB,OAAO,KAAKE,SAAS,EAAE;IACzB,KAAK,IAAIC,KAAK,IAAIJ,MAAM,EAAE;MACxB,IAAII,KAAK,GAAG,CAACA,KAAK,EAAE;QAClBF,KAAK,CAACf,GAAG,CAACiB,KAAK,CAAC;MAClB;IACF;EACF,CAAC,MAAM;IACL,IAAIC,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAID,KAAK,IAAIJ,MAAM,EAAE;MACxB,IAAII,KAAK,GAAG,CAACH,OAAO,CAACG,KAAK,EAAE,EAAEC,KAAK,EAAEL,MAAM,CAAC,EAAE;QAC5CE,KAAK,CAACf,GAAG,CAACiB,KAAK,CAAC;MAClB;IACF;EACF;EACA,OAAO,CAACF,KAAK;AACf;AAEA,OAAO,SAASI,OAAOA,CAACN,MAAM,EAAEC,OAAO,EAAE;EACvC,MAAMC,KAAK,GAAG,IAAIpB,KAAK,CAAC,CAAC;EACzB,IAAIuB,KAAK,GAAG,CAAC,CAAC;EACd,OAAOpB,YAAY,CAACsB,IAAI,CAACP,MAAM,EAAEC,OAAO,KAAKE,SAAS,GAChDK,CAAC,IAAIN,KAAK,CAACf,GAAG,CAAC,CAACqB,CAAC,IAAI,CAAC,CAAC,GACvBA,CAAC,IAAIN,KAAK,CAACf,GAAG,CAAC,CAACc,OAAO,CAACO,CAAC,EAAE,EAAEH,KAAK,EAAEL,MAAM,CAAC,IAAI,CAAC,CACtD,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}