{"ast": null, "code": "import variance from \"./variance.js\";\nexport default function deviation(values, valueof) {\n  const v = variance(values, valueof);\n  return v ? Math.sqrt(v) : v;\n}", "map": {"version": 3, "names": ["variance", "deviation", "values", "valueof", "v", "Math", "sqrt"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/node_modules/d3-array/src/deviation.js"], "sourcesContent": ["import variance from \"./variance.js\";\n\nexport default function deviation(values, valueof) {\n  const v = variance(values, valueof);\n  return v ? Math.sqrt(v) : v;\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AAEpC,eAAe,SAASC,SAASA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACjD,MAAMC,CAAC,GAAGJ,QAAQ,CAACE,MAAM,EAAEC,OAAO,CAAC;EACnC,OAAOC,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACF,CAAC,CAAC,GAAGA,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}