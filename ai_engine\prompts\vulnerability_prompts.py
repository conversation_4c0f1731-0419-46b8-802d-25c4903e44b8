"""
취약점 분석을 위한 프롬프트 템플릿
"""

from typing import Dict, Any, List, Optional
import json

class VulnerabilityPromptManager:
    """취약점 분석 프롬프트 관리자"""
    
    def __init__(self):
        self.base_system_prompt = self._get_base_system_prompt()
        self.classification_prompt = self._get_classification_prompt()
        self.severity_prompt = self._get_severity_prompt()
        self.recommendation_prompt = self._get_recommendation_prompt()
    
    def _get_base_system_prompt(self) -> str:
        """기본 시스템 프롬프트"""
        return """당신은 사이버보안 전문가이며 Bugcrowd의 취약점 분류 체계를 완벽히 이해하고 있습니다.
주어진 취약점 보고서를 분석하여 정확한 분류와 평가를 제공해야 합니다.

분석 시 다음 사항을 고려하세요:
1. 취약점의 기술적 특성과 공격 벡터
2. 영향도와 악용 가능성
3. Bugcrowd 분류 체계에 따른 정확한 카테고리 매칭
4. 우선순위와 심각도 평가
5. 실용적인 보안 권장사항

응답은 항상 JSON 형식으로 제공하며, 분석 근거를 명확히 설명해야 합니다."""

    def _get_classification_prompt(self) -> str:
        """분류 분석 프롬프트"""
        return """다음 취약점 보고서를 Bugcrowd 분류 체계에 따라 분석하고 분류하세요.

취약점 보고서:
제목: {title}
설명: {description}
영향받는 시스템: {affected_system}
공격 벡터: {attack_vector}
재현 단계: {steps_to_reproduce}

Bugcrowd 분류 체계 (주요 카테고리):
{taxonomy_categories}

다음 JSON 형식으로 응답하세요:
{{
    "primary_category": {{
        "id": "카테고리_ID",
        "name": "카테고리_이름",
        "type": "category|subcategory|variant",
        "confidence_score": 0.0-1.0
    }},
    "secondary_categories": [
        {{
            "id": "카테고리_ID",
            "name": "카테고리_이름", 
            "type": "category|subcategory|variant",
            "confidence_score": 0.0-1.0
        }}
    ],
    "reasoning": "분류 근거 설명",
    "keywords_found": ["발견된_키워드들"],
    "analysis_confidence": 0.0-1.0
}}"""

    def _get_severity_prompt(self) -> str:
        """심각도 평가 프롬프트"""
        return """다음 취약점의 심각도를 평가하세요.

취약점 정보:
제목: {title}
설명: {description}
분류: {category}
영향도: {impact}

심각도 기준:
- CRITICAL: 즉시 시스템 전체 침해 가능, 원격 코드 실행, 민감 데이터 대량 유출
- HIGH: 시스템 일부 침해, 권한 상승, 중요 데이터 접근
- MEDIUM: 제한적 영향, 정보 노출, 서비스 거부
- LOW: 최소한의 영향, 정보 수집, 설정 오류
- INFO: 보안 위험 없음, 정보성 발견

다음 JSON 형식으로 응답하세요:
{{
    "severity": "critical|high|medium|low|info",
    "confidence": 0.0-1.0,
    "reasoning": "심각도 평가 근거",
    "impact_analysis": {{
        "confidentiality": "none|low|medium|high",
        "integrity": "none|low|medium|high", 
        "availability": "none|low|medium|high"
    }},
    "exploitability": {{
        "attack_complexity": "low|medium|high",
        "privileges_required": "none|low|high",
        "user_interaction": "none|required"
    }}
}}"""

    def _get_recommendation_prompt(self) -> str:
        """권장사항 생성 프롬프트"""
        return """다음 취약점에 대한 구체적이고 실행 가능한 보안 권장사항을 제공하세요.

취약점 정보:
제목: {title}
설명: {description}
분류: {category}
심각도: {severity}

다음 JSON 형식으로 응답하세요:
{{
    "immediate_actions": [
        "즉시 수행해야 할 조치들"
    ],
    "short_term_fixes": [
        "단기적 해결 방안들"
    ],
    "long_term_improvements": [
        "장기적 보안 개선 사항들"
    ],
    "prevention_measures": [
        "재발 방지를 위한 조치들"
    ],
    "testing_recommendations": [
        "보안 테스트 권장사항들"
    ],
    "priority": "high|medium|low",
    "estimated_effort": "low|medium|high"
}}"""

    def create_full_analysis_prompt(self, vulnerability_data: Dict[str, Any], 
                                  taxonomy_categories: List[Dict[str, Any]]) -> str:
        """전체 분석을 위한 통합 프롬프트"""
        
        # 분류 체계 정보를 문자열로 변환
        taxonomy_str = self._format_taxonomy_for_prompt(taxonomy_categories)
        
        prompt = f"""{self.base_system_prompt}

다음 취약점 보고서를 종합적으로 분석하세요:

=== 취약점 보고서 ===
제목: {vulnerability_data.get('title', 'N/A')}
설명: {vulnerability_data.get('description', 'N/A')}
영향받는 시스템: {vulnerability_data.get('affected_system', 'N/A')}
취약점 유형: {vulnerability_data.get('vulnerability_type', 'N/A')}
공격 벡터: {vulnerability_data.get('attack_vector', 'N/A')}
영향도: {vulnerability_data.get('impact', 'N/A')}
재현 단계: {vulnerability_data.get('steps_to_reproduce', 'N/A')}
개념 증명: {vulnerability_data.get('proof_of_concept', 'N/A')}

=== Bugcrowd 분류 체계 (주요 카테고리) ===
{taxonomy_str}

다음 JSON 형식으로 종합 분석 결과를 제공하세요:
{{
    "classification": {{
        "primary_category": {{
            "id": "카테고리_ID",
            "name": "카테고리_이름",
            "type": "category|subcategory|variant",
            "confidence_score": 0.0-1.0
        }},
        "secondary_categories": [
            {{
                "id": "카테고리_ID",
                "name": "카테고리_이름",
                "type": "category|subcategory|variant", 
                "confidence_score": 0.0-1.0
            }}
        ],
        "reasoning": "분류 근거"
    }},
    "severity_assessment": {{
        "severity": "critical|high|medium|low|info",
        "confidence": 0.0-1.0,
        "reasoning": "심각도 평가 근거",
        "cvss_estimation": {{
            "base_score": 0.0-10.0,
            "impact_score": 0.0-10.0,
            "exploitability_score": 0.0-10.0
        }}
    }},
    "recommendations": {{
        "immediate_actions": ["즉시 조치사항"],
        "short_term_fixes": ["단기 해결방안"],
        "long_term_improvements": ["장기 개선사항"],
        "prevention_measures": ["재발 방지책"]
    }},
    "analysis_metadata": {{
        "confidence": 0.0-1.0,
        "keywords_identified": ["키워드들"],
        "similar_vulnerabilities": ["유사 취약점들"],
        "analysis_notes": "추가 분석 노트"
    }}
}}"""
        
        return prompt

    def _format_taxonomy_for_prompt(self, taxonomy_categories: List[Dict[str, Any]]) -> str:
        """분류 체계를 프롬프트용 문자열로 포맷"""
        formatted_lines = []
        
        for category in taxonomy_categories[:20]:  # 상위 20개만 포함
            line = f"- {category.get('name', 'Unknown')} (ID: {category.get('id', 'unknown')})"
            if category.get('priority'):
                line += f" [우선순위: {category['priority']}]"
            formatted_lines.append(line)
        
        return "\n".join(formatted_lines)

    def create_batch_analysis_prompt(self, vulnerabilities: List[Dict[str, Any]]) -> str:
        """배치 분석을 위한 프롬프트"""
        prompt = f"""{self.base_system_prompt}

다음 {len(vulnerabilities)}개의 취약점들을 일괄 분석하세요:

"""
        
        for i, vuln in enumerate(vulnerabilities, 1):
            prompt += f"""
=== 취약점 #{i} ===
제목: {vuln.get('title', 'N/A')}
설명: {vuln.get('description', 'N/A')}
"""
        
        prompt += """
각 취약점에 대해 다음 JSON 배열 형식으로 응답하세요:
[
    {
        "vulnerability_index": 1,
        "classification": {...},
        "severity": "...",
        "confidence": 0.0-1.0
    },
    ...
]"""
        
        return prompt

    def create_verification_prompt(self, original_analysis: Dict[str, Any], 
                                 human_feedback: str) -> str:
        """검증 및 개선을 위한 프롬프트"""
        return f"""{self.base_system_prompt}

다음은 이전 AI 분석 결과입니다:
{json.dumps(original_analysis, ensure_ascii=False, indent=2)}

사람 전문가의 피드백:
{human_feedback}

피드백을 반영하여 분석을 개선하고, 다음 JSON 형식으로 수정된 분석을 제공하세요:
{{
    "revised_analysis": {{
        "classification": {{...}},
        "severity_assessment": {{...}},
        "recommendations": {{...}}
    }},
    "changes_made": [
        "변경사항 설명들"
    ],
    "learning_notes": "향후 분석 개선을 위한 학습 노트"
}}"""
