# API 가이드

Bugcrowd AI 취약점 검증 툴의 REST API 사용 가이드입니다.

## 기본 정보

- **Base URL**: `http://localhost:8000/api/v1`
- **Content-Type**: `application/json`
- **인증**: 현재 버전에서는 인증이 필요하지 않습니다

## 엔드포인트 개요

### 취약점 보고서 관리
- `POST /vulnerability/reports` - 새 보고서 생성
- `GET /vulnerability/reports` - 보고서 목록 조회
- `GET /vulnerability/reports/{id}` - 특정 보고서 조회
- `PUT /vulnerability/reports/{id}` - 보고서 업데이트
- `DELETE /vulnerability/reports/{id}` - 보고서 삭제

### AI 분석
- `POST /analysis/analyze` - 취약점 분석 요청
- `GET /analysis/results/{id}` - 분석 결과 조회
- `GET /analysis/results` - 모든 분석 결과 조회
- `PUT /analysis/results/{id}/verify` - 분석 결과 검증

### 리포트 및 분류 체계
- `GET /reports/taxonomy` - 전체 분류 체계 조회
- `GET /reports/taxonomy/categories` - 카테고리 목록
- `GET /reports/taxonomy/search` - 분류 체계 검색
- `GET /reports/analysis-summary` - 분석 요약 리포트

## 상세 API 문서

### 1. 취약점 분석 요청

**POST** `/analysis/analyze`

새로운 취약점에 대한 AI 분석을 요청합니다.

#### 요청 본문
```json
{
  "report": {
    "title": "XSS vulnerability in search form",
    "description": "The search form allows script injection through the query parameter",
    "severity": "medium",
    "affected_system": "Web Application",
    "vulnerability_type": "Cross-Site Scripting",
    "attack_vector": "Network",
    "impact": "Allows execution of arbitrary JavaScript code",
    "steps_to_reproduce": "1. Go to search page\n2. Enter <script>alert(1)</script>\n3. Submit form",
    "proof_of_concept": "<script>alert('XSS')</script>",
    "reporter": "Security Researcher",
    "tags": ["xss", "web", "javascript"]
  },
  "ai_model": "gpt-4",
  "include_reasoning": true,
  "include_recommendations": true
}
```

#### 응답
```json
{
  "success": true,
  "message": "분석 요청이 접수되었습니다",
  "analysis_id": "analysis_123456789",
  "result": {
    "id": "analysis_123456789",
    "report_id": "report_987654321",
    "status": "pending",
    "ai_model_used": "gpt-4",
    "started_at": "2024-01-15T10:30:00Z"
  }
}
```

### 2. 분석 결과 조회

**GET** `/analysis/results/{analysis_id}`

특정 분석의 결과를 조회합니다.

#### 응답
```json
{
  "id": "analysis_123456789",
  "report_id": "report_987654321",
  "status": "completed",
  "started_at": "2024-01-15T10:30:00Z",
  "completed_at": "2024-01-15T10:32:30Z",
  "predicted_categories": [
    {
      "id": "cross_site_scripting_xss",
      "name": "Cross-Site Scripting (XSS)",
      "type": "category",
      "confidence_score": 0.92
    }
  ],
  "primary_category": {
    "id": "cross_site_scripting_xss",
    "name": "Cross-Site Scripting (XSS)",
    "type": "category",
    "confidence_score": 0.92
  },
  "predicted_severity": "medium",
  "severity_confidence": 0.85,
  "ai_model_used": "gpt-4",
  "analysis_confidence": 0.88,
  "reasoning": "입력된 취약점 설명에서 XSS 관련 키워드와 패턴이 발견되었습니다. 스크립트 태그를 통한 코드 실행이 가능한 전형적인 XSS 취약점입니다.",
  "recommendations": [
    "입력 데이터 검증 강화",
    "출력 데이터 이스케이프 처리",
    "Content Security Policy 적용",
    "XSS 필터링 라이브러리 사용"
  ],
  "is_verified": true,
  "verified_by": "AI_AUTO_VERIFICATION",
  "verified_at": "2024-01-15T10:32:30Z",
  "verification_notes": "자동 검증 점수: 0.82 - 높은 AI 신뢰도 (≥80%); 키워드 일치도 높음; 분류 일관성 높음"
}
```

### 3. 분류 체계 검색

**GET** `/reports/taxonomy/search?q={query}`

분류 체계에서 특정 키워드를 검색합니다.

#### 쿼리 파라미터
- `q` (required): 검색어 (최소 2자)
- `case_sensitive` (optional): 대소문자 구분 여부 (기본값: false)

#### 응답
```json
{
  "query": "xss",
  "total_count": 3,
  "results": [
    {
      "id": "cross_site_scripting_xss",
      "name": "Cross-Site Scripting (XSS)",
      "type": "category",
      "priority": 3,
      "children": []
    },
    {
      "id": "stored_xss",
      "name": "Stored XSS",
      "type": "subcategory",
      "priority": 2,
      "children": []
    },
    {
      "id": "reflected_xss",
      "name": "Reflected XSS",
      "type": "subcategory",
      "priority": 3,
      "children": []
    }
  ]
}
```

### 4. 분석 요약 리포트

**GET** `/reports/analysis-summary`

지정된 기간의 분석 요약 정보를 조회합니다.

#### 쿼리 파라미터
- `start_date` (optional): 시작 날짜 (YYYY-MM-DD)
- `end_date` (optional): 종료 날짜 (YYYY-MM-DD)

#### 응답
```json
{
  "summary": {
    "total_analyses": 150,
    "completed_analyses": 142,
    "verified_analyses": 128,
    "completion_rate": 0.947,
    "verification_rate": 0.901
  },
  "distributions": {
    "categories": {
      "Cross-Site Scripting (XSS)": 45,
      "SQL Injection": 32,
      "Cross-Site Request Forgery (CSRF)": 28,
      "Authentication Bypass": 20,
      "Information Disclosure": 15,
      "기타": 10
    },
    "severities": {
      "critical": 8,
      "high": 35,
      "medium": 67,
      "low": 32,
      "info": 8
    }
  },
  "period": {
    "start_date": "2024-01-01",
    "end_date": "2024-01-31"
  }
}
```

## 오류 응답

API는 표준 HTTP 상태 코드를 사용합니다:

- `200` - 성공
- `400` - 잘못된 요청
- `404` - 리소스를 찾을 수 없음
- `500` - 내부 서버 오류

### 오류 응답 형식
```json
{
  "detail": {
    "message": "입력 데이터가 유효하지 않습니다",
    "errors": [
      "제목은 필수입니다",
      "설명은 최소 20자 이상이어야 합니다"
    ]
  }
}
```

## 사용 예시

### Python 예시
```python
import requests

# 분석 요청
analysis_request = {
    "report": {
        "title": "XSS in search form",
        "description": "Cross-site scripting vulnerability in search functionality",
        "steps_to_reproduce": "1. Go to search\n2. Enter <script>alert(1)</script>\n3. Submit"
    },
    "ai_model": "gpt-4",
    "include_reasoning": True,
    "include_recommendations": True
}

response = requests.post(
    "http://localhost:8000/api/v1/analysis/analyze",
    json=analysis_request
)

if response.status_code == 200:
    result = response.json()
    analysis_id = result["analysis_id"]
    print(f"분석 시작됨: {analysis_id}")
    
    # 결과 조회
    result_response = requests.get(
        f"http://localhost:8000/api/v1/analysis/results/{analysis_id}"
    )
    
    if result_response.status_code == 200:
        analysis_result = result_response.json()
        print(f"분석 상태: {analysis_result['status']}")
        if analysis_result['status'] == 'completed':
            print(f"주요 분류: {analysis_result['primary_category']['name']}")
            print(f"예측 심각도: {analysis_result['predicted_severity']}")
```

### JavaScript 예시
```javascript
// 분석 요청
const analysisRequest = {
  report: {
    title: "XSS in search form",
    description: "Cross-site scripting vulnerability in search functionality",
    steps_to_reproduce: "1. Go to search\n2. Enter <script>alert(1)</script>\n3. Submit"
  },
  ai_model: "gpt-4",
  include_reasoning: true,
  include_recommendations: true
};

fetch('http://localhost:8000/api/v1/analysis/analyze', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(analysisRequest)
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('분석 시작됨:', data.analysis_id);
    
    // 결과 조회
    return fetch(`http://localhost:8000/api/v1/analysis/results/${data.analysis_id}`);
  }
})
.then(response => response.json())
.then(result => {
  console.log('분석 상태:', result.status);
  if (result.status === 'completed') {
    console.log('주요 분류:', result.primary_category.name);
    console.log('예측 심각도:', result.predicted_severity);
  }
});
```

## 추가 정보

- 모든 날짜/시간은 ISO 8601 형식 (UTC)으로 반환됩니다
- 분석은 비동기적으로 처리되므로 결과 조회 시 상태를 확인해야 합니다
- 대용량 배치 분석의 경우 `/analysis/batch-analyze` 엔드포인트를 사용하세요
- API 사용량 제한은 현재 적용되지 않지만, 향후 버전에서 추가될 예정입니다
