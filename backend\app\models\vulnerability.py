"""
취약점 관련 데이터 모델
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

class SeverityLevel(str, Enum):
    """심각도 레벨"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"

class AnalysisStatus(str, Enum):
    """분석 상태"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"

class VulnerabilityCategory(BaseModel):
    """취약점 카테고리"""
    id: str = Field(..., description="카테고리 ID")
    name: str = Field(..., description="카테고리 이름")
    type: str = Field(..., description="카테고리 타입")
    priority: Optional[int] = Field(None, description="우선순위")
    confidence_score: float = Field(0.0, description="신뢰도 점수 (0.0-1.0)")

class VulnerabilityReport(BaseModel):
    """취약점 보고서"""
    id: Optional[str] = Field(None, description="보고서 ID")
    title: str = Field(..., description="취약점 제목")
    description: str = Field(..., description="취약점 설명")
    severity: Optional[SeverityLevel] = Field(None, description="심각도")
    
    # 기술적 세부사항
    affected_system: Optional[str] = Field(None, description="영향받는 시스템")
    vulnerability_type: Optional[str] = Field(None, description="취약점 유형")
    attack_vector: Optional[str] = Field(None, description="공격 벡터")
    impact: Optional[str] = Field(None, description="영향도")
    
    # 재현 정보
    steps_to_reproduce: Optional[str] = Field(None, description="재현 단계")
    proof_of_concept: Optional[str] = Field(None, description="개념 증명")
    
    # 메타데이터
    reporter: Optional[str] = Field(None, description="보고자")
    reported_date: Optional[datetime] = Field(None, description="보고 일시")
    tags: List[str] = Field(default_factory=list, description="태그")
    
    # 첨부 파일
    attachments: List[str] = Field(default_factory=list, description="첨부 파일 경로")
    
    class Config:
        use_enum_values = True

class AnalysisResult(BaseModel):
    """AI 분석 결과"""
    id: Optional[str] = Field(None, description="분석 결과 ID")
    report_id: str = Field(..., description="분석 대상 보고서 ID")
    
    # 분석 상태
    status: AnalysisStatus = Field(AnalysisStatus.PENDING, description="분석 상태")
    started_at: Optional[datetime] = Field(None, description="분석 시작 시간")
    completed_at: Optional[datetime] = Field(None, description="분석 완료 시간")
    
    # 분류 결과
    predicted_categories: List[VulnerabilityCategory] = Field(
        default_factory=list, 
        description="예측된 취약점 카테고리들"
    )
    primary_category: Optional[VulnerabilityCategory] = Field(
        None, 
        description="주요 카테고리"
    )
    
    # 심각도 분석
    predicted_severity: Optional[SeverityLevel] = Field(None, description="예측된 심각도")
    severity_confidence: float = Field(0.0, description="심각도 예측 신뢰도")
    
    # AI 분석 세부사항
    ai_model_used: str = Field(..., description="사용된 AI 모델")
    analysis_confidence: float = Field(0.0, description="전체 분석 신뢰도")
    reasoning: Optional[str] = Field(None, description="분석 근거")
    
    # 추가 정보
    recommendations: List[str] = Field(default_factory=list, description="권장사항")
    similar_cases: List[str] = Field(default_factory=list, description="유사 사례")
    
    # 검증 정보
    is_verified: bool = Field(False, description="검증 여부")
    verified_by: Optional[str] = Field(None, description="검증자")
    verified_at: Optional[datetime] = Field(None, description="검증 시간")
    verification_notes: Optional[str] = Field(None, description="검증 노트")
    
    class Config:
        use_enum_values = True

class AnalysisRequest(BaseModel):
    """분석 요청"""
    report: VulnerabilityReport = Field(..., description="분석할 취약점 보고서")
    ai_model: Optional[str] = Field(None, description="사용할 AI 모델")
    include_reasoning: bool = Field(True, description="분석 근거 포함 여부")
    include_recommendations: bool = Field(True, description="권장사항 포함 여부")

class AnalysisResponse(BaseModel):
    """분석 응답"""
    success: bool = Field(..., description="성공 여부")
    message: str = Field(..., description="응답 메시지")
    analysis_id: Optional[str] = Field(None, description="분석 ID")
    result: Optional[AnalysisResult] = Field(None, description="분석 결과")
