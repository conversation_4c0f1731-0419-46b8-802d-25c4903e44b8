{"ast": null, "code": "import { timeInterval } from \"./interval.js\";\nimport { durationHour, durationMinute, durationSecond } from \"./duration.js\";\nexport const timeHour = timeInterval(date => {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond - date.getMinutes() * durationMinute);\n}, (date, step) => {\n  date.setTime(+date + step * durationHour);\n}, (start, end) => {\n  return (end - start) / durationHour;\n}, date => {\n  return date.getHours();\n});\nexport const timeHours = timeHour.range;\nexport const utcHour = timeInterval(date => {\n  date.setUTCMinutes(0, 0, 0);\n}, (date, step) => {\n  date.setTime(+date + step * durationHour);\n}, (start, end) => {\n  return (end - start) / durationHour;\n}, date => {\n  return date.getUTCHours();\n});\nexport const utcHours = utcHour.range;", "map": {"version": 3, "names": ["timeInterval", "durationHour", "durationMinute", "durationSecond", "timeHour", "date", "setTime", "getMilliseconds", "getSeconds", "getMinutes", "step", "start", "end", "getHours", "timeHours", "range", "utcHour", "setUTCMinutes", "getUTCHours", "utcHours"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/node_modules/d3-time/src/hour.js"], "sourcesContent": ["import {timeInterval} from \"./interval.js\";\nimport {durationHour, durationMinute, durationSecond} from \"./duration.js\";\n\nexport const timeHour = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond - date.getMinutes() * durationMinute);\n}, (date, step) => {\n  date.setTime(+date + step * durationHour);\n}, (start, end) => {\n  return (end - start) / durationHour;\n}, (date) => {\n  return date.getHours();\n});\n\nexport const timeHours = timeHour.range;\n\nexport const utcHour = timeInterval((date) => {\n  date.setUTCMinutes(0, 0, 0);\n}, (date, step) => {\n  date.setTime(+date + step * durationHour);\n}, (start, end) => {\n  return (end - start) / durationHour;\n}, (date) => {\n  return date.getUTCHours();\n});\n\nexport const utcHours = utcHour.range;\n"], "mappings": "AAAA,SAAQA,YAAY,QAAO,eAAe;AAC1C,SAAQC,YAAY,EAAEC,cAAc,EAAEC,cAAc,QAAO,eAAe;AAE1E,OAAO,MAAMC,QAAQ,GAAGJ,YAAY,CAAEK,IAAI,IAAK;EAC7CA,IAAI,CAACC,OAAO,CAACD,IAAI,GAAGA,IAAI,CAACE,eAAe,CAAC,CAAC,GAAGF,IAAI,CAACG,UAAU,CAAC,CAAC,GAAGL,cAAc,GAAGE,IAAI,CAACI,UAAU,CAAC,CAAC,GAAGP,cAAc,CAAC;AACvH,CAAC,EAAE,CAACG,IAAI,EAAEK,IAAI,KAAK;EACjBL,IAAI,CAACC,OAAO,CAAC,CAACD,IAAI,GAAGK,IAAI,GAAGT,YAAY,CAAC;AAC3C,CAAC,EAAE,CAACU,KAAK,EAAEC,GAAG,KAAK;EACjB,OAAO,CAACA,GAAG,GAAGD,KAAK,IAAIV,YAAY;AACrC,CAAC,EAAGI,IAAI,IAAK;EACX,OAAOA,IAAI,CAACQ,QAAQ,CAAC,CAAC;AACxB,CAAC,CAAC;AAEF,OAAO,MAAMC,SAAS,GAAGV,QAAQ,CAACW,KAAK;AAEvC,OAAO,MAAMC,OAAO,GAAGhB,YAAY,CAAEK,IAAI,IAAK;EAC5CA,IAAI,CAACY,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7B,CAAC,EAAE,CAACZ,IAAI,EAAEK,IAAI,KAAK;EACjBL,IAAI,CAACC,OAAO,CAAC,CAACD,IAAI,GAAGK,IAAI,GAAGT,YAAY,CAAC;AAC3C,CAAC,EAAE,CAACU,KAAK,EAAEC,GAAG,KAAK;EACjB,OAAO,CAACA,GAAG,GAAGD,KAAK,IAAIV,YAAY;AACrC,CAAC,EAAGI,IAAI,IAAK;EACX,OAAOA,IAAI,CAACa,WAAW,CAAC,CAAC;AAC3B,CAAC,CAAC;AAEF,OAAO,MAAMC,QAAQ,GAAGH,OAAO,CAACD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}