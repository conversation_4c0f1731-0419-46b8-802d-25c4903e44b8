{"ast": null, "code": "import React from 'react';\nimport { notifyManager } from '../core/notifyManager';\nimport { parseFilterArgs } from '../core/utils';\nimport { useQueryClient } from './QueryClientProvider';\nvar checkIsFetching = function checkIsFetching(queryClient, filters, isFetching, setIsFetching) {\n  var newIsFetching = queryClient.isFetching(filters);\n  if (isFetching !== newIsFetching) {\n    setIsFetching(newIsFetching);\n  }\n};\nexport function useIsFetching(arg1, arg2) {\n  var mountedRef = React.useRef(false);\n  var queryClient = useQueryClient();\n  var _parseFilterArgs = parseFilterArgs(arg1, arg2),\n    filters = _parseFilterArgs[0];\n  var _React$useState = React.useState(queryClient.isFetching(filters)),\n    isFetching = _React$useState[0],\n    setIsFetching = _React$useState[1];\n  var filtersRef = React.useRef(filters);\n  filtersRef.current = filters;\n  var isFetchingRef = React.useRef(isFetching);\n  isFetchingRef.current = isFetching;\n  React.useEffect(function () {\n    mountedRef.current = true;\n    checkIsFetching(queryClient, filtersRef.current, isFetchingRef.current, setIsFetching);\n    var unsubscribe = queryClient.getQueryCache().subscribe(notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        checkIsFetching(queryClient, filtersRef.current, isFetchingRef.current, setIsFetching);\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [queryClient]);\n  return isFetching;\n}", "map": {"version": 3, "names": ["React", "notify<PERSON><PERSON>ger", "parseFilter<PERSON><PERSON>s", "useQueryClient", "checkIsFetching", "queryClient", "filters", "isFetching", "setIsFetching", "newIsFetching", "useIsFetching", "arg1", "arg2", "mountedRef", "useRef", "_parseFilterArgs", "_React$useState", "useState", "filtersRef", "current", "isFetchingRef", "useEffect", "unsubscribe", "get<PERSON><PERSON><PERSON><PERSON>ache", "subscribe", "batchCalls"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/node_modules/react-query/es/react/useIsFetching.js"], "sourcesContent": ["import React from 'react';\nimport { notifyManager } from '../core/notifyManager';\nimport { parseFilterArgs } from '../core/utils';\nimport { useQueryClient } from './QueryClientProvider';\n\nvar checkIsFetching = function checkIsFetching(queryClient, filters, isFetching, setIsFetching) {\n  var newIsFetching = queryClient.isFetching(filters);\n\n  if (isFetching !== newIsFetching) {\n    setIsFetching(newIsFetching);\n  }\n};\n\nexport function useIsFetching(arg1, arg2) {\n  var mountedRef = React.useRef(false);\n  var queryClient = useQueryClient();\n\n  var _parseFilterArgs = parseFilterArgs(arg1, arg2),\n      filters = _parseFilterArgs[0];\n\n  var _React$useState = React.useState(queryClient.isFetching(filters)),\n      isFetching = _React$useState[0],\n      setIsFetching = _React$useState[1];\n\n  var filtersRef = React.useRef(filters);\n  filtersRef.current = filters;\n  var isFetchingRef = React.useRef(isFetching);\n  isFetchingRef.current = isFetching;\n  React.useEffect(function () {\n    mountedRef.current = true;\n    checkIsFetching(queryClient, filtersRef.current, isFetchingRef.current, setIsFetching);\n    var unsubscribe = queryClient.getQueryCache().subscribe(notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        checkIsFetching(queryClient, filtersRef.current, isFetchingRef.current, setIsFetching);\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [queryClient]);\n  return isFetching;\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,eAAe,QAAQ,eAAe;AAC/C,SAASC,cAAc,QAAQ,uBAAuB;AAEtD,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,WAAW,EAAEC,OAAO,EAAEC,UAAU,EAAEC,aAAa,EAAE;EAC9F,IAAIC,aAAa,GAAGJ,WAAW,CAACE,UAAU,CAACD,OAAO,CAAC;EAEnD,IAAIC,UAAU,KAAKE,aAAa,EAAE;IAChCD,aAAa,CAACC,aAAa,CAAC;EAC9B;AACF,CAAC;AAED,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACxC,IAAIC,UAAU,GAAGb,KAAK,CAACc,MAAM,CAAC,KAAK,CAAC;EACpC,IAAIT,WAAW,GAAGF,cAAc,CAAC,CAAC;EAElC,IAAIY,gBAAgB,GAAGb,eAAe,CAACS,IAAI,EAAEC,IAAI,CAAC;IAC9CN,OAAO,GAAGS,gBAAgB,CAAC,CAAC,CAAC;EAEjC,IAAIC,eAAe,GAAGhB,KAAK,CAACiB,QAAQ,CAACZ,WAAW,CAACE,UAAU,CAACD,OAAO,CAAC,CAAC;IACjEC,UAAU,GAAGS,eAAe,CAAC,CAAC,CAAC;IAC/BR,aAAa,GAAGQ,eAAe,CAAC,CAAC,CAAC;EAEtC,IAAIE,UAAU,GAAGlB,KAAK,CAACc,MAAM,CAACR,OAAO,CAAC;EACtCY,UAAU,CAACC,OAAO,GAAGb,OAAO;EAC5B,IAAIc,aAAa,GAAGpB,KAAK,CAACc,MAAM,CAACP,UAAU,CAAC;EAC5Ca,aAAa,CAACD,OAAO,GAAGZ,UAAU;EAClCP,KAAK,CAACqB,SAAS,CAAC,YAAY;IAC1BR,UAAU,CAACM,OAAO,GAAG,IAAI;IACzBf,eAAe,CAACC,WAAW,EAAEa,UAAU,CAACC,OAAO,EAAEC,aAAa,CAACD,OAAO,EAAEX,aAAa,CAAC;IACtF,IAAIc,WAAW,GAAGjB,WAAW,CAACkB,aAAa,CAAC,CAAC,CAACC,SAAS,CAACvB,aAAa,CAACwB,UAAU,CAAC,YAAY;MAC3F,IAAIZ,UAAU,CAACM,OAAO,EAAE;QACtBf,eAAe,CAACC,WAAW,EAAEa,UAAU,CAACC,OAAO,EAAEC,aAAa,CAACD,OAAO,EAAEX,aAAa,CAAC;MACxF;IACF,CAAC,CAAC,CAAC;IACH,OAAO,YAAY;MACjBK,UAAU,CAACM,OAAO,GAAG,KAAK;MAC1BG,WAAW,CAAC,CAAC;IACf,CAAC;EACH,CAAC,EAAE,CAACjB,WAAW,CAAC,CAAC;EACjB,OAAOE,UAAU;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}