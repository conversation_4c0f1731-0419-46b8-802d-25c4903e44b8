{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aidesk\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-6 sm:px-0\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl font-bold text-gray-900 mb-4\",\n              children: \"\\uD83D\\uDEE1\\uFE0F Bugcrowd AI \\uCDE8\\uC57D\\uC810 \\uAC80\\uC99D \\uD234\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 10,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl text-gray-600 mb-8\",\n              children: \"AI\\uB97C \\uD65C\\uC6A9\\uD55C \\uC790\\uB3D9 \\uCDE8\\uC57D\\uC810 \\uBD84\\uC11D \\uBC0F \\uBD84\\uB958 \\uC2DC\\uC2A4\\uD15C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 13,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\",\n                children: \"\\u2705 \\uBC31\\uC5D4\\uB4DC \\uC11C\\uBC84: \\uC815\\uC0C1 \\uC791\\uB3D9 (http://localhost:8000)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 17,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\",\n                children: \"\\u2705 \\uD504\\uB860\\uD2B8\\uC5D4\\uB4DC \\uC11C\\uBC84: \\uC815\\uC0C1 \\uC791\\uB3D9 (http://localhost:3000)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 20,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded\",\n                children: [\"\\uD83D\\uDCDA API \\uBB38\\uC11C: \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"http://localhost:8000/docs\",\n                  className: \"underline\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  children: \"http://localhost:8000/docs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 24,\n                  columnNumber: 30\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 16,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 9,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 8,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "App", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "target", "rel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\n\nfunction App() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          <div className=\"border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center\">\n            <div className=\"text-center\">\n              <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n                🛡️ Bugcrowd AI 취약점 검증 툴\n              </h1>\n              <p className=\"text-xl text-gray-600 mb-8\">\n                AI를 활용한 자동 취약점 분석 및 분류 시스템\n              </p>\n              <div className=\"space-y-4\">\n                <div className=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\">\n                  ✅ 백엔드 서버: 정상 작동 (http://localhost:8000)\n                </div>\n                <div className=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\">\n                  ✅ 프론트엔드 서버: 정상 작동 (http://localhost:3000)\n                </div>\n                <div className=\"bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded\">\n                  📚 API 문서: <a href=\"http://localhost:8000/docs\" className=\"underline\" target=\"_blank\" rel=\"noopener noreferrer\">http://localhost:8000/docs</a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAKE,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtCH,OAAA;MAAKE,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrDH,OAAA;QAAKE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCH,OAAA;UAAKE,SAAS,EAAC,yFAAyF;UAAAC,QAAA,eACtGH,OAAA;YAAKE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BH,OAAA;cAAIE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLP,OAAA;cAAGE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJP,OAAA;cAAKE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBH,OAAA;gBAAKE,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,EAAC;cAEvF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNP,OAAA;gBAAKE,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,EAAC;cAEvF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNP,OAAA;gBAAKE,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,GAAC,iCACvE,eAAAH,OAAA;kBAAGQ,IAAI,EAAC,4BAA4B;kBAACN,SAAS,EAAC,WAAW;kBAACO,MAAM,EAAC,QAAQ;kBAACC,GAAG,EAAC,qBAAqB;kBAAAP,QAAA,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3I,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACI,EAAA,GA9BQV,GAAG;AAgCZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}