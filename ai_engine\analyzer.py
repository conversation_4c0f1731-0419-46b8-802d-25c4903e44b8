"""
메인 취약점 분석 엔진
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

from .models.ai_models import AIModelManager, AIResponse
from .prompts.vulnerability_prompts import VulnerabilityPromptManager
from .utils.text_processor import TextProcessor, ProcessedText
from .utils.result_parser import ResultParser, ParsedAnalysis

@dataclass
class AnalysisConfig:
    """분석 설정"""
    model_name: Optional[str] = None
    temperature: float = 0.3
    max_tokens: int = 2000
    include_reasoning: bool = True
    include_recommendations: bool = True
    confidence_threshold: float = 0.5

@dataclass
class VulnerabilityAnalysisResult:
    """취약점 분석 결과"""
    success: bool
    analysis_id: str
    vulnerability_data: Dict[str, Any]
    processed_text: ProcessedText
    ai_response: Optional[AIResponse]
    parsed_analysis: Optional[ParsedAnalysis]
    classification: Dict[str, Any]
    severity_assessment: Dict[str, Any]
    recommendations: List[str]
    confidence: float
    processing_time: float
    errors: List[str]
    metadata: Dict[str, Any]

class VulnerabilityAnalyzer:
    """취약점 분석 엔진"""
    
    def __init__(self, taxonomy_data: Optional[List[Dict[str, Any]]] = None):
        self.ai_manager = AIModelManager()
        self.prompt_manager = VulnerabilityPromptManager()
        self.text_processor = TextProcessor()
        self.result_parser = ResultParser()
        self.taxonomy_data = taxonomy_data or []
        
    async def analyze_vulnerability(self, 
                                  vulnerability_data: Dict[str, Any],
                                  config: Optional[AnalysisConfig] = None) -> VulnerabilityAnalysisResult:
        """단일 취약점 분석"""
        start_time = time.time()
        config = config or AnalysisConfig()
        analysis_id = f"analysis_{int(time.time() * 1000)}"
        
        try:
            # 1. 텍스트 전처리
            processed_text = self._preprocess_vulnerability_data(vulnerability_data)
            
            # 2. AI 프롬프트 생성
            prompt = self.prompt_manager.create_full_analysis_prompt(
                vulnerability_data, 
                self.taxonomy_data
            )
            
            # 3. AI 모델 호출
            ai_response = await self.ai_manager.generate_response(
                prompt=prompt,
                model_name=config.model_name,
                temperature=config.temperature,
                max_tokens=config.max_tokens
            )
            
            # 4. 결과 파싱
            parsed_analysis = self.result_parser.parse_ai_response(ai_response.content)
            
            # 5. 결과 검증 및 후처리
            validated_result = self._validate_and_enhance_result(
                parsed_analysis, 
                processed_text,
                config
            )
            
            processing_time = time.time() - start_time
            
            return VulnerabilityAnalysisResult(
                success=True,
                analysis_id=analysis_id,
                vulnerability_data=vulnerability_data,
                processed_text=processed_text,
                ai_response=ai_response,
                parsed_analysis=parsed_analysis,
                classification=validated_result["classification"],
                severity_assessment=validated_result["severity_assessment"],
                recommendations=validated_result["recommendations"],
                confidence=validated_result["confidence"],
                processing_time=processing_time,
                errors=parsed_analysis.errors if parsed_analysis else [],
                metadata=self._generate_analysis_metadata(
                    ai_response, processed_text, processing_time
                )
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            return VulnerabilityAnalysisResult(
                success=False,
                analysis_id=analysis_id,
                vulnerability_data=vulnerability_data,
                processed_text=ProcessedText("", "", [], [], {}),
                ai_response=None,
                parsed_analysis=None,
                classification={},
                severity_assessment={},
                recommendations=[],
                confidence=0.0,
                processing_time=processing_time,
                errors=[f"분석 중 오류 발생: {str(e)}"],
                metadata={}
            )
    
    async def batch_analyze_vulnerabilities(self,
                                          vulnerabilities: List[Dict[str, Any]],
                                          config: Optional[AnalysisConfig] = None) -> List[VulnerabilityAnalysisResult]:
        """배치 취약점 분석"""
        config = config or AnalysisConfig()
        
        # 동시 분석 (최대 5개씩)
        semaphore = asyncio.Semaphore(5)
        
        async def analyze_single(vuln_data):
            async with semaphore:
                return await self.analyze_vulnerability(vuln_data, config)
        
        tasks = [analyze_single(vuln) for vuln in vulnerabilities]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 예외 처리
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # 예외 발생 시 실패 결과 생성
                processed_results.append(VulnerabilityAnalysisResult(
                    success=False,
                    analysis_id=f"batch_error_{i}",
                    vulnerability_data=vulnerabilities[i],
                    processed_text=ProcessedText("", "", [], [], {}),
                    ai_response=None,
                    parsed_analysis=None,
                    classification={},
                    severity_assessment={},
                    recommendations=[],
                    confidence=0.0,
                    processing_time=0.0,
                    errors=[f"배치 분석 오류: {str(result)}"],
                    metadata={}
                ))
            else:
                processed_results.append(result)
        
        return processed_results
    
    def _preprocess_vulnerability_data(self, vulnerability_data: Dict[str, Any]) -> ProcessedText:
        """취약점 데이터 전처리"""
        # 주요 텍스트 필드들을 결합
        text_fields = [
            vulnerability_data.get("title", ""),
            vulnerability_data.get("description", ""),
            vulnerability_data.get("steps_to_reproduce", ""),
            vulnerability_data.get("proof_of_concept", ""),
            vulnerability_data.get("impact", "")
        ]
        
        combined_text = " ".join(filter(None, text_fields))
        return self.text_processor.process_vulnerability_text(combined_text)
    
    def _validate_and_enhance_result(self, 
                                   parsed_analysis: Optional[ParsedAnalysis],
                                   processed_text: ProcessedText,
                                   config: AnalysisConfig) -> Dict[str, Any]:
        """결과 검증 및 개선"""
        if not parsed_analysis or not self.result_parser.validate_parsed_result(parsed_analysis):
            # 기본 분석 결과 생성
            return self._generate_fallback_analysis(processed_text)
        
        # 신뢰도 검증
        if parsed_analysis.confidence < config.confidence_threshold:
            # 신뢰도가 낮은 경우 보정
            parsed_analysis.confidence = max(parsed_analysis.confidence, 0.3)
        
        # 키워드 기반 분류 보정
        enhanced_classification = self._enhance_classification_with_keywords(
            parsed_analysis.classification,
            processed_text.keywords
        )
        
        # 권장사항 정리
        recommendations = self._consolidate_recommendations(parsed_analysis.recommendations)
        
        return {
            "classification": enhanced_classification,
            "severity_assessment": parsed_analysis.severity_assessment,
            "recommendations": recommendations,
            "confidence": parsed_analysis.confidence
        }
    
    def _generate_fallback_analysis(self, processed_text: ProcessedText) -> Dict[str, Any]:
        """기본 분석 결과 생성 (AI 분석 실패 시)"""
        # 키워드 기반 간단한 분류
        primary_category = self._classify_by_keywords(processed_text.keywords)
        
        # 키워드 기반 심각도 추정
        severity = self._estimate_severity_by_keywords(processed_text.keywords)
        
        return {
            "classification": {
                "primary_category": primary_category,
                "reasoning": "키워드 기반 자동 분류"
            },
            "severity_assessment": {
                "severity": severity,
                "confidence": 0.3,
                "reasoning": "키워드 기반 심각도 추정"
            },
            "recommendations": [
                "상세한 보안 검토 수행",
                "전문가 검증 필요",
                "추가 테스트 실시"
            ],
            "confidence": 0.3
        }
    
    def _classify_by_keywords(self, keywords: List[str]) -> Dict[str, Any]:
        """키워드 기반 분류"""
        keyword_str = " ".join(keywords).lower()
        
        # 간단한 키워드 매칭
        if any(word in keyword_str for word in ["xss", "script", "javascript"]):
            return {
                "id": "cross_site_scripting_xss",
                "name": "Cross-Site Scripting (XSS)",
                "type": "category",
                "confidence_score": 0.6
            }
        elif any(word in keyword_str for word in ["sql", "injection", "union"]):
            return {
                "id": "sql_injection",
                "name": "SQL Injection",
                "type": "category", 
                "confidence_score": 0.6
            }
        elif any(word in keyword_str for word in ["csrf", "cross-site request"]):
            return {
                "id": "cross_site_request_forgery_csrf",
                "name": "Cross-Site Request Forgery (CSRF)",
                "type": "category",
                "confidence_score": 0.6
            }
        else:
            return {
                "id": "unknown",
                "name": "Unknown Vulnerability Type",
                "type": "category",
                "confidence_score": 0.2
            }
    
    def _estimate_severity_by_keywords(self, keywords: List[str]) -> str:
        """키워드 기반 심각도 추정"""
        keyword_str = " ".join(keywords).lower()
        
        if any(word in keyword_str for word in ["remote code execution", "rce", "system compromise"]):
            return "critical"
        elif any(word in keyword_str for word in ["privilege escalation", "admin access", "authentication bypass"]):
            return "high"
        elif any(word in keyword_str for word in ["xss", "sql injection", "csrf"]):
            return "medium"
        else:
            return "low"
    
    def _enhance_classification_with_keywords(self, 
                                            classification: Dict[str, Any],
                                            keywords: List[str]) -> Dict[str, Any]:
        """키워드를 활용한 분류 개선"""
        if not classification:
            return classification
        
        # 키워드 일치도 확인
        primary_cat = classification.get("primary_category", {})
        if primary_cat:
            cat_name = primary_cat.get("name", "").lower()
            keyword_str = " ".join(keywords).lower()
            
            # 키워드와 분류의 일치도 계산
            relevance_score = self._calculate_keyword_relevance(cat_name, keyword_str)
            
            # 신뢰도 조정
            current_confidence = primary_cat.get("confidence_score", 0.0)
            adjusted_confidence = (current_confidence + relevance_score) / 2
            primary_cat["confidence_score"] = min(adjusted_confidence, 1.0)
        
        return classification
    
    def _calculate_keyword_relevance(self, category_name: str, keyword_str: str) -> float:
        """카테고리와 키워드의 관련성 점수 계산"""
        category_words = category_name.split()
        matches = sum(1 for word in category_words if word in keyword_str)
        return matches / len(category_words) if category_words else 0.0
    
    def _consolidate_recommendations(self, recommendations: Dict[str, Any]) -> List[str]:
        """권장사항 통합"""
        all_recommendations = []
        
        for category, items in recommendations.items():
            if isinstance(items, list):
                all_recommendations.extend(items)
        
        # 중복 제거 및 정리
        unique_recommendations = list(set(all_recommendations))
        return unique_recommendations[:10]  # 최대 10개
    
    def _generate_analysis_metadata(self, 
                                  ai_response: Optional[AIResponse],
                                  processed_text: ProcessedText,
                                  processing_time: float) -> Dict[str, Any]:
        """분석 메타데이터 생성"""
        metadata = {
            "processing_time": processing_time,
            "text_complexity": processed_text.metadata.get("complexity_score", 0.0),
            "keyword_count": len(processed_text.keywords),
            "entity_count": len(processed_text.entities)
        }
        
        if ai_response:
            metadata.update({
                "model_used": ai_response.model_used,
                "ai_confidence": ai_response.confidence,
                "tokens_used": ai_response.tokens_used
            })
        
        return metadata
    
    def get_available_models(self) -> List[str]:
        """사용 가능한 AI 모델 목록 반환"""
        return self.ai_manager.get_available_models()
    
    def set_taxonomy_data(self, taxonomy_data: List[Dict[str, Any]]):
        """분류 체계 데이터 설정"""
        self.taxonomy_data = taxonomy_data
