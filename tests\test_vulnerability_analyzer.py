"""
취약점 분석기 테스트
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from ai_engine.analyzer import VulnerabilityA<PERSON>y<PERSON>, AnalysisConfig
from ai_engine.models.ai_models import AIResponse
from ai_engine.utils.text_processor import ProcessedText

class TestVulnerabilityAnalyzer:
    """취약점 분석기 테스트 클래스"""
    
    @pytest.fixture
    def analyzer(self):
        """분석기 인스턴스 생성"""
        taxonomy_data = [
            {
                'id': 'cross_site_scripting_xss',
                'name': 'Cross-Site Scripting (XSS)',
                'type': 'category',
                'priority': 3
            },
            {
                'id': 'sql_injection',
                'name': 'SQL Injection',
                'type': 'category',
                'priority': 2
            }
        ]
        return VulnerabilityAnalyzer(taxonomy_data)
    
    @pytest.fixture
    def sample_vulnerability_data(self):
        """샘플 취약점 데이터"""
        return {
            'title': 'XSS vulnerability in search form',
            'description': 'The search form allows script injection through the query parameter',
            'steps_to_reproduce': '1. Go to search page\n2. Enter <script>alert(1)</script>\n3. Submit form',
            'proof_of_concept': '<script>alert("XSS")</script>',
            'impact': 'Allows execution of arbitrary JavaScript code',
            'affected_system': 'Web application',
            'vulnerability_type': 'Cross-Site Scripting',
            'attack_vector': 'Network'
        }
    
    @pytest.fixture
    def mock_ai_response(self):
        """모의 AI 응답"""
        return AIResponse(
            content='{"classification": {"primary_category": {"id": "cross_site_scripting_xss", "name": "Cross-Site Scripting (XSS)", "type": "category", "confidence_score": 0.9}}, "severity_assessment": {"severity": "medium", "confidence": 0.8}, "recommendations": {"immediate_actions": ["Sanitize input", "Implement CSP"]}}',
            confidence=0.85,
            model_used='gpt-4',
            tokens_used=500,
            processing_time=2.5
        )
    
    def test_analyzer_initialization(self, analyzer):
        """분석기 초기화 테스트"""
        assert analyzer is not None
        assert len(analyzer.taxonomy_data) == 2
        assert analyzer.ai_manager is not None
        assert analyzer.prompt_manager is not None
        assert analyzer.text_processor is not None
        assert analyzer.result_parser is not None
    
    def test_preprocess_vulnerability_data(self, analyzer, sample_vulnerability_data):
        """취약점 데이터 전처리 테스트"""
        processed = analyzer._preprocess_vulnerability_data(sample_vulnerability_data)
        
        assert isinstance(processed, ProcessedText)
        assert len(processed.original) > 0
        assert len(processed.cleaned) > 0
        assert len(processed.keywords) > 0
        assert 'xss' in [k.lower() for k in processed.keywords]
    
    def test_classify_by_keywords(self, analyzer):
        """키워드 기반 분류 테스트"""
        keywords = ['xss', 'script', 'javascript', 'injection']
        classification = analyzer._classify_by_keywords(keywords)
        
        assert classification['id'] == 'cross_site_scripting_xss'
        assert classification['name'] == 'Cross-Site Scripting (XSS)'
        assert classification['confidence_score'] > 0.5
    
    def test_estimate_severity_by_keywords(self, analyzer):
        """키워드 기반 심각도 추정 테스트"""
        # Critical severity keywords
        critical_keywords = ['remote code execution', 'rce', 'system compromise']
        assert analyzer._estimate_severity_by_keywords(critical_keywords) == 'critical'
        
        # High severity keywords
        high_keywords = ['privilege escalation', 'admin access']
        assert analyzer._estimate_severity_by_keywords(high_keywords) == 'high'
        
        # Medium severity keywords
        medium_keywords = ['xss', 'sql injection']
        assert analyzer._estimate_severity_by_keywords(medium_keywords) == 'medium'
        
        # Low severity keywords
        low_keywords = ['information disclosure']
        assert analyzer._estimate_severity_by_keywords(low_keywords) == 'low'
    
    @pytest.mark.asyncio
    async def test_analyze_vulnerability_success(self, analyzer, sample_vulnerability_data, mock_ai_response):
        """성공적인 취약점 분석 테스트"""
        with patch.object(analyzer.ai_manager, 'generate_response', return_value=mock_ai_response):
            config = AnalysisConfig(confidence_threshold=0.5)
            result = await analyzer.analyze_vulnerability(sample_vulnerability_data, config)
            
            assert result.success is True
            assert result.analysis_id is not None
            assert result.confidence > 0.0
            assert result.processing_time > 0.0
            assert len(result.errors) == 0
    
    @pytest.mark.asyncio
    async def test_analyze_vulnerability_failure(self, analyzer, sample_vulnerability_data):
        """실패한 취약점 분석 테스트"""
        with patch.object(analyzer.ai_manager, 'generate_response', side_effect=Exception("AI model error")):
            config = AnalysisConfig()
            result = await analyzer.analyze_vulnerability(sample_vulnerability_data, config)
            
            assert result.success is False
            assert len(result.errors) > 0
            assert "AI model error" in str(result.errors)
    
    @pytest.mark.asyncio
    async def test_batch_analyze_vulnerabilities(self, analyzer, sample_vulnerability_data, mock_ai_response):
        """배치 취약점 분석 테스트"""
        vulnerabilities = [sample_vulnerability_data, sample_vulnerability_data.copy()]
        
        with patch.object(analyzer.ai_manager, 'generate_response', return_value=mock_ai_response):
            config = AnalysisConfig()
            results = await analyzer.batch_analyze_vulnerabilities(vulnerabilities, config)
            
            assert len(results) == 2
            for result in results:
                assert result.analysis_id is not None
    
    def test_generate_fallback_analysis(self, analyzer):
        """폴백 분석 생성 테스트"""
        processed_text = ProcessedText(
            original="XSS vulnerability test",
            cleaned="XSS vulnerability test",
            keywords=['xss', 'script'],
            entities=[],
            metadata={'complexity_score': 0.5}
        )
        
        fallback = analyzer._generate_fallback_analysis(processed_text)
        
        assert 'classification' in fallback
        assert 'severity_assessment' in fallback
        assert 'recommendations' in fallback
        assert fallback['confidence'] > 0.0
    
    def test_enhance_classification_with_keywords(self, analyzer):
        """키워드를 활용한 분류 개선 테스트"""
        classification = {
            'primary_category': {
                'id': 'cross_site_scripting_xss',
                'name': 'Cross-Site Scripting (XSS)',
                'confidence_score': 0.7
            }
        }
        keywords = ['xss', 'script', 'javascript']
        
        enhanced = analyzer._enhance_classification_with_keywords(classification, keywords)
        
        # 키워드 일치로 인해 신뢰도가 향상되어야 함
        assert enhanced['primary_category']['confidence_score'] >= 0.7
    
    def test_calculate_keyword_relevance(self, analyzer):
        """키워드 관련성 계산 테스트"""
        category_name = "cross-site scripting"
        keyword_str = "xss script injection cross site"
        
        relevance = analyzer._calculate_keyword_relevance(category_name, keyword_str)
        assert relevance > 0.5  # 관련 키워드가 있으므로 높은 관련성
    
    def test_consolidate_recommendations(self, analyzer):
        """권장사항 통합 테스트"""
        recommendations = {
            'immediate_actions': ['Sanitize input', 'Validate data'],
            'short_term_fixes': ['Implement CSP', 'Update framework'],
            'long_term_improvements': ['Security training', 'Code review']
        }
        
        consolidated = analyzer._consolidate_recommendations(recommendations)
        
        assert isinstance(consolidated, list)
        assert len(consolidated) <= 10  # 최대 10개로 제한
        assert 'Sanitize input' in consolidated
    
    def test_get_available_models(self, analyzer):
        """사용 가능한 모델 목록 테스트"""
        with patch.object(analyzer.ai_manager, 'get_available_models', return_value=['gpt-4', 'ollama']):
            models = analyzer.get_available_models()
            assert isinstance(models, list)
            assert len(models) > 0
    
    def test_set_taxonomy_data(self, analyzer):
        """분류 체계 데이터 설정 테스트"""
        new_taxonomy = [
            {
                'id': 'new_category',
                'name': 'New Category',
                'type': 'category',
                'priority': 1
            }
        ]
        
        analyzer.set_taxonomy_data(new_taxonomy)
        assert analyzer.taxonomy_data == new_taxonomy

class TestAnalysisConfig:
    """분석 설정 테스트 클래스"""
    
    def test_default_config(self):
        """기본 설정 테스트"""
        config = AnalysisConfig()
        
        assert config.model_name is None
        assert config.temperature == 0.3
        assert config.max_tokens == 2000
        assert config.include_reasoning is True
        assert config.include_recommendations is True
        assert config.confidence_threshold == 0.5
    
    def test_custom_config(self):
        """커스텀 설정 테스트"""
        config = AnalysisConfig(
            model_name='gpt-4',
            temperature=0.5,
            max_tokens=1500,
            confidence_threshold=0.8
        )
        
        assert config.model_name == 'gpt-4'
        assert config.temperature == 0.5
        assert config.max_tokens == 1500
        assert config.confidence_threshold == 0.8

if __name__ == '__main__':
    pytest.main([__file__])
