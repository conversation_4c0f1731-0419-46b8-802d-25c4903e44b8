# Bugcrowd AI 취약점 검증 툴

Bugcrowd의 취약점 분류 체계를 기반으로 한 AI 자동 취약점 검증 도구입니다.

## 주요 기능

- 🤖 AI 기반 취약점 자동 분석
- 📊 Bugcrowd 취약점 분류 체계 준수
- 🌐 직관적인 웹 인터페이스
- 📈 상세한 검증 결과 리포팅
- 🔧 로컬 AI 및 커스텀 API 지원

## 시스템 아키텍처

```
aidesk/
├── backend/                    # 백엔드 API 서버
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py            # FastAPI 메인 애플리케이션
│   │   ├── models/            # 데이터 모델
│   │   ├── services/          # 비즈니스 로직
│   │   ├── api/               # API 엔드포인트
│   │   └── utils/             # 유틸리티 함수
│   ├── requirements.txt
│   └── config.py
├── frontend/                   # React 프론트엔드
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── services/
│   │   └── utils/
│   ├── package.json
│   └── public/
├── ai_engine/                  # AI 분석 엔진
│   ├── __init__.py
│   ├── analyzer.py            # 메인 분석 엔진
│   ├── models/                # AI 모델 관리
│   ├── prompts/               # 프롬프트 템플릿
│   └── utils/
├── data/                       # 데이터 파일
│   ├── vulnerability-rating-taxonomy.json
│   └── sample_reports/
├── tests/                      # 테스트 파일
├── docs/                       # 문서
├── docker-compose.yml          # Docker 설정
├── .env.example               # 환경 변수 예시
└── README.md
```

## 기술 스택

### 백엔드
- **FastAPI**: 고성능 Python 웹 프레임워크
- **SQLAlchemy**: ORM
- **Pydantic**: 데이터 검증
- **uvicorn**: ASGI 서버

### 프론트엔드
- **React**: 사용자 인터페이스
- **TypeScript**: 타입 안전성
- **Tailwind CSS**: 스타일링
- **Axios**: HTTP 클라이언트

### AI 엔진
- **OpenAI API**: GPT 모델 (선택사항)
- **Ollama**: 로컬 LLM 지원
- **LangChain**: AI 체인 관리
- **Transformers**: 허깅페이스 모델

## 설치 및 실행

### 1. 환경 설정
```bash
# 환경 변수 설정
cp .env.example .env
# .env 파일을 편집하여 필요한 설정 추가
```

### 2. Docker를 사용한 실행 (권장)
```bash
docker-compose up -d
```

### 3. 수동 설치
```bash
# 백엔드 설치
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload

# 프론트엔드 설치
cd frontend
npm install
npm start
```

## 사용법

1. 웹 브라우저에서 `http://localhost:3000` 접속
2. 취약점 보고서 정보 입력
3. AI 분석 실행
4. 검증 결과 확인 및 다운로드

## 라이선스

MIT License
