import React, { useState } from 'react';
import { useQuery } from 'react-query';
import {
  ShieldCheckIcon,
  MagnifyingGlassIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { reportsApi, TaxonomyNode } from '../services/api';

const TaxonomyBrowser: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // 전체 분류 체계 조회
  const { data: taxonomy, isLoading: taxonomyLoading } = useQuery(
    'taxonomy',
    reportsApi.getTaxonomy
  );

  // 검색 결과 조회
  const { data: searchResults, isLoading: searchLoading } = useQuery(
    ['taxonomy-search', searchQuery],
    () => reportsApi.searchTaxonomy(searchQuery),
    { enabled: searchQuery.length >= 2 }
  );

  // 선택된 카테고리 상세 정보
  const { data: categoryDetail } = useQuery(
    ['category-detail', selectedCategory],
    () => reportsApi.getCategoryDetail(selectedCategory!),
    { enabled: !!selectedCategory }
  );

  // 높은 우선순위 취약점들
  const { data: highPriorityVulns } = useQuery(
    'high-priority-vulnerabilities',
    reportsApi.getHighPriorityVulnerabilities
  );

  // 통계 정보
  const { data: stats } = useQuery(
    'taxonomy-statistics',
    reportsApi.getTaxonomyStatistics
  );

  const toggleNode = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const getPriorityBadge = (priority: number | undefined) => {
    if (!priority) return null;
    
    const colorMap: Record<number, string> = {
      1: 'badge-danger',
      2: 'badge bg-orange-100 text-orange-800',
      3: 'badge-warning',
      4: 'badge bg-blue-100 text-blue-800',
      5: 'badge-secondary',
    };

    return (
      <span className={`badge ${colorMap[priority] || 'badge-secondary'}`}>
        P{priority}
      </span>
    );
  };

  const renderTaxonomyNode = (node: TaxonomyNode, level: number = 0) => {
    const isExpanded = expandedNodes.has(node.id);
    const hasChildren = node.children && node.children.length > 0;
    const isSelected = selectedCategory === node.id;

    return (
      <div key={node.id} className={`${level > 0 ? 'ml-6' : ''}`}>
        <div
          className={`flex items-center py-2 px-3 rounded-md cursor-pointer hover:bg-gray-50 ${
            isSelected ? 'bg-primary-50 border-l-4 border-primary-500' : ''
          }`}
          onClick={() => {
            if (hasChildren) {
              toggleNode(node.id);
            }
            setSelectedCategory(node.id);
          }}
        >
          <div className="flex items-center flex-1 min-w-0">
            {hasChildren ? (
              isExpanded ? (
                <ChevronDownIcon className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
              ) : (
                <ChevronRightIcon className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
              )
            ) : (
              <div className="w-6 mr-2" />
            )}
            
            <span className={`text-sm truncate ${
              node.type === 'category' ? 'font-medium text-gray-900' :
              node.type === 'subcategory' ? 'font-normal text-gray-700' :
              'font-normal text-gray-600'
            }`}>
              {node.name}
            </span>
          </div>
          
          <div className="flex items-center space-x-2 ml-2">
            {getPriorityBadge(node.priority)}
            <span className={`badge ${
              node.type === 'category' ? 'badge-primary' :
              node.type === 'subcategory' ? 'badge-secondary' :
              'badge bg-gray-100 text-gray-600'
            }`}>
              {node.type}
            </span>
          </div>
        </div>
        
        {isExpanded && hasChildren && (
          <div className="mt-1">
            {node.children.map((child) => renderTaxonomyNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (taxonomyLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="loading-spinner h-8 w-8 mx-auto mb-4" />
          <p className="text-gray-500">분류 체계를 불러오는 중...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 헤더 */}
      <div>
        <div className="flex items-center">
          <ShieldCheckIcon className="h-8 w-8 text-primary-600 mr-3" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">취약점 분류 체계</h1>
            <p className="mt-1 text-sm text-gray-500">
              Bugcrowd의 취약점 분류 체계를 탐색하고 검색하세요.
            </p>
          </div>
        </div>
      </div>

      {/* 통계 요약 */}
      {stats?.data && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="card">
            <div className="card-body">
              <div className="text-2xl font-bold text-gray-900">
                {stats.data.total_categories}
              </div>
              <div className="text-sm text-gray-500">카테고리</div>
            </div>
          </div>
          <div className="card">
            <div className="card-body">
              <div className="text-2xl font-bold text-gray-900">
                {stats.data.total_subcategories}
              </div>
              <div className="text-sm text-gray-500">하위 카테고리</div>
            </div>
          </div>
          <div className="card">
            <div className="card-body">
              <div className="text-2xl font-bold text-gray-900">
                {stats.data.total_variants}
              </div>
              <div className="text-sm text-gray-500">변형</div>
            </div>
          </div>
          <div className="card">
            <div className="card-body">
              <div className="text-2xl font-bold text-gray-900">
                {stats.data.total_nodes}
              </div>
              <div className="text-sm text-gray-500">전체 노드</div>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 왼쪽: 분류 체계 트리 */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-medium text-gray-900">분류 체계</h2>
                <div className="relative">
                  <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="분류 검색..."
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-primary-500 focus:border-primary-500"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
            </div>
            <div className="card-body max-h-96 overflow-y-auto">
              {searchQuery.length >= 2 ? (
                // 검색 결과 표시
                <div className="space-y-2">
                  {searchLoading ? (
                    <div className="text-center py-4">
                      <div className="loading-spinner h-6 w-6 mx-auto" />
                    </div>
                  ) : searchResults?.data.results.length === 0 ? (
                    <div className="text-center py-4 text-gray-500">
                      검색 결과가 없습니다.
                    </div>
                  ) : (
                    searchResults?.data.results.map((node) => (
                      <div
                        key={node.id}
                        className="flex items-center justify-between p-3 border rounded-md hover:bg-gray-50 cursor-pointer"
                        onClick={() => setSelectedCategory(node.id)}
                      >
                        <div>
                          <div className="font-medium text-gray-900">{node.name}</div>
                          <div className="text-sm text-gray-500">ID: {node.id}</div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getPriorityBadge(node.priority)}
                          <span className="badge badge-secondary">{node.type}</span>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              ) : (
                // 전체 분류 체계 트리 표시
                <div className="space-y-1">
                  {taxonomy?.data.content.map((node) => renderTaxonomyNode(node))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 오른쪽: 상세 정보 및 높은 우선순위 취약점 */}
        <div className="space-y-6">
          {/* 선택된 카테고리 상세 정보 */}
          {selectedCategory && categoryDetail?.data && (
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900">상세 정보</h3>
              </div>
              <div className="card-body space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900">
                    {categoryDetail.data.category.name}
                  </h4>
                  <p className="text-sm text-gray-500 mt-1">
                    ID: {categoryDetail.data.category.id}
                  </p>
                  <div className="flex items-center space-x-2 mt-2">
                    {getPriorityBadge(categoryDetail.data.category.priority)}
                    <span className="badge badge-primary">
                      {categoryDetail.data.category.type}
                    </span>
                  </div>
                </div>

                {categoryDetail.data.subcategories.length > 0 && (
                  <div>
                    <h5 className="font-medium text-gray-700 mb-2">
                      하위 카테고리 ({categoryDetail.data.subcategory_count}개)
                    </h5>
                    <div className="space-y-2">
                      {categoryDetail.data.subcategories.slice(0, 5).map((sub) => (
                        <div
                          key={sub.id}
                          className="flex items-center justify-between text-sm"
                        >
                          <span className="text-gray-700">{sub.name}</span>
                          {getPriorityBadge(sub.priority)}
                        </div>
                      ))}
                      {categoryDetail.data.subcategory_count > 5 && (
                        <p className="text-sm text-gray-500">
                          +{categoryDetail.data.subcategory_count - 5}개 더...
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 높은 우선순위 취약점 */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <ExclamationTriangleIcon className="h-5 w-5 text-warning-500 mr-2" />
                높은 우선순위 취약점
              </h3>
            </div>
            <div className="card-body">
              {highPriorityVulns?.data.vulnerabilities ? (
                <div className="space-y-3">
                  {highPriorityVulns.data.vulnerabilities.slice(0, 10).map((vuln) => (
                    <div
                      key={vuln.id}
                      className="flex items-center justify-between p-2 border rounded hover:bg-gray-50 cursor-pointer"
                      onClick={() => setSelectedCategory(vuln.id)}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {vuln.name}
                        </div>
                        <div className="text-xs text-gray-500">{vuln.type}</div>
                      </div>
                      {getPriorityBadge(vuln.priority)}
                    </div>
                  ))}
                  {highPriorityVulns.data.total_count > 10 && (
                    <p className="text-sm text-gray-500 text-center">
                      +{highPriorityVulns.data.total_count - 10}개 더...
                    </p>
                  )}
                </div>
              ) : (
                <p className="text-sm text-gray-500">
                  높은 우선순위 취약점 정보를 불러오는 중...
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaxonomyBrowser;
