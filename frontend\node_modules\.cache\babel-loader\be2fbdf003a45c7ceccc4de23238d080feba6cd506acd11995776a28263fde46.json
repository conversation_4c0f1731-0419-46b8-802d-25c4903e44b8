{"ast": null, "code": "// Side effects\nimport './setBatchUpdatesFn';\nimport './setLogger';\nexport { QueryClientProvider, useQueryClient } from './QueryClientProvider';\nexport { QueryErrorResetBoundary, useQueryErrorResetBoundary } from './QueryErrorResetBoundary';\nexport { useIsFetching } from './useIsFetching';\nexport { useIsMutating } from './useIsMutating';\nexport { useMutation } from './useMutation';\nexport { useQuery } from './useQuery';\nexport { useQueries } from './useQueries';\nexport { useInfiniteQuery } from './useInfiniteQuery';\nexport { useHydrate, Hydrate } from './Hydrate'; // Types\n\nexport * from './types';", "map": {"version": 3, "names": ["QueryClientProvider", "useQueryClient", "QueryErrorResetBoundary", "useQueryErrorResetBoundary", "useIsFetching", "useIsMutating", "useMutation", "useQuery", "useQueries", "useInfiniteQuery", "useHydrate", "Hydrate"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/node_modules/react-query/es/react/index.js"], "sourcesContent": ["// Side effects\nimport './setBatchUpdatesFn';\nimport './setLogger';\nexport { QueryClientProvider, useQueryClient } from './QueryClientProvider';\nexport { QueryErrorResetBoundary, useQueryErrorResetBoundary } from './QueryErrorResetBoundary';\nexport { useIsFetching } from './useIsFetching';\nexport { useIsMutating } from './useIsMutating';\nexport { useMutation } from './useMutation';\nexport { useQuery } from './useQuery';\nexport { useQueries } from './useQueries';\nexport { useInfiniteQuery } from './useInfiniteQuery';\nexport { useHydrate, Hydrate } from './Hydrate'; // Types\n\nexport * from './types';"], "mappings": "AAAA;AACA,OAAO,qBAAqB;AAC5B,OAAO,aAAa;AACpB,SAASA,mBAAmB,EAAEC,cAAc,QAAQ,uBAAuB;AAC3E,SAASC,uBAAuB,EAAEC,0BAA0B,QAAQ,2BAA2B;AAC/F,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,UAAU,EAAEC,OAAO,QAAQ,WAAW,CAAC,CAAC;;AAEjD,cAAc,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}