"""
취약점 관련 API 엔드포인트
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Query
from app.models.vulnerability import VulnerabilityReport, AnalysisResult
from app.services.vulnerability_service import VulnerabilityService
from app.services.taxonomy_service import TaxonomyService
from app.utils.validators import validate_vulnerability_report
from app.utils.logger import setup_logger

logger = setup_logger(__name__)
router = APIRouter()

# 의존성 주입을 위한 서비스 인스턴스
def get_taxonomy_service() -> TaxonomyService:
    return TaxonomyService()

def get_vulnerability_service(
    taxonomy_service: TaxonomyService = Depends(get_taxonomy_service)
) -> VulnerabilityService:
    return VulnerabilityService(taxonomy_service)

@router.post("/reports", response_model=VulnerabilityReport)
async def create_vulnerability_report(
    report_data: Dict[str, Any],
    service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """새 취약점 보고서 생성"""
    try:
        # 데이터 검증
        validation_result = validate_vulnerability_report(report_data)
        if not validation_result['is_valid']:
            raise HTTPException(
                status_code=400,
                detail={
                    "message": "입력 데이터가 유효하지 않습니다",
                    "errors": validation_result['errors']
                }
            )
        
        # 보고서 생성
        report = service.create_report(report_data)
        logger.info(f"새 취약점 보고서 생성됨: {report.id}")
        
        return report
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"보고서 생성 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.get("/reports", response_model=List[VulnerabilityReport])
async def get_vulnerability_reports(
    skip: int = Query(0, ge=0, description="건너뛸 항목 수"),
    limit: int = Query(100, ge=1, le=1000, description="반환할 최대 항목 수"),
    search: Optional[str] = Query(None, description="검색어"),
    severity: Optional[str] = Query(None, description="심각도 필터"),
    service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """취약점 보고서 목록 조회"""
    try:
        if search:
            # 검색 필터 구성
            filters = {}
            if severity:
                filters['severity'] = severity
            
            reports = service.search_reports(search, filters)
        else:
            reports = service.get_all_reports()
        
        # 페이지네이션 적용
        total = len(reports)
        reports = reports[skip:skip + limit]
        
        logger.info(f"보고서 목록 조회: {len(reports)}개 반환 (전체 {total}개)")
        return reports
        
    except Exception as e:
        logger.error(f"보고서 목록 조회 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.get("/reports/{report_id}", response_model=VulnerabilityReport)
async def get_vulnerability_report(
    report_id: str,
    service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """특정 취약점 보고서 조회"""
    try:
        report = service.get_report(report_id)
        if not report:
            raise HTTPException(status_code=404, detail="보고서를 찾을 수 없습니다")
        
        return report
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"보고서 조회 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.put("/reports/{report_id}", response_model=VulnerabilityReport)
async def update_vulnerability_report(
    report_id: str,
    update_data: Dict[str, Any],
    service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """취약점 보고서 업데이트"""
    try:
        # 기존 보고서 확인
        existing_report = service.get_report(report_id)
        if not existing_report:
            raise HTTPException(status_code=404, detail="보고서를 찾을 수 없습니다")
        
        # 데이터 검증 (부분 업데이트이므로 필수 필드 검증 제외)
        if update_data:
            # 기본 검증만 수행
            for key, value in update_data.items():
                if key in ['title', 'description'] and (not value or not isinstance(value, str)):
                    raise HTTPException(
                        status_code=400,
                        detail=f"유효하지 않은 값: {key}"
                    )
        
        # 보고서 업데이트
        updated_report = service.update_report(report_id, update_data)
        if not updated_report:
            raise HTTPException(status_code=500, detail="보고서 업데이트 실패")
        
        logger.info(f"보고서 업데이트됨: {report_id}")
        return updated_report
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"보고서 업데이트 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.delete("/reports/{report_id}")
async def delete_vulnerability_report(
    report_id: str,
    service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """취약점 보고서 삭제"""
    try:
        # 기존 보고서 확인
        existing_report = service.get_report(report_id)
        if not existing_report:
            raise HTTPException(status_code=404, detail="보고서를 찾을 수 없습니다")
        
        # 보고서 삭제
        success = service.delete_report(report_id)
        if not success:
            raise HTTPException(status_code=500, detail="보고서 삭제 실패")
        
        logger.info(f"보고서 삭제됨: {report_id}")
        return {"message": "보고서가 성공적으로 삭제되었습니다"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"보고서 삭제 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.get("/reports/{report_id}/analysis", response_model=List[AnalysisResult])
async def get_report_analysis_results(
    report_id: str,
    service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """보고서의 분석 결과들 조회"""
    try:
        # 보고서 존재 확인
        report = service.get_report(report_id)
        if not report:
            raise HTTPException(status_code=404, detail="보고서를 찾을 수 없습니다")
        
        # 분석 결과 조회
        analysis_results = service.get_analysis_results_by_report(report_id)
        
        return analysis_results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"분석 결과 조회 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.get("/statistics")
async def get_vulnerability_statistics(
    service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """취약점 통계 정보 조회"""
    try:
        stats = service.get_statistics()
        return stats
        
    except Exception as e:
        logger.error(f"통계 조회 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")
