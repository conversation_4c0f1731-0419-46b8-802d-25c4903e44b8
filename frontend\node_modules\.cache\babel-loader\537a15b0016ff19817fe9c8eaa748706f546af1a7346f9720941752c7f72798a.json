{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { getAbortController, functionalUpdate, isValidTimeout, noop, replaceEqualDeep, timeUntilStale, ensureQueryKeyArray } from './utils';\nimport { notifyManager } from './notifyManager';\nimport { getLogger } from './logger';\nimport { Retryer, isCancelledError } from './retryer'; // TYPES\n\n// CLASS\nexport var Query = /*#__PURE__*/function () {\n  function Query(config) {\n    this.abortSignalConsumed = false;\n    this.hadObservers = false;\n    this.defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.cache = config.cache;\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.initialState = config.state || this.getDefaultState(this.options);\n    this.state = this.initialState;\n    this.meta = config.meta;\n    this.scheduleGc();\n  }\n  var _proto = Query.prototype;\n  _proto.setOptions = function setOptions(options) {\n    var _this$options$cacheTi;\n    this.options = _extends({}, this.defaultOptions, options);\n    this.meta = options == null ? void 0 : options.meta; // Default to 5 minutes if not cache time is set\n\n    this.cacheTime = Math.max(this.cacheTime || 0, (_this$options$cacheTi = this.options.cacheTime) != null ? _this$options$cacheTi : 5 * 60 * 1000);\n  };\n  _proto.setDefaultOptions = function setDefaultOptions(options) {\n    this.defaultOptions = options;\n  };\n  _proto.scheduleGc = function scheduleGc() {\n    var _this = this;\n    this.clearGcTimeout();\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(function () {\n        _this.optionalRemove();\n      }, this.cacheTime);\n    }\n  };\n  _proto.clearGcTimeout = function clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout);\n      this.gcTimeout = undefined;\n    }\n  };\n  _proto.optionalRemove = function optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.isFetching) {\n        if (this.hadObservers) {\n          this.scheduleGc();\n        }\n      } else {\n        this.cache.remove(this);\n      }\n    }\n  };\n  _proto.setData = function setData(updater, options) {\n    var _this$options$isDataE, _this$options;\n    var prevData = this.state.data; // Get the new data\n\n    var data = functionalUpdate(updater, prevData); // Use prev data if an isDataEqual function is defined and returns `true`\n\n    if ((_this$options$isDataE = (_this$options = this.options).isDataEqual) == null ? void 0 : _this$options$isDataE.call(_this$options, prevData, data)) {\n      data = prevData;\n    } else if (this.options.structuralSharing !== false) {\n      // Structurally share data between prev and new data if needed\n      data = replaceEqualDeep(prevData, data);\n    } // Set data and mark it as cached\n\n    this.dispatch({\n      data: data,\n      type: 'success',\n      dataUpdatedAt: options == null ? void 0 : options.updatedAt\n    });\n    return data;\n  };\n  _proto.setState = function setState(state, setStateOptions) {\n    this.dispatch({\n      type: 'setState',\n      state: state,\n      setStateOptions: setStateOptions\n    });\n  };\n  _proto.cancel = function cancel(options) {\n    var _this$retryer;\n    var promise = this.promise;\n    (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  };\n  _proto.destroy = function destroy() {\n    this.clearGcTimeout();\n    this.cancel({\n      silent: true\n    });\n  };\n  _proto.reset = function reset() {\n    this.destroy();\n    this.setState(this.initialState);\n  };\n  _proto.isActive = function isActive() {\n    return this.observers.some(function (observer) {\n      return observer.options.enabled !== false;\n    });\n  };\n  _proto.isFetching = function isFetching() {\n    return this.state.isFetching;\n  };\n  _proto.isStale = function isStale() {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || this.observers.some(function (observer) {\n      return observer.getCurrentResult().isStale;\n    });\n  };\n  _proto.isStaleByTime = function isStaleByTime(staleTime) {\n    if (staleTime === void 0) {\n      staleTime = 0;\n    }\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  };\n  _proto.onFocus = function onFocus() {\n    var _this$retryer2;\n    var observer = this.observers.find(function (x) {\n      return x.shouldFetchOnWindowFocus();\n    });\n    if (observer) {\n      observer.refetch();\n    } // Continue fetch if currently paused\n\n    (_this$retryer2 = this.retryer) == null ? void 0 : _this$retryer2.continue();\n  };\n  _proto.onOnline = function onOnline() {\n    var _this$retryer3;\n    var observer = this.observers.find(function (x) {\n      return x.shouldFetchOnReconnect();\n    });\n    if (observer) {\n      observer.refetch();\n    } // Continue fetch if currently paused\n\n    (_this$retryer3 = this.retryer) == null ? void 0 : _this$retryer3.continue();\n  };\n  _proto.addObserver = function addObserver(observer) {\n    if (this.observers.indexOf(observer) === -1) {\n      this.observers.push(observer);\n      this.hadObservers = true; // Stop the query from being garbage collected\n\n      this.clearGcTimeout();\n      this.cache.notify({\n        type: 'observerAdded',\n        query: this,\n        observer: observer\n      });\n    }\n  };\n  _proto.removeObserver = function removeObserver(observer) {\n    if (this.observers.indexOf(observer) !== -1) {\n      this.observers = this.observers.filter(function (x) {\n        return x !== observer;\n      });\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.retryer.isTransportCancelable || this.abortSignalConsumed) {\n            this.retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.retryer.cancelRetry();\n          }\n        }\n        if (this.cacheTime) {\n          this.scheduleGc();\n        } else {\n          this.cache.remove(this);\n        }\n      }\n      this.cache.notify({\n        type: 'observerRemoved',\n        query: this,\n        observer: observer\n      });\n    }\n  };\n  _proto.getObserversCount = function getObserversCount() {\n    return this.observers.length;\n  };\n  _proto.invalidate = function invalidate() {\n    if (!this.state.isInvalidated) {\n      this.dispatch({\n        type: 'invalidate'\n      });\n    }\n  };\n  _proto.fetch = function fetch(options, fetchOptions) {\n    var _this2 = this,\n      _this$options$behavio,\n      _context$fetchOptions,\n      _abortController$abor;\n    if (this.state.isFetching) {\n      if (this.state.dataUpdatedAt && (fetchOptions == null ? void 0 : fetchOptions.cancelRefetch)) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({\n          silent: true\n        });\n      } else if (this.promise) {\n        var _this$retryer4;\n\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        (_this$retryer4 = this.retryer) == null ? void 0 : _this$retryer4.continueRetry(); // Return current promise if we are already fetching\n\n        return this.promise;\n      }\n    } // Update config if passed, otherwise the config from the last execution is used\n\n    if (options) {\n      this.setOptions(options);\n    } // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n\n    if (!this.options.queryFn) {\n      var observer = this.observers.find(function (x) {\n        return x.options.queryFn;\n      });\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    var queryKey = ensureQueryKeyArray(this.queryKey);\n    var abortController = getAbortController(); // Create query function context\n\n    var queryFnContext = {\n      queryKey: queryKey,\n      pageParam: undefined,\n      meta: this.meta\n    };\n    Object.defineProperty(queryFnContext, 'signal', {\n      enumerable: true,\n      get: function get() {\n        if (abortController) {\n          _this2.abortSignalConsumed = true;\n          return abortController.signal;\n        }\n        return undefined;\n      }\n    }); // Create fetch function\n\n    var fetchFn = function fetchFn() {\n      if (!_this2.options.queryFn) {\n        return Promise.reject('Missing queryFn');\n      }\n      _this2.abortSignalConsumed = false;\n      return _this2.options.queryFn(queryFnContext);\n    }; // Trigger behavior hook\n\n    var context = {\n      fetchOptions: fetchOptions,\n      options: this.options,\n      queryKey: queryKey,\n      state: this.state,\n      fetchFn: fetchFn,\n      meta: this.meta\n    };\n    if ((_this$options$behavio = this.options.behavior) == null ? void 0 : _this$options$behavio.onFetch) {\n      var _this$options$behavio2;\n      (_this$options$behavio2 = this.options.behavior) == null ? void 0 : _this$options$behavio2.onFetch(context);\n    } // Store state in case the current fetch needs to be reverted\n\n    this.revertState = this.state; // Set to fetching state if not already in it\n\n    if (!this.state.isFetching || this.state.fetchMeta !== ((_context$fetchOptions = context.fetchOptions) == null ? void 0 : _context$fetchOptions.meta)) {\n      var _context$fetchOptions2;\n      this.dispatch({\n        type: 'fetch',\n        meta: (_context$fetchOptions2 = context.fetchOptions) == null ? void 0 : _context$fetchOptions2.meta\n      });\n    } // Try to fetch the data\n\n    this.retryer = new Retryer({\n      fn: context.fetchFn,\n      abort: abortController == null ? void 0 : (_abortController$abor = abortController.abort) == null ? void 0 : _abortController$abor.bind(abortController),\n      onSuccess: function onSuccess(data) {\n        _this2.setData(data); // Notify cache callback\n\n        _this2.cache.config.onSuccess == null ? void 0 : _this2.cache.config.onSuccess(data, _this2); // Remove query after fetching if cache time is 0\n\n        if (_this2.cacheTime === 0) {\n          _this2.optionalRemove();\n        }\n      },\n      onError: function onError(error) {\n        // Optimistically update state if needed\n        if (!(isCancelledError(error) && error.silent)) {\n          _this2.dispatch({\n            type: 'error',\n            error: error\n          });\n        }\n        if (!isCancelledError(error)) {\n          // Notify cache callback\n          _this2.cache.config.onError == null ? void 0 : _this2.cache.config.onError(error, _this2); // Log error\n\n          getLogger().error(error);\n        } // Remove query after fetching if cache time is 0\n\n        if (_this2.cacheTime === 0) {\n          _this2.optionalRemove();\n        }\n      },\n      onFail: function onFail() {\n        _this2.dispatch({\n          type: 'failed'\n        });\n      },\n      onPause: function onPause() {\n        _this2.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: function onContinue() {\n        _this2.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay\n    });\n    this.promise = this.retryer.promise;\n    return this.promise;\n  };\n  _proto.dispatch = function dispatch(action) {\n    var _this3 = this;\n    this.state = this.reducer(this.state, action);\n    notifyManager.batch(function () {\n      _this3.observers.forEach(function (observer) {\n        observer.onQueryUpdate(action);\n      });\n      _this3.cache.notify({\n        query: _this3,\n        type: 'queryUpdated',\n        action: action\n      });\n    });\n  };\n  _proto.getDefaultState = function getDefaultState(options) {\n    var data = typeof options.initialData === 'function' ? options.initialData() : options.initialData;\n    var hasInitialData = typeof options.initialData !== 'undefined';\n    var initialDataUpdatedAt = hasInitialData ? typeof options.initialDataUpdatedAt === 'function' ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n    var hasData = typeof data !== 'undefined';\n    return {\n      data: data,\n      dataUpdateCount: 0,\n      dataUpdatedAt: hasData ? initialDataUpdatedAt != null ? initialDataUpdatedAt : Date.now() : 0,\n      error: null,\n      errorUpdateCount: 0,\n      errorUpdatedAt: 0,\n      fetchFailureCount: 0,\n      fetchMeta: null,\n      isFetching: false,\n      isInvalidated: false,\n      isPaused: false,\n      status: hasData ? 'success' : 'idle'\n    };\n  };\n  _proto.reducer = function reducer(state, action) {\n    var _action$meta, _action$dataUpdatedAt;\n    switch (action.type) {\n      case 'failed':\n        return _extends({}, state, {\n          fetchFailureCount: state.fetchFailureCount + 1\n        });\n      case 'pause':\n        return _extends({}, state, {\n          isPaused: true\n        });\n      case 'continue':\n        return _extends({}, state, {\n          isPaused: false\n        });\n      case 'fetch':\n        return _extends({}, state, {\n          fetchFailureCount: 0,\n          fetchMeta: (_action$meta = action.meta) != null ? _action$meta : null,\n          isFetching: true,\n          isPaused: false\n        }, !state.dataUpdatedAt && {\n          error: null,\n          status: 'loading'\n        });\n      case 'success':\n        return _extends({}, state, {\n          data: action.data,\n          dataUpdateCount: state.dataUpdateCount + 1,\n          dataUpdatedAt: (_action$dataUpdatedAt = action.dataUpdatedAt) != null ? _action$dataUpdatedAt : Date.now(),\n          error: null,\n          fetchFailureCount: 0,\n          isFetching: false,\n          isInvalidated: false,\n          isPaused: false,\n          status: 'success'\n        });\n      case 'error':\n        var error = action.error;\n        if (isCancelledError(error) && error.revert && this.revertState) {\n          return _extends({}, this.revertState);\n        }\n        return _extends({}, state, {\n          error: error,\n          errorUpdateCount: state.errorUpdateCount + 1,\n          errorUpdatedAt: Date.now(),\n          fetchFailureCount: state.fetchFailureCount + 1,\n          isFetching: false,\n          isPaused: false,\n          status: 'error'\n        });\n      case 'invalidate':\n        return _extends({}, state, {\n          isInvalidated: true\n        });\n      case 'setState':\n        return _extends({}, state, action.state);\n      default:\n        return state;\n    }\n  };\n  return Query;\n}();", "map": {"version": 3, "names": ["_extends", "getAbortController", "functionalUpdate", "isValidTimeout", "noop", "replaceEqualDeep", "timeUntilStale", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notify<PERSON><PERSON>ger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "isCancelledError", "Query", "config", "abortSignalConsumed", "hadObservers", "defaultOptions", "setOptions", "options", "observers", "cache", "query<PERSON><PERSON>", "queryHash", "initialState", "state", "getDefaultState", "meta", "scheduleGc", "_proto", "prototype", "_this$options$cacheTi", "cacheTime", "Math", "max", "setDefaultOptions", "_this", "clearGcTimeout", "gcTimeout", "setTimeout", "optionalRemove", "clearTimeout", "undefined", "length", "isFetching", "remove", "setData", "updater", "_this$options$isDataE", "_this$options", "prevData", "data", "isDataEqual", "call", "structuralSharing", "dispatch", "type", "dataUpdatedAt", "updatedAt", "setState", "setStateOptions", "cancel", "_this$retryer", "promise", "retryer", "then", "catch", "Promise", "resolve", "destroy", "silent", "reset", "isActive", "some", "observer", "enabled", "isStale", "isInvalidated", "getCurrentResult", "isStaleByTime", "staleTime", "onFocus", "_this$retryer2", "find", "x", "shouldFetchOnWindowFocus", "refetch", "continue", "onOnline", "_this$retryer3", "shouldFetchOnReconnect", "addObserver", "indexOf", "push", "notify", "query", "removeObserver", "filter", "isTransportCancelable", "revert", "cancelRetry", "getObserversCount", "invalidate", "fetch", "fetchOptions", "_this2", "_this$options$behavio", "_context$fetchOptions", "_abortController$abor", "cancelRefetch", "_this$retryer4", "continueRetry", "queryFn", "abortController", "queryFnContext", "pageParam", "Object", "defineProperty", "enumerable", "get", "signal", "fetchFn", "reject", "context", "behavior", "onFetch", "_this$options$behavio2", "revertState", "fetchMeta", "_context$fetchOptions2", "fn", "abort", "bind", "onSuccess", "onError", "error", "onFail", "onPause", "onContinue", "retry", "retry<PERSON><PERSON><PERSON>", "action", "_this3", "reducer", "batch", "for<PERSON>ach", "onQueryUpdate", "initialData", "hasInitialData", "initialDataUpdatedAt", "hasData", "dataUpdateCount", "Date", "now", "errorUpdateCount", "errorUpdatedAt", "fetchFailureCount", "isPaused", "status", "_action$meta", "_action$dataUpdatedAt"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/node_modules/react-query/es/core/query.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { getAbortController, functionalUpdate, isValidTimeout, noop, replaceEqualDeep, timeUntilStale, ensureQueryKeyArray } from './utils';\nimport { notifyManager } from './notifyManager';\nimport { getLogger } from './logger';\nimport { Retryer, isCancelledError } from './retryer'; // TYPES\n\n// CLASS\nexport var Query = /*#__PURE__*/function () {\n  function Query(config) {\n    this.abortSignalConsumed = false;\n    this.hadObservers = false;\n    this.defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.cache = config.cache;\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.initialState = config.state || this.getDefaultState(this.options);\n    this.state = this.initialState;\n    this.meta = config.meta;\n    this.scheduleGc();\n  }\n\n  var _proto = Query.prototype;\n\n  _proto.setOptions = function setOptions(options) {\n    var _this$options$cacheTi;\n\n    this.options = _extends({}, this.defaultOptions, options);\n    this.meta = options == null ? void 0 : options.meta; // Default to 5 minutes if not cache time is set\n\n    this.cacheTime = Math.max(this.cacheTime || 0, (_this$options$cacheTi = this.options.cacheTime) != null ? _this$options$cacheTi : 5 * 60 * 1000);\n  };\n\n  _proto.setDefaultOptions = function setDefaultOptions(options) {\n    this.defaultOptions = options;\n  };\n\n  _proto.scheduleGc = function scheduleGc() {\n    var _this = this;\n\n    this.clearGcTimeout();\n\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(function () {\n        _this.optionalRemove();\n      }, this.cacheTime);\n    }\n  };\n\n  _proto.clearGcTimeout = function clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout);\n      this.gcTimeout = undefined;\n    }\n  };\n\n  _proto.optionalRemove = function optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.isFetching) {\n        if (this.hadObservers) {\n          this.scheduleGc();\n        }\n      } else {\n        this.cache.remove(this);\n      }\n    }\n  };\n\n  _proto.setData = function setData(updater, options) {\n    var _this$options$isDataE, _this$options;\n\n    var prevData = this.state.data; // Get the new data\n\n    var data = functionalUpdate(updater, prevData); // Use prev data if an isDataEqual function is defined and returns `true`\n\n    if ((_this$options$isDataE = (_this$options = this.options).isDataEqual) == null ? void 0 : _this$options$isDataE.call(_this$options, prevData, data)) {\n      data = prevData;\n    } else if (this.options.structuralSharing !== false) {\n      // Structurally share data between prev and new data if needed\n      data = replaceEqualDeep(prevData, data);\n    } // Set data and mark it as cached\n\n\n    this.dispatch({\n      data: data,\n      type: 'success',\n      dataUpdatedAt: options == null ? void 0 : options.updatedAt\n    });\n    return data;\n  };\n\n  _proto.setState = function setState(state, setStateOptions) {\n    this.dispatch({\n      type: 'setState',\n      state: state,\n      setStateOptions: setStateOptions\n    });\n  };\n\n  _proto.cancel = function cancel(options) {\n    var _this$retryer;\n\n    var promise = this.promise;\n    (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  };\n\n  _proto.destroy = function destroy() {\n    this.clearGcTimeout();\n    this.cancel({\n      silent: true\n    });\n  };\n\n  _proto.reset = function reset() {\n    this.destroy();\n    this.setState(this.initialState);\n  };\n\n  _proto.isActive = function isActive() {\n    return this.observers.some(function (observer) {\n      return observer.options.enabled !== false;\n    });\n  };\n\n  _proto.isFetching = function isFetching() {\n    return this.state.isFetching;\n  };\n\n  _proto.isStale = function isStale() {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || this.observers.some(function (observer) {\n      return observer.getCurrentResult().isStale;\n    });\n  };\n\n  _proto.isStaleByTime = function isStaleByTime(staleTime) {\n    if (staleTime === void 0) {\n      staleTime = 0;\n    }\n\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  };\n\n  _proto.onFocus = function onFocus() {\n    var _this$retryer2;\n\n    var observer = this.observers.find(function (x) {\n      return x.shouldFetchOnWindowFocus();\n    });\n\n    if (observer) {\n      observer.refetch();\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer2 = this.retryer) == null ? void 0 : _this$retryer2.continue();\n  };\n\n  _proto.onOnline = function onOnline() {\n    var _this$retryer3;\n\n    var observer = this.observers.find(function (x) {\n      return x.shouldFetchOnReconnect();\n    });\n\n    if (observer) {\n      observer.refetch();\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer3 = this.retryer) == null ? void 0 : _this$retryer3.continue();\n  };\n\n  _proto.addObserver = function addObserver(observer) {\n    if (this.observers.indexOf(observer) === -1) {\n      this.observers.push(observer);\n      this.hadObservers = true; // Stop the query from being garbage collected\n\n      this.clearGcTimeout();\n      this.cache.notify({\n        type: 'observerAdded',\n        query: this,\n        observer: observer\n      });\n    }\n  };\n\n  _proto.removeObserver = function removeObserver(observer) {\n    if (this.observers.indexOf(observer) !== -1) {\n      this.observers = this.observers.filter(function (x) {\n        return x !== observer;\n      });\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.retryer.isTransportCancelable || this.abortSignalConsumed) {\n            this.retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.retryer.cancelRetry();\n          }\n        }\n\n        if (this.cacheTime) {\n          this.scheduleGc();\n        } else {\n          this.cache.remove(this);\n        }\n      }\n\n      this.cache.notify({\n        type: 'observerRemoved',\n        query: this,\n        observer: observer\n      });\n    }\n  };\n\n  _proto.getObserversCount = function getObserversCount() {\n    return this.observers.length;\n  };\n\n  _proto.invalidate = function invalidate() {\n    if (!this.state.isInvalidated) {\n      this.dispatch({\n        type: 'invalidate'\n      });\n    }\n  };\n\n  _proto.fetch = function fetch(options, fetchOptions) {\n    var _this2 = this,\n        _this$options$behavio,\n        _context$fetchOptions,\n        _abortController$abor;\n\n    if (this.state.isFetching) {\n      if (this.state.dataUpdatedAt && (fetchOptions == null ? void 0 : fetchOptions.cancelRefetch)) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({\n          silent: true\n        });\n      } else if (this.promise) {\n        var _this$retryer4;\n\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        (_this$retryer4 = this.retryer) == null ? void 0 : _this$retryer4.continueRetry(); // Return current promise if we are already fetching\n\n        return this.promise;\n      }\n    } // Update config if passed, otherwise the config from the last execution is used\n\n\n    if (options) {\n      this.setOptions(options);\n    } // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n\n\n    if (!this.options.queryFn) {\n      var observer = this.observers.find(function (x) {\n        return x.options.queryFn;\n      });\n\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n\n    var queryKey = ensureQueryKeyArray(this.queryKey);\n    var abortController = getAbortController(); // Create query function context\n\n    var queryFnContext = {\n      queryKey: queryKey,\n      pageParam: undefined,\n      meta: this.meta\n    };\n    Object.defineProperty(queryFnContext, 'signal', {\n      enumerable: true,\n      get: function get() {\n        if (abortController) {\n          _this2.abortSignalConsumed = true;\n          return abortController.signal;\n        }\n\n        return undefined;\n      }\n    }); // Create fetch function\n\n    var fetchFn = function fetchFn() {\n      if (!_this2.options.queryFn) {\n        return Promise.reject('Missing queryFn');\n      }\n\n      _this2.abortSignalConsumed = false;\n      return _this2.options.queryFn(queryFnContext);\n    }; // Trigger behavior hook\n\n\n    var context = {\n      fetchOptions: fetchOptions,\n      options: this.options,\n      queryKey: queryKey,\n      state: this.state,\n      fetchFn: fetchFn,\n      meta: this.meta\n    };\n\n    if ((_this$options$behavio = this.options.behavior) == null ? void 0 : _this$options$behavio.onFetch) {\n      var _this$options$behavio2;\n\n      (_this$options$behavio2 = this.options.behavior) == null ? void 0 : _this$options$behavio2.onFetch(context);\n    } // Store state in case the current fetch needs to be reverted\n\n\n    this.revertState = this.state; // Set to fetching state if not already in it\n\n    if (!this.state.isFetching || this.state.fetchMeta !== ((_context$fetchOptions = context.fetchOptions) == null ? void 0 : _context$fetchOptions.meta)) {\n      var _context$fetchOptions2;\n\n      this.dispatch({\n        type: 'fetch',\n        meta: (_context$fetchOptions2 = context.fetchOptions) == null ? void 0 : _context$fetchOptions2.meta\n      });\n    } // Try to fetch the data\n\n\n    this.retryer = new Retryer({\n      fn: context.fetchFn,\n      abort: abortController == null ? void 0 : (_abortController$abor = abortController.abort) == null ? void 0 : _abortController$abor.bind(abortController),\n      onSuccess: function onSuccess(data) {\n        _this2.setData(data); // Notify cache callback\n\n\n        _this2.cache.config.onSuccess == null ? void 0 : _this2.cache.config.onSuccess(data, _this2); // Remove query after fetching if cache time is 0\n\n        if (_this2.cacheTime === 0) {\n          _this2.optionalRemove();\n        }\n      },\n      onError: function onError(error) {\n        // Optimistically update state if needed\n        if (!(isCancelledError(error) && error.silent)) {\n          _this2.dispatch({\n            type: 'error',\n            error: error\n          });\n        }\n\n        if (!isCancelledError(error)) {\n          // Notify cache callback\n          _this2.cache.config.onError == null ? void 0 : _this2.cache.config.onError(error, _this2); // Log error\n\n          getLogger().error(error);\n        } // Remove query after fetching if cache time is 0\n\n\n        if (_this2.cacheTime === 0) {\n          _this2.optionalRemove();\n        }\n      },\n      onFail: function onFail() {\n        _this2.dispatch({\n          type: 'failed'\n        });\n      },\n      onPause: function onPause() {\n        _this2.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: function onContinue() {\n        _this2.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay\n    });\n    this.promise = this.retryer.promise;\n    return this.promise;\n  };\n\n  _proto.dispatch = function dispatch(action) {\n    var _this3 = this;\n\n    this.state = this.reducer(this.state, action);\n    notifyManager.batch(function () {\n      _this3.observers.forEach(function (observer) {\n        observer.onQueryUpdate(action);\n      });\n\n      _this3.cache.notify({\n        query: _this3,\n        type: 'queryUpdated',\n        action: action\n      });\n    });\n  };\n\n  _proto.getDefaultState = function getDefaultState(options) {\n    var data = typeof options.initialData === 'function' ? options.initialData() : options.initialData;\n    var hasInitialData = typeof options.initialData !== 'undefined';\n    var initialDataUpdatedAt = hasInitialData ? typeof options.initialDataUpdatedAt === 'function' ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n    var hasData = typeof data !== 'undefined';\n    return {\n      data: data,\n      dataUpdateCount: 0,\n      dataUpdatedAt: hasData ? initialDataUpdatedAt != null ? initialDataUpdatedAt : Date.now() : 0,\n      error: null,\n      errorUpdateCount: 0,\n      errorUpdatedAt: 0,\n      fetchFailureCount: 0,\n      fetchMeta: null,\n      isFetching: false,\n      isInvalidated: false,\n      isPaused: false,\n      status: hasData ? 'success' : 'idle'\n    };\n  };\n\n  _proto.reducer = function reducer(state, action) {\n    var _action$meta, _action$dataUpdatedAt;\n\n    switch (action.type) {\n      case 'failed':\n        return _extends({}, state, {\n          fetchFailureCount: state.fetchFailureCount + 1\n        });\n\n      case 'pause':\n        return _extends({}, state, {\n          isPaused: true\n        });\n\n      case 'continue':\n        return _extends({}, state, {\n          isPaused: false\n        });\n\n      case 'fetch':\n        return _extends({}, state, {\n          fetchFailureCount: 0,\n          fetchMeta: (_action$meta = action.meta) != null ? _action$meta : null,\n          isFetching: true,\n          isPaused: false\n        }, !state.dataUpdatedAt && {\n          error: null,\n          status: 'loading'\n        });\n\n      case 'success':\n        return _extends({}, state, {\n          data: action.data,\n          dataUpdateCount: state.dataUpdateCount + 1,\n          dataUpdatedAt: (_action$dataUpdatedAt = action.dataUpdatedAt) != null ? _action$dataUpdatedAt : Date.now(),\n          error: null,\n          fetchFailureCount: 0,\n          isFetching: false,\n          isInvalidated: false,\n          isPaused: false,\n          status: 'success'\n        });\n\n      case 'error':\n        var error = action.error;\n\n        if (isCancelledError(error) && error.revert && this.revertState) {\n          return _extends({}, this.revertState);\n        }\n\n        return _extends({}, state, {\n          error: error,\n          errorUpdateCount: state.errorUpdateCount + 1,\n          errorUpdatedAt: Date.now(),\n          fetchFailureCount: state.fetchFailureCount + 1,\n          isFetching: false,\n          isPaused: false,\n          status: 'error'\n        });\n\n      case 'invalidate':\n        return _extends({}, state, {\n          isInvalidated: true\n        });\n\n      case 'setState':\n        return _extends({}, state, action.state);\n\n      default:\n        return state;\n    }\n  };\n\n  return Query;\n}();"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,kBAAkB,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,IAAI,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,mBAAmB,QAAQ,SAAS;AAC3I,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,SAAS,QAAQ,UAAU;AACpC,SAASC,OAAO,EAAEC,gBAAgB,QAAQ,WAAW,CAAC,CAAC;;AAEvD;AACA,OAAO,IAAIC,KAAK,GAAG,aAAa,YAAY;EAC1C,SAASA,KAAKA,CAACC,MAAM,EAAE;IACrB,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,cAAc,GAAGH,MAAM,CAACG,cAAc;IAC3C,IAAI,CAACC,UAAU,CAACJ,MAAM,CAACK,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,KAAK,GAAGP,MAAM,CAACO,KAAK;IACzB,IAAI,CAACC,QAAQ,GAAGR,MAAM,CAACQ,QAAQ;IAC/B,IAAI,CAACC,SAAS,GAAGT,MAAM,CAACS,SAAS;IACjC,IAAI,CAACC,YAAY,GAAGV,MAAM,CAACW,KAAK,IAAI,IAAI,CAACC,eAAe,CAAC,IAAI,CAACP,OAAO,CAAC;IACtE,IAAI,CAACM,KAAK,GAAG,IAAI,CAACD,YAAY;IAC9B,IAAI,CAACG,IAAI,GAAGb,MAAM,CAACa,IAAI;IACvB,IAAI,CAACC,UAAU,CAAC,CAAC;EACnB;EAEA,IAAIC,MAAM,GAAGhB,KAAK,CAACiB,SAAS;EAE5BD,MAAM,CAACX,UAAU,GAAG,SAASA,UAAUA,CAACC,OAAO,EAAE;IAC/C,IAAIY,qBAAqB;IAEzB,IAAI,CAACZ,OAAO,GAAGlB,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACgB,cAAc,EAAEE,OAAO,CAAC;IACzD,IAAI,CAACQ,IAAI,GAAGR,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACQ,IAAI,CAAC,CAAC;;IAErD,IAAI,CAACK,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACF,SAAS,IAAI,CAAC,EAAE,CAACD,qBAAqB,GAAG,IAAI,CAACZ,OAAO,CAACa,SAAS,KAAK,IAAI,GAAGD,qBAAqB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;EAClJ,CAAC;EAEDF,MAAM,CAACM,iBAAiB,GAAG,SAASA,iBAAiBA,CAAChB,OAAO,EAAE;IAC7D,IAAI,CAACF,cAAc,GAAGE,OAAO;EAC/B,CAAC;EAEDU,MAAM,CAACD,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACxC,IAAIQ,KAAK,GAAG,IAAI;IAEhB,IAAI,CAACC,cAAc,CAAC,CAAC;IAErB,IAAIjC,cAAc,CAAC,IAAI,CAAC4B,SAAS,CAAC,EAAE;MAClC,IAAI,CAACM,SAAS,GAAGC,UAAU,CAAC,YAAY;QACtCH,KAAK,CAACI,cAAc,CAAC,CAAC;MACxB,CAAC,EAAE,IAAI,CAACR,SAAS,CAAC;IACpB;EACF,CAAC;EAEDH,MAAM,CAACQ,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAChD,IAAI,IAAI,CAACC,SAAS,EAAE;MAClBG,YAAY,CAAC,IAAI,CAACH,SAAS,CAAC;MAC5B,IAAI,CAACA,SAAS,GAAGI,SAAS;IAC5B;EACF,CAAC;EAEDb,MAAM,CAACW,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAChD,IAAI,CAAC,IAAI,CAACpB,SAAS,CAACuB,MAAM,EAAE;MAC1B,IAAI,IAAI,CAAClB,KAAK,CAACmB,UAAU,EAAE;QACzB,IAAI,IAAI,CAAC5B,YAAY,EAAE;UACrB,IAAI,CAACY,UAAU,CAAC,CAAC;QACnB;MACF,CAAC,MAAM;QACL,IAAI,CAACP,KAAK,CAACwB,MAAM,CAAC,IAAI,CAAC;MACzB;IACF;EACF,CAAC;EAEDhB,MAAM,CAACiB,OAAO,GAAG,SAASA,OAAOA,CAACC,OAAO,EAAE5B,OAAO,EAAE;IAClD,IAAI6B,qBAAqB,EAAEC,aAAa;IAExC,IAAIC,QAAQ,GAAG,IAAI,CAACzB,KAAK,CAAC0B,IAAI,CAAC,CAAC;;IAEhC,IAAIA,IAAI,GAAGhD,gBAAgB,CAAC4C,OAAO,EAAEG,QAAQ,CAAC,CAAC,CAAC;;IAEhD,IAAI,CAACF,qBAAqB,GAAG,CAACC,aAAa,GAAG,IAAI,CAAC9B,OAAO,EAAEiC,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,qBAAqB,CAACK,IAAI,CAACJ,aAAa,EAAEC,QAAQ,EAAEC,IAAI,CAAC,EAAE;MACrJA,IAAI,GAAGD,QAAQ;IACjB,CAAC,MAAM,IAAI,IAAI,CAAC/B,OAAO,CAACmC,iBAAiB,KAAK,KAAK,EAAE;MACnD;MACAH,IAAI,GAAG7C,gBAAgB,CAAC4C,QAAQ,EAAEC,IAAI,CAAC;IACzC,CAAC,CAAC;;IAGF,IAAI,CAACI,QAAQ,CAAC;MACZJ,IAAI,EAAEA,IAAI;MACVK,IAAI,EAAE,SAAS;MACfC,aAAa,EAAEtC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACuC;IACpD,CAAC,CAAC;IACF,OAAOP,IAAI;EACb,CAAC;EAEDtB,MAAM,CAAC8B,QAAQ,GAAG,SAASA,QAAQA,CAAClC,KAAK,EAAEmC,eAAe,EAAE;IAC1D,IAAI,CAACL,QAAQ,CAAC;MACZC,IAAI,EAAE,UAAU;MAChB/B,KAAK,EAAEA,KAAK;MACZmC,eAAe,EAAEA;IACnB,CAAC,CAAC;EACJ,CAAC;EAED/B,MAAM,CAACgC,MAAM,GAAG,SAASA,MAAMA,CAAC1C,OAAO,EAAE;IACvC,IAAI2C,aAAa;IAEjB,IAAIC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,CAACD,aAAa,GAAG,IAAI,CAACE,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,aAAa,CAACD,MAAM,CAAC1C,OAAO,CAAC;IAC/E,OAAO4C,OAAO,GAAGA,OAAO,CAACE,IAAI,CAAC5D,IAAI,CAAC,CAAC6D,KAAK,CAAC7D,IAAI,CAAC,GAAG8D,OAAO,CAACC,OAAO,CAAC,CAAC;EACrE,CAAC;EAEDvC,MAAM,CAACwC,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAClC,IAAI,CAAChC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACwB,MAAM,CAAC;MACVS,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAEDzC,MAAM,CAAC0C,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC9B,IAAI,CAACF,OAAO,CAAC,CAAC;IACd,IAAI,CAACV,QAAQ,CAAC,IAAI,CAACnC,YAAY,CAAC;EAClC,CAAC;EAEDK,MAAM,CAAC2C,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACpC,OAAO,IAAI,CAACpD,SAAS,CAACqD,IAAI,CAAC,UAAUC,QAAQ,EAAE;MAC7C,OAAOA,QAAQ,CAACvD,OAAO,CAACwD,OAAO,KAAK,KAAK;IAC3C,CAAC,CAAC;EACJ,CAAC;EAED9C,MAAM,CAACe,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACxC,OAAO,IAAI,CAACnB,KAAK,CAACmB,UAAU;EAC9B,CAAC;EAEDf,MAAM,CAAC+C,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAClC,OAAO,IAAI,CAACnD,KAAK,CAACoD,aAAa,IAAI,CAAC,IAAI,CAACpD,KAAK,CAACgC,aAAa,IAAI,IAAI,CAACrC,SAAS,CAACqD,IAAI,CAAC,UAAUC,QAAQ,EAAE;MACtG,OAAOA,QAAQ,CAACI,gBAAgB,CAAC,CAAC,CAACF,OAAO;IAC5C,CAAC,CAAC;EACJ,CAAC;EAED/C,MAAM,CAACkD,aAAa,GAAG,SAASA,aAAaA,CAACC,SAAS,EAAE;IACvD,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;MACxBA,SAAS,GAAG,CAAC;IACf;IAEA,OAAO,IAAI,CAACvD,KAAK,CAACoD,aAAa,IAAI,CAAC,IAAI,CAACpD,KAAK,CAACgC,aAAa,IAAI,CAAClD,cAAc,CAAC,IAAI,CAACkB,KAAK,CAACgC,aAAa,EAAEuB,SAAS,CAAC;EACtH,CAAC;EAEDnD,MAAM,CAACoD,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAClC,IAAIC,cAAc;IAElB,IAAIR,QAAQ,GAAG,IAAI,CAACtD,SAAS,CAAC+D,IAAI,CAAC,UAAUC,CAAC,EAAE;MAC9C,OAAOA,CAAC,CAACC,wBAAwB,CAAC,CAAC;IACrC,CAAC,CAAC;IAEF,IAAIX,QAAQ,EAAE;MACZA,QAAQ,CAACY,OAAO,CAAC,CAAC;IACpB,CAAC,CAAC;;IAGF,CAACJ,cAAc,GAAG,IAAI,CAAClB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkB,cAAc,CAACK,QAAQ,CAAC,CAAC;EAC9E,CAAC;EAED1D,MAAM,CAAC2D,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACpC,IAAIC,cAAc;IAElB,IAAIf,QAAQ,GAAG,IAAI,CAACtD,SAAS,CAAC+D,IAAI,CAAC,UAAUC,CAAC,EAAE;MAC9C,OAAOA,CAAC,CAACM,sBAAsB,CAAC,CAAC;IACnC,CAAC,CAAC;IAEF,IAAIhB,QAAQ,EAAE;MACZA,QAAQ,CAACY,OAAO,CAAC,CAAC;IACpB,CAAC,CAAC;;IAGF,CAACG,cAAc,GAAG,IAAI,CAACzB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyB,cAAc,CAACF,QAAQ,CAAC,CAAC;EAC9E,CAAC;EAED1D,MAAM,CAAC8D,WAAW,GAAG,SAASA,WAAWA,CAACjB,QAAQ,EAAE;IAClD,IAAI,IAAI,CAACtD,SAAS,CAACwE,OAAO,CAAClB,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MAC3C,IAAI,CAACtD,SAAS,CAACyE,IAAI,CAACnB,QAAQ,CAAC;MAC7B,IAAI,CAAC1D,YAAY,GAAG,IAAI,CAAC,CAAC;;MAE1B,IAAI,CAACqB,cAAc,CAAC,CAAC;MACrB,IAAI,CAAChB,KAAK,CAACyE,MAAM,CAAC;QAChBtC,IAAI,EAAE,eAAe;QACrBuC,KAAK,EAAE,IAAI;QACXrB,QAAQ,EAAEA;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;EAED7C,MAAM,CAACmE,cAAc,GAAG,SAASA,cAAcA,CAACtB,QAAQ,EAAE;IACxD,IAAI,IAAI,CAACtD,SAAS,CAACwE,OAAO,CAAClB,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MAC3C,IAAI,CAACtD,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC6E,MAAM,CAAC,UAAUb,CAAC,EAAE;QAClD,OAAOA,CAAC,KAAKV,QAAQ;MACvB,CAAC,CAAC;MAEF,IAAI,CAAC,IAAI,CAACtD,SAAS,CAACuB,MAAM,EAAE;QAC1B;QACA;QACA,IAAI,IAAI,CAACqB,OAAO,EAAE;UAChB,IAAI,IAAI,CAACA,OAAO,CAACkC,qBAAqB,IAAI,IAAI,CAACnF,mBAAmB,EAAE;YAClE,IAAI,CAACiD,OAAO,CAACH,MAAM,CAAC;cAClBsC,MAAM,EAAE;YACV,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,IAAI,CAACnC,OAAO,CAACoC,WAAW,CAAC,CAAC;UAC5B;QACF;QAEA,IAAI,IAAI,CAACpE,SAAS,EAAE;UAClB,IAAI,CAACJ,UAAU,CAAC,CAAC;QACnB,CAAC,MAAM;UACL,IAAI,CAACP,KAAK,CAACwB,MAAM,CAAC,IAAI,CAAC;QACzB;MACF;MAEA,IAAI,CAACxB,KAAK,CAACyE,MAAM,CAAC;QAChBtC,IAAI,EAAE,iBAAiB;QACvBuC,KAAK,EAAE,IAAI;QACXrB,QAAQ,EAAEA;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;EAED7C,MAAM,CAACwE,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACtD,OAAO,IAAI,CAACjF,SAAS,CAACuB,MAAM;EAC9B,CAAC;EAEDd,MAAM,CAACyE,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACxC,IAAI,CAAC,IAAI,CAAC7E,KAAK,CAACoD,aAAa,EAAE;MAC7B,IAAI,CAACtB,QAAQ,CAAC;QACZC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC;EAED3B,MAAM,CAAC0E,KAAK,GAAG,SAASA,KAAKA,CAACpF,OAAO,EAAEqF,YAAY,EAAE;IACnD,IAAIC,MAAM,GAAG,IAAI;MACbC,qBAAqB;MACrBC,qBAAqB;MACrBC,qBAAqB;IAEzB,IAAI,IAAI,CAACnF,KAAK,CAACmB,UAAU,EAAE;MACzB,IAAI,IAAI,CAACnB,KAAK,CAACgC,aAAa,KAAK+C,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACK,aAAa,CAAC,EAAE;QAC5F;QACA,IAAI,CAAChD,MAAM,CAAC;UACVS,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,IAAI,CAACP,OAAO,EAAE;QACvB,IAAI+C,cAAc;;QAElB;QACA,CAACA,cAAc,GAAG,IAAI,CAAC9C,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG8C,cAAc,CAACC,aAAa,CAAC,CAAC,CAAC,CAAC;;QAEnF,OAAO,IAAI,CAAChD,OAAO;MACrB;IACF,CAAC,CAAC;;IAGF,IAAI5C,OAAO,EAAE;MACX,IAAI,CAACD,UAAU,CAACC,OAAO,CAAC;IAC1B,CAAC,CAAC;IACF;;IAGA,IAAI,CAAC,IAAI,CAACA,OAAO,CAAC6F,OAAO,EAAE;MACzB,IAAItC,QAAQ,GAAG,IAAI,CAACtD,SAAS,CAAC+D,IAAI,CAAC,UAAUC,CAAC,EAAE;QAC9C,OAAOA,CAAC,CAACjE,OAAO,CAAC6F,OAAO;MAC1B,CAAC,CAAC;MAEF,IAAItC,QAAQ,EAAE;QACZ,IAAI,CAACxD,UAAU,CAACwD,QAAQ,CAACvD,OAAO,CAAC;MACnC;IACF;IAEA,IAAIG,QAAQ,GAAGd,mBAAmB,CAAC,IAAI,CAACc,QAAQ,CAAC;IACjD,IAAI2F,eAAe,GAAG/G,kBAAkB,CAAC,CAAC,CAAC,CAAC;;IAE5C,IAAIgH,cAAc,GAAG;MACnB5F,QAAQ,EAAEA,QAAQ;MAClB6F,SAAS,EAAEzE,SAAS;MACpBf,IAAI,EAAE,IAAI,CAACA;IACb,CAAC;IACDyF,MAAM,CAACC,cAAc,CAACH,cAAc,EAAE,QAAQ,EAAE;MAC9CI,UAAU,EAAE,IAAI;MAChBC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;QAClB,IAAIN,eAAe,EAAE;UACnBR,MAAM,CAAC1F,mBAAmB,GAAG,IAAI;UACjC,OAAOkG,eAAe,CAACO,MAAM;QAC/B;QAEA,OAAO9E,SAAS;MAClB;IACF,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAI+E,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;MAC/B,IAAI,CAAChB,MAAM,CAACtF,OAAO,CAAC6F,OAAO,EAAE;QAC3B,OAAO7C,OAAO,CAACuD,MAAM,CAAC,iBAAiB,CAAC;MAC1C;MAEAjB,MAAM,CAAC1F,mBAAmB,GAAG,KAAK;MAClC,OAAO0F,MAAM,CAACtF,OAAO,CAAC6F,OAAO,CAACE,cAAc,CAAC;IAC/C,CAAC,CAAC,CAAC;;IAGH,IAAIS,OAAO,GAAG;MACZnB,YAAY,EAAEA,YAAY;MAC1BrF,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBG,QAAQ,EAAEA,QAAQ;MAClBG,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBgG,OAAO,EAAEA,OAAO;MAChB9F,IAAI,EAAE,IAAI,CAACA;IACb,CAAC;IAED,IAAI,CAAC+E,qBAAqB,GAAG,IAAI,CAACvF,OAAO,CAACyG,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGlB,qBAAqB,CAACmB,OAAO,EAAE;MACpG,IAAIC,sBAAsB;MAE1B,CAACA,sBAAsB,GAAG,IAAI,CAAC3G,OAAO,CAACyG,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,sBAAsB,CAACD,OAAO,CAACF,OAAO,CAAC;IAC7G,CAAC,CAAC;;IAGF,IAAI,CAACI,WAAW,GAAG,IAAI,CAACtG,KAAK,CAAC,CAAC;;IAE/B,IAAI,CAAC,IAAI,CAACA,KAAK,CAACmB,UAAU,IAAI,IAAI,CAACnB,KAAK,CAACuG,SAAS,MAAM,CAACrB,qBAAqB,GAAGgB,OAAO,CAACnB,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGG,qBAAqB,CAAChF,IAAI,CAAC,EAAE;MACrJ,IAAIsG,sBAAsB;MAE1B,IAAI,CAAC1E,QAAQ,CAAC;QACZC,IAAI,EAAE,OAAO;QACb7B,IAAI,EAAE,CAACsG,sBAAsB,GAAGN,OAAO,CAACnB,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyB,sBAAsB,CAACtG;MAClG,CAAC,CAAC;IACJ,CAAC,CAAC;;IAGF,IAAI,CAACqC,OAAO,GAAG,IAAIrD,OAAO,CAAC;MACzBuH,EAAE,EAAEP,OAAO,CAACF,OAAO;MACnBU,KAAK,EAAElB,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAACL,qBAAqB,GAAGK,eAAe,CAACkB,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGvB,qBAAqB,CAACwB,IAAI,CAACnB,eAAe,CAAC;MACxJoB,SAAS,EAAE,SAASA,SAASA,CAAClF,IAAI,EAAE;QAClCsD,MAAM,CAAC3D,OAAO,CAACK,IAAI,CAAC,CAAC,CAAC;;QAGtBsD,MAAM,CAACpF,KAAK,CAACP,MAAM,CAACuH,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG5B,MAAM,CAACpF,KAAK,CAACP,MAAM,CAACuH,SAAS,CAAClF,IAAI,EAAEsD,MAAM,CAAC,CAAC,CAAC;;QAE9F,IAAIA,MAAM,CAACzE,SAAS,KAAK,CAAC,EAAE;UAC1ByE,MAAM,CAACjE,cAAc,CAAC,CAAC;QACzB;MACF,CAAC;MACD8F,OAAO,EAAE,SAASA,OAAOA,CAACC,KAAK,EAAE;QAC/B;QACA,IAAI,EAAE3H,gBAAgB,CAAC2H,KAAK,CAAC,IAAIA,KAAK,CAACjE,MAAM,CAAC,EAAE;UAC9CmC,MAAM,CAAClD,QAAQ,CAAC;YACdC,IAAI,EAAE,OAAO;YACb+E,KAAK,EAAEA;UACT,CAAC,CAAC;QACJ;QAEA,IAAI,CAAC3H,gBAAgB,CAAC2H,KAAK,CAAC,EAAE;UAC5B;UACA9B,MAAM,CAACpF,KAAK,CAACP,MAAM,CAACwH,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG7B,MAAM,CAACpF,KAAK,CAACP,MAAM,CAACwH,OAAO,CAACC,KAAK,EAAE9B,MAAM,CAAC,CAAC,CAAC;;UAE3F/F,SAAS,CAAC,CAAC,CAAC6H,KAAK,CAACA,KAAK,CAAC;QAC1B,CAAC,CAAC;;QAGF,IAAI9B,MAAM,CAACzE,SAAS,KAAK,CAAC,EAAE;UAC1ByE,MAAM,CAACjE,cAAc,CAAC,CAAC;QACzB;MACF,CAAC;MACDgG,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;QACxB/B,MAAM,CAAClD,QAAQ,CAAC;UACdC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC;MACDiF,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1BhC,MAAM,CAAClD,QAAQ,CAAC;UACdC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC;MACDkF,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChCjC,MAAM,CAAClD,QAAQ,CAAC;UACdC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC;MACDmF,KAAK,EAAEhB,OAAO,CAACxG,OAAO,CAACwH,KAAK;MAC5BC,UAAU,EAAEjB,OAAO,CAACxG,OAAO,CAACyH;IAC9B,CAAC,CAAC;IACF,IAAI,CAAC7E,OAAO,GAAG,IAAI,CAACC,OAAO,CAACD,OAAO;IACnC,OAAO,IAAI,CAACA,OAAO;EACrB,CAAC;EAEDlC,MAAM,CAAC0B,QAAQ,GAAG,SAASA,QAAQA,CAACsF,MAAM,EAAE;IAC1C,IAAIC,MAAM,GAAG,IAAI;IAEjB,IAAI,CAACrH,KAAK,GAAG,IAAI,CAACsH,OAAO,CAAC,IAAI,CAACtH,KAAK,EAAEoH,MAAM,CAAC;IAC7CpI,aAAa,CAACuI,KAAK,CAAC,YAAY;MAC9BF,MAAM,CAAC1H,SAAS,CAAC6H,OAAO,CAAC,UAAUvE,QAAQ,EAAE;QAC3CA,QAAQ,CAACwE,aAAa,CAACL,MAAM,CAAC;MAChC,CAAC,CAAC;MAEFC,MAAM,CAACzH,KAAK,CAACyE,MAAM,CAAC;QAClBC,KAAK,EAAE+C,MAAM;QACbtF,IAAI,EAAE,cAAc;QACpBqF,MAAM,EAAEA;MACV,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAEDhH,MAAM,CAACH,eAAe,GAAG,SAASA,eAAeA,CAACP,OAAO,EAAE;IACzD,IAAIgC,IAAI,GAAG,OAAOhC,OAAO,CAACgI,WAAW,KAAK,UAAU,GAAGhI,OAAO,CAACgI,WAAW,CAAC,CAAC,GAAGhI,OAAO,CAACgI,WAAW;IAClG,IAAIC,cAAc,GAAG,OAAOjI,OAAO,CAACgI,WAAW,KAAK,WAAW;IAC/D,IAAIE,oBAAoB,GAAGD,cAAc,GAAG,OAAOjI,OAAO,CAACkI,oBAAoB,KAAK,UAAU,GAAGlI,OAAO,CAACkI,oBAAoB,CAAC,CAAC,GAAGlI,OAAO,CAACkI,oBAAoB,GAAG,CAAC;IAClK,IAAIC,OAAO,GAAG,OAAOnG,IAAI,KAAK,WAAW;IACzC,OAAO;MACLA,IAAI,EAAEA,IAAI;MACVoG,eAAe,EAAE,CAAC;MAClB9F,aAAa,EAAE6F,OAAO,GAAGD,oBAAoB,IAAI,IAAI,GAAGA,oBAAoB,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;MAC7FlB,KAAK,EAAE,IAAI;MACXmB,gBAAgB,EAAE,CAAC;MACnBC,cAAc,EAAE,CAAC;MACjBC,iBAAiB,EAAE,CAAC;MACpB5B,SAAS,EAAE,IAAI;MACfpF,UAAU,EAAE,KAAK;MACjBiC,aAAa,EAAE,KAAK;MACpBgF,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAER,OAAO,GAAG,SAAS,GAAG;IAChC,CAAC;EACH,CAAC;EAEDzH,MAAM,CAACkH,OAAO,GAAG,SAASA,OAAOA,CAACtH,KAAK,EAAEoH,MAAM,EAAE;IAC/C,IAAIkB,YAAY,EAAEC,qBAAqB;IAEvC,QAAQnB,MAAM,CAACrF,IAAI;MACjB,KAAK,QAAQ;QACX,OAAOvD,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;UACzBmI,iBAAiB,EAAEnI,KAAK,CAACmI,iBAAiB,GAAG;QAC/C,CAAC,CAAC;MAEJ,KAAK,OAAO;QACV,OAAO3J,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;UACzBoI,QAAQ,EAAE;QACZ,CAAC,CAAC;MAEJ,KAAK,UAAU;QACb,OAAO5J,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;UACzBoI,QAAQ,EAAE;QACZ,CAAC,CAAC;MAEJ,KAAK,OAAO;QACV,OAAO5J,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;UACzBmI,iBAAiB,EAAE,CAAC;UACpB5B,SAAS,EAAE,CAAC+B,YAAY,GAAGlB,MAAM,CAAClH,IAAI,KAAK,IAAI,GAAGoI,YAAY,GAAG,IAAI;UACrEnH,UAAU,EAAE,IAAI;UAChBiH,QAAQ,EAAE;QACZ,CAAC,EAAE,CAACpI,KAAK,CAACgC,aAAa,IAAI;UACzB8E,KAAK,EAAE,IAAI;UACXuB,MAAM,EAAE;QACV,CAAC,CAAC;MAEJ,KAAK,SAAS;QACZ,OAAO7J,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;UACzB0B,IAAI,EAAE0F,MAAM,CAAC1F,IAAI;UACjBoG,eAAe,EAAE9H,KAAK,CAAC8H,eAAe,GAAG,CAAC;UAC1C9F,aAAa,EAAE,CAACuG,qBAAqB,GAAGnB,MAAM,CAACpF,aAAa,KAAK,IAAI,GAAGuG,qBAAqB,GAAGR,IAAI,CAACC,GAAG,CAAC,CAAC;UAC1GlB,KAAK,EAAE,IAAI;UACXqB,iBAAiB,EAAE,CAAC;UACpBhH,UAAU,EAAE,KAAK;UACjBiC,aAAa,EAAE,KAAK;UACpBgF,QAAQ,EAAE,KAAK;UACfC,MAAM,EAAE;QACV,CAAC,CAAC;MAEJ,KAAK,OAAO;QACV,IAAIvB,KAAK,GAAGM,MAAM,CAACN,KAAK;QAExB,IAAI3H,gBAAgB,CAAC2H,KAAK,CAAC,IAAIA,KAAK,CAACpC,MAAM,IAAI,IAAI,CAAC4B,WAAW,EAAE;UAC/D,OAAO9H,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC8H,WAAW,CAAC;QACvC;QAEA,OAAO9H,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;UACzB8G,KAAK,EAAEA,KAAK;UACZmB,gBAAgB,EAAEjI,KAAK,CAACiI,gBAAgB,GAAG,CAAC;UAC5CC,cAAc,EAAEH,IAAI,CAACC,GAAG,CAAC,CAAC;UAC1BG,iBAAiB,EAAEnI,KAAK,CAACmI,iBAAiB,GAAG,CAAC;UAC9ChH,UAAU,EAAE,KAAK;UACjBiH,QAAQ,EAAE,KAAK;UACfC,MAAM,EAAE;QACV,CAAC,CAAC;MAEJ,KAAK,YAAY;QACf,OAAO7J,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;UACzBoD,aAAa,EAAE;QACjB,CAAC,CAAC;MAEJ,KAAK,UAAU;QACb,OAAO5E,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAEoH,MAAM,CAACpH,KAAK,CAAC;MAE1C;QACE,OAAOA,KAAK;IAChB;EACF,CAAC;EAED,OAAOZ,KAAK;AACd,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}