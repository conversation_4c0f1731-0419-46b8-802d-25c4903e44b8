# 웹 프레임워크
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 데이터베이스
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
sqlite3

# AI 및 머신러닝
openai==1.3.7
langchain==0.0.350
langchain-openai==0.0.2
transformers==4.36.2
torch==2.1.1
ollama==0.1.7

# 데이터 처리
pandas==2.1.4
numpy==1.25.2
python-json-logger==2.0.7

# HTTP 클라이언트
httpx==0.25.2
requests==2.31.0

# 인증 및 보안
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 캐싱
redis==5.0.1

# 환경 변수
python-dotenv==1.0.0

# 로깅
loguru==0.7.2

# 테스트
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# 유틸리티
python-dateutil==2.8.2
pytz==2023.3
typing-extensions==4.8.0

# CORS
fastapi-cors==0.0.6

# 파일 처리
python-magic==0.4.27
aiofiles==23.2.1

# 스키마 검증
jsonschema==4.20.0

# 백그라운드 작업
celery==5.3.4
flower==2.0.1
