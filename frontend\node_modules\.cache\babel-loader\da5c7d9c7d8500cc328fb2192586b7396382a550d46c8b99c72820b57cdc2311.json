{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { hashQuery<PERSON>ey, noop, parseFilterArgs, parseQueryArgs, partialMatch<PERSON>ey, hashQueryKeyByOptions } from './utils';\nimport { QueryCache } from './queryCache';\nimport { MutationCache } from './mutationCache';\nimport { focusManager } from './focusManager';\nimport { onlineManager } from './onlineManager';\nimport { notifyManager } from './notifyManager';\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior';\n// CLASS\nexport var QueryClient = /*#__PURE__*/function () {\n  function QueryClient(config) {\n    if (config === void 0) {\n      config = {};\n    }\n    this.queryCache = config.queryCache || new QueryCache();\n    this.mutationCache = config.mutationCache || new MutationCache();\n    this.defaultOptions = config.defaultOptions || {};\n    this.queryDefaults = [];\n    this.mutationDefaults = [];\n  }\n  var _proto = QueryClient.prototype;\n  _proto.mount = function mount() {\n    var _this = this;\n    this.unsubscribeFocus = focusManager.subscribe(function () {\n      if (focusManager.isFocused() && onlineManager.isOnline()) {\n        _this.mutationCache.onFocus();\n        _this.queryCache.onFocus();\n      }\n    });\n    this.unsubscribeOnline = onlineManager.subscribe(function () {\n      if (focusManager.isFocused() && onlineManager.isOnline()) {\n        _this.mutationCache.onOnline();\n        _this.queryCache.onOnline();\n      }\n    });\n  };\n  _proto.unmount = function unmount() {\n    var _this$unsubscribeFocu, _this$unsubscribeOnli;\n    (_this$unsubscribeFocu = this.unsubscribeFocus) == null ? void 0 : _this$unsubscribeFocu.call(this);\n    (_this$unsubscribeOnli = this.unsubscribeOnline) == null ? void 0 : _this$unsubscribeOnli.call(this);\n  };\n  _proto.isFetching = function isFetching(arg1, arg2) {\n    var _parseFilterArgs = parseFilterArgs(arg1, arg2),\n      filters = _parseFilterArgs[0];\n    filters.fetching = true;\n    return this.queryCache.findAll(filters).length;\n  };\n  _proto.isMutating = function isMutating(filters) {\n    return this.mutationCache.findAll(_extends({}, filters, {\n      fetching: true\n    })).length;\n  };\n  _proto.getQueryData = function getQueryData(queryKey, filters) {\n    var _this$queryCache$find;\n    return (_this$queryCache$find = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find.state.data;\n  };\n  _proto.getQueriesData = function getQueriesData(queryKeyOrFilters) {\n    return this.getQueryCache().findAll(queryKeyOrFilters).map(function (_ref) {\n      var queryKey = _ref.queryKey,\n        state = _ref.state;\n      var data = state.data;\n      return [queryKey, data];\n    });\n  };\n  _proto.setQueryData = function setQueryData(queryKey, updater, options) {\n    var parsedOptions = parseQueryArgs(queryKey);\n    var defaultedOptions = this.defaultQueryOptions(parsedOptions);\n    return this.queryCache.build(this, defaultedOptions).setData(updater, options);\n  };\n  _proto.setQueriesData = function setQueriesData(queryKeyOrFilters, updater, options) {\n    var _this2 = this;\n    return notifyManager.batch(function () {\n      return _this2.getQueryCache().findAll(queryKeyOrFilters).map(function (_ref2) {\n        var queryKey = _ref2.queryKey;\n        return [queryKey, _this2.setQueryData(queryKey, updater, options)];\n      });\n    });\n  };\n  _proto.getQueryState = function getQueryState(queryKey, filters) {\n    var _this$queryCache$find2;\n    return (_this$queryCache$find2 = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find2.state;\n  };\n  _proto.removeQueries = function removeQueries(arg1, arg2) {\n    var _parseFilterArgs2 = parseFilterArgs(arg1, arg2),\n      filters = _parseFilterArgs2[0];\n    var queryCache = this.queryCache;\n    notifyManager.batch(function () {\n      queryCache.findAll(filters).forEach(function (query) {\n        queryCache.remove(query);\n      });\n    });\n  };\n  _proto.resetQueries = function resetQueries(arg1, arg2, arg3) {\n    var _this3 = this;\n    var _parseFilterArgs3 = parseFilterArgs(arg1, arg2, arg3),\n      filters = _parseFilterArgs3[0],\n      options = _parseFilterArgs3[1];\n    var queryCache = this.queryCache;\n    var refetchFilters = _extends({}, filters, {\n      active: true\n    });\n    return notifyManager.batch(function () {\n      queryCache.findAll(filters).forEach(function (query) {\n        query.reset();\n      });\n      return _this3.refetchQueries(refetchFilters, options);\n    });\n  };\n  _proto.cancelQueries = function cancelQueries(arg1, arg2, arg3) {\n    var _this4 = this;\n    var _parseFilterArgs4 = parseFilterArgs(arg1, arg2, arg3),\n      filters = _parseFilterArgs4[0],\n      _parseFilterArgs4$ = _parseFilterArgs4[1],\n      cancelOptions = _parseFilterArgs4$ === void 0 ? {} : _parseFilterArgs4$;\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true;\n    }\n    var promises = notifyManager.batch(function () {\n      return _this4.queryCache.findAll(filters).map(function (query) {\n        return query.cancel(cancelOptions);\n      });\n    });\n    return Promise.all(promises).then(noop).catch(noop);\n  };\n  _proto.invalidateQueries = function invalidateQueries(arg1, arg2, arg3) {\n    var _ref3,\n      _filters$refetchActiv,\n      _filters$refetchInact,\n      _this5 = this;\n    var _parseFilterArgs5 = parseFilterArgs(arg1, arg2, arg3),\n      filters = _parseFilterArgs5[0],\n      options = _parseFilterArgs5[1];\n    var refetchFilters = _extends({}, filters, {\n      // if filters.refetchActive is not provided and filters.active is explicitly false,\n      // e.g. invalidateQueries({ active: false }), we don't want to refetch active queries\n      active: (_ref3 = (_filters$refetchActiv = filters.refetchActive) != null ? _filters$refetchActiv : filters.active) != null ? _ref3 : true,\n      inactive: (_filters$refetchInact = filters.refetchInactive) != null ? _filters$refetchInact : false\n    });\n    return notifyManager.batch(function () {\n      _this5.queryCache.findAll(filters).forEach(function (query) {\n        query.invalidate();\n      });\n      return _this5.refetchQueries(refetchFilters, options);\n    });\n  };\n  _proto.refetchQueries = function refetchQueries(arg1, arg2, arg3) {\n    var _this6 = this;\n    var _parseFilterArgs6 = parseFilterArgs(arg1, arg2, arg3),\n      filters = _parseFilterArgs6[0],\n      options = _parseFilterArgs6[1];\n    var promises = notifyManager.batch(function () {\n      return _this6.queryCache.findAll(filters).map(function (query) {\n        return query.fetch(undefined, _extends({}, options, {\n          meta: {\n            refetchPage: filters == null ? void 0 : filters.refetchPage\n          }\n        }));\n      });\n    });\n    var promise = Promise.all(promises).then(noop);\n    if (!(options == null ? void 0 : options.throwOnError)) {\n      promise = promise.catch(noop);\n    }\n    return promise;\n  };\n  _proto.fetchQuery = function fetchQuery(arg1, arg2, arg3) {\n    var parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n    var defaultedOptions = this.defaultQueryOptions(parsedOptions); // https://github.com/tannerlinsley/react-query/issues/652\n\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false;\n    }\n    var query = this.queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(defaultedOptions.staleTime) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  };\n  _proto.prefetchQuery = function prefetchQuery(arg1, arg2, arg3) {\n    return this.fetchQuery(arg1, arg2, arg3).then(noop).catch(noop);\n  };\n  _proto.fetchInfiniteQuery = function fetchInfiniteQuery(arg1, arg2, arg3) {\n    var parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n    parsedOptions.behavior = infiniteQueryBehavior();\n    return this.fetchQuery(parsedOptions);\n  };\n  _proto.prefetchInfiniteQuery = function prefetchInfiniteQuery(arg1, arg2, arg3) {\n    return this.fetchInfiniteQuery(arg1, arg2, arg3).then(noop).catch(noop);\n  };\n  _proto.cancelMutations = function cancelMutations() {\n    var _this7 = this;\n    var promises = notifyManager.batch(function () {\n      return _this7.mutationCache.getAll().map(function (mutation) {\n        return mutation.cancel();\n      });\n    });\n    return Promise.all(promises).then(noop).catch(noop);\n  };\n  _proto.resumePausedMutations = function resumePausedMutations() {\n    return this.getMutationCache().resumePausedMutations();\n  };\n  _proto.executeMutation = function executeMutation(options) {\n    return this.mutationCache.build(this, options).execute();\n  };\n  _proto.getQueryCache = function getQueryCache() {\n    return this.queryCache;\n  };\n  _proto.getMutationCache = function getMutationCache() {\n    return this.mutationCache;\n  };\n  _proto.getDefaultOptions = function getDefaultOptions() {\n    return this.defaultOptions;\n  };\n  _proto.setDefaultOptions = function setDefaultOptions(options) {\n    this.defaultOptions = options;\n  };\n  _proto.setQueryDefaults = function setQueryDefaults(queryKey, options) {\n    var result = this.queryDefaults.find(function (x) {\n      return hashQueryKey(queryKey) === hashQueryKey(x.queryKey);\n    });\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.queryDefaults.push({\n        queryKey: queryKey,\n        defaultOptions: options\n      });\n    }\n  };\n  _proto.getQueryDefaults = function getQueryDefaults(queryKey) {\n    var _this$queryDefaults$f;\n    return queryKey ? (_this$queryDefaults$f = this.queryDefaults.find(function (x) {\n      return partialMatchKey(queryKey, x.queryKey);\n    })) == null ? void 0 : _this$queryDefaults$f.defaultOptions : undefined;\n  };\n  _proto.setMutationDefaults = function setMutationDefaults(mutationKey, options) {\n    var result = this.mutationDefaults.find(function (x) {\n      return hashQueryKey(mutationKey) === hashQueryKey(x.mutationKey);\n    });\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.mutationDefaults.push({\n        mutationKey: mutationKey,\n        defaultOptions: options\n      });\n    }\n  };\n  _proto.getMutationDefaults = function getMutationDefaults(mutationKey) {\n    var _this$mutationDefault;\n    return mutationKey ? (_this$mutationDefault = this.mutationDefaults.find(function (x) {\n      return partialMatchKey(mutationKey, x.mutationKey);\n    })) == null ? void 0 : _this$mutationDefault.defaultOptions : undefined;\n  };\n  _proto.defaultQueryOptions = function defaultQueryOptions(options) {\n    if (options == null ? void 0 : options._defaulted) {\n      return options;\n    }\n    var defaultedOptions = _extends({}, this.defaultOptions.queries, this.getQueryDefaults(options == null ? void 0 : options.queryKey), options, {\n      _defaulted: true\n    });\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(defaultedOptions.queryKey, defaultedOptions);\n    }\n    return defaultedOptions;\n  };\n  _proto.defaultQueryObserverOptions = function defaultQueryObserverOptions(options) {\n    return this.defaultQueryOptions(options);\n  };\n  _proto.defaultMutationOptions = function defaultMutationOptions(options) {\n    if (options == null ? void 0 : options._defaulted) {\n      return options;\n    }\n    return _extends({}, this.defaultOptions.mutations, this.getMutationDefaults(options == null ? void 0 : options.mutationKey), options, {\n      _defaulted: true\n    });\n  };\n  _proto.clear = function clear() {\n    this.queryCache.clear();\n    this.mutationCache.clear();\n  };\n  return QueryClient;\n}();", "map": {"version": 3, "names": ["_extends", "hashQuery<PERSON>ey", "noop", "parseFilter<PERSON><PERSON>s", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "partialMatchKey", "hashQueryKeyByOptions", "Query<PERSON>ache", "MutationCache", "focusManager", "onlineManager", "notify<PERSON><PERSON>ger", "infiniteQueryBehavior", "QueryClient", "config", "queryCache", "mutationCache", "defaultOptions", "queryDefaults", "mutationDefaults", "_proto", "prototype", "mount", "_this", "unsubscribeFocus", "subscribe", "isFocused", "isOnline", "onFocus", "unsubscribeOnline", "onOnline", "unmount", "_this$unsubscribeFocu", "_this$unsubscribeOnli", "call", "isFetching", "arg1", "arg2", "_parseFilterArgs", "filters", "fetching", "findAll", "length", "isMutating", "getQueryData", "query<PERSON><PERSON>", "_this$queryCache$find", "find", "state", "data", "getQueriesData", "query<PERSON>eyOrFilters", "get<PERSON><PERSON><PERSON><PERSON>ache", "map", "_ref", "setQueryData", "updater", "options", "parsedOptions", "defaultedOptions", "defaultQueryOptions", "build", "setData", "setQueriesData", "_this2", "batch", "_ref2", "getQueryState", "_this$queryCache$find2", "removeQueries", "_parseFilterArgs2", "for<PERSON>ach", "query", "remove", "resetQueries", "arg3", "_this3", "_parseFilterArgs3", "refetchFilters", "active", "reset", "refetchQueries", "cancelQueries", "_this4", "_parseFilterArgs4", "_parseFilterArgs4$", "cancelOptions", "revert", "promises", "cancel", "Promise", "all", "then", "catch", "invalidateQueries", "_ref3", "_filters$refetchActiv", "_filters$refetchInact", "_this5", "_parseFilterArgs5", "refetchActive", "inactive", "refetchInactive", "invalidate", "_this6", "_parseFilterArgs6", "fetch", "undefined", "meta", "refetchPage", "promise", "throwOnError", "<PERSON><PERSON><PERSON><PERSON>", "retry", "isStaleByTime", "staleTime", "resolve", "prefetch<PERSON><PERSON>y", "fetchInfiniteQuery", "behavior", "prefetchInfiniteQuery", "cancelMutations", "_this7", "getAll", "mutation", "resumePausedMutations", "getMutationCache", "executeMutation", "execute", "getDefaultOptions", "setDefaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "result", "x", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_this$queryDefaults$f", "setMutationDefaults", "<PERSON><PERSON><PERSON>", "getMutationDefaults", "_this$mutationDefault", "_defaulted", "queries", "queryHash", "defaultQueryObserverOptions", "defaultMutationOptions", "mutations", "clear"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/node_modules/react-query/es/core/queryClient.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { hashQuery<PERSON>ey, noop, parseFilterArgs, parseQueryArgs, partialMatch<PERSON>ey, hashQueryKeyByOptions } from './utils';\nimport { QueryCache } from './queryCache';\nimport { MutationCache } from './mutationCache';\nimport { focusManager } from './focusManager';\nimport { onlineManager } from './onlineManager';\nimport { notifyManager } from './notifyManager';\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior';\n// CLASS\nexport var QueryClient = /*#__PURE__*/function () {\n  function QueryClient(config) {\n    if (config === void 0) {\n      config = {};\n    }\n\n    this.queryCache = config.queryCache || new QueryCache();\n    this.mutationCache = config.mutationCache || new MutationCache();\n    this.defaultOptions = config.defaultOptions || {};\n    this.queryDefaults = [];\n    this.mutationDefaults = [];\n  }\n\n  var _proto = QueryClient.prototype;\n\n  _proto.mount = function mount() {\n    var _this = this;\n\n    this.unsubscribeFocus = focusManager.subscribe(function () {\n      if (focusManager.isFocused() && onlineManager.isOnline()) {\n        _this.mutationCache.onFocus();\n\n        _this.queryCache.onFocus();\n      }\n    });\n    this.unsubscribeOnline = onlineManager.subscribe(function () {\n      if (focusManager.isFocused() && onlineManager.isOnline()) {\n        _this.mutationCache.onOnline();\n\n        _this.queryCache.onOnline();\n      }\n    });\n  };\n\n  _proto.unmount = function unmount() {\n    var _this$unsubscribeFocu, _this$unsubscribeOnli;\n\n    (_this$unsubscribeFocu = this.unsubscribeFocus) == null ? void 0 : _this$unsubscribeFocu.call(this);\n    (_this$unsubscribeOnli = this.unsubscribeOnline) == null ? void 0 : _this$unsubscribeOnli.call(this);\n  };\n\n  _proto.isFetching = function isFetching(arg1, arg2) {\n    var _parseFilterArgs = parseFilterArgs(arg1, arg2),\n        filters = _parseFilterArgs[0];\n\n    filters.fetching = true;\n    return this.queryCache.findAll(filters).length;\n  };\n\n  _proto.isMutating = function isMutating(filters) {\n    return this.mutationCache.findAll(_extends({}, filters, {\n      fetching: true\n    })).length;\n  };\n\n  _proto.getQueryData = function getQueryData(queryKey, filters) {\n    var _this$queryCache$find;\n\n    return (_this$queryCache$find = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find.state.data;\n  };\n\n  _proto.getQueriesData = function getQueriesData(queryKeyOrFilters) {\n    return this.getQueryCache().findAll(queryKeyOrFilters).map(function (_ref) {\n      var queryKey = _ref.queryKey,\n          state = _ref.state;\n      var data = state.data;\n      return [queryKey, data];\n    });\n  };\n\n  _proto.setQueryData = function setQueryData(queryKey, updater, options) {\n    var parsedOptions = parseQueryArgs(queryKey);\n    var defaultedOptions = this.defaultQueryOptions(parsedOptions);\n    return this.queryCache.build(this, defaultedOptions).setData(updater, options);\n  };\n\n  _proto.setQueriesData = function setQueriesData(queryKeyOrFilters, updater, options) {\n    var _this2 = this;\n\n    return notifyManager.batch(function () {\n      return _this2.getQueryCache().findAll(queryKeyOrFilters).map(function (_ref2) {\n        var queryKey = _ref2.queryKey;\n        return [queryKey, _this2.setQueryData(queryKey, updater, options)];\n      });\n    });\n  };\n\n  _proto.getQueryState = function getQueryState(queryKey, filters) {\n    var _this$queryCache$find2;\n\n    return (_this$queryCache$find2 = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find2.state;\n  };\n\n  _proto.removeQueries = function removeQueries(arg1, arg2) {\n    var _parseFilterArgs2 = parseFilterArgs(arg1, arg2),\n        filters = _parseFilterArgs2[0];\n\n    var queryCache = this.queryCache;\n    notifyManager.batch(function () {\n      queryCache.findAll(filters).forEach(function (query) {\n        queryCache.remove(query);\n      });\n    });\n  };\n\n  _proto.resetQueries = function resetQueries(arg1, arg2, arg3) {\n    var _this3 = this;\n\n    var _parseFilterArgs3 = parseFilterArgs(arg1, arg2, arg3),\n        filters = _parseFilterArgs3[0],\n        options = _parseFilterArgs3[1];\n\n    var queryCache = this.queryCache;\n\n    var refetchFilters = _extends({}, filters, {\n      active: true\n    });\n\n    return notifyManager.batch(function () {\n      queryCache.findAll(filters).forEach(function (query) {\n        query.reset();\n      });\n      return _this3.refetchQueries(refetchFilters, options);\n    });\n  };\n\n  _proto.cancelQueries = function cancelQueries(arg1, arg2, arg3) {\n    var _this4 = this;\n\n    var _parseFilterArgs4 = parseFilterArgs(arg1, arg2, arg3),\n        filters = _parseFilterArgs4[0],\n        _parseFilterArgs4$ = _parseFilterArgs4[1],\n        cancelOptions = _parseFilterArgs4$ === void 0 ? {} : _parseFilterArgs4$;\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true;\n    }\n\n    var promises = notifyManager.batch(function () {\n      return _this4.queryCache.findAll(filters).map(function (query) {\n        return query.cancel(cancelOptions);\n      });\n    });\n    return Promise.all(promises).then(noop).catch(noop);\n  };\n\n  _proto.invalidateQueries = function invalidateQueries(arg1, arg2, arg3) {\n    var _ref3,\n        _filters$refetchActiv,\n        _filters$refetchInact,\n        _this5 = this;\n\n    var _parseFilterArgs5 = parseFilterArgs(arg1, arg2, arg3),\n        filters = _parseFilterArgs5[0],\n        options = _parseFilterArgs5[1];\n\n    var refetchFilters = _extends({}, filters, {\n      // if filters.refetchActive is not provided and filters.active is explicitly false,\n      // e.g. invalidateQueries({ active: false }), we don't want to refetch active queries\n      active: (_ref3 = (_filters$refetchActiv = filters.refetchActive) != null ? _filters$refetchActiv : filters.active) != null ? _ref3 : true,\n      inactive: (_filters$refetchInact = filters.refetchInactive) != null ? _filters$refetchInact : false\n    });\n\n    return notifyManager.batch(function () {\n      _this5.queryCache.findAll(filters).forEach(function (query) {\n        query.invalidate();\n      });\n\n      return _this5.refetchQueries(refetchFilters, options);\n    });\n  };\n\n  _proto.refetchQueries = function refetchQueries(arg1, arg2, arg3) {\n    var _this6 = this;\n\n    var _parseFilterArgs6 = parseFilterArgs(arg1, arg2, arg3),\n        filters = _parseFilterArgs6[0],\n        options = _parseFilterArgs6[1];\n\n    var promises = notifyManager.batch(function () {\n      return _this6.queryCache.findAll(filters).map(function (query) {\n        return query.fetch(undefined, _extends({}, options, {\n          meta: {\n            refetchPage: filters == null ? void 0 : filters.refetchPage\n          }\n        }));\n      });\n    });\n    var promise = Promise.all(promises).then(noop);\n\n    if (!(options == null ? void 0 : options.throwOnError)) {\n      promise = promise.catch(noop);\n    }\n\n    return promise;\n  };\n\n  _proto.fetchQuery = function fetchQuery(arg1, arg2, arg3) {\n    var parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n    var defaultedOptions = this.defaultQueryOptions(parsedOptions); // https://github.com/tannerlinsley/react-query/issues/652\n\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false;\n    }\n\n    var query = this.queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(defaultedOptions.staleTime) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  };\n\n  _proto.prefetchQuery = function prefetchQuery(arg1, arg2, arg3) {\n    return this.fetchQuery(arg1, arg2, arg3).then(noop).catch(noop);\n  };\n\n  _proto.fetchInfiniteQuery = function fetchInfiniteQuery(arg1, arg2, arg3) {\n    var parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n    parsedOptions.behavior = infiniteQueryBehavior();\n    return this.fetchQuery(parsedOptions);\n  };\n\n  _proto.prefetchInfiniteQuery = function prefetchInfiniteQuery(arg1, arg2, arg3) {\n    return this.fetchInfiniteQuery(arg1, arg2, arg3).then(noop).catch(noop);\n  };\n\n  _proto.cancelMutations = function cancelMutations() {\n    var _this7 = this;\n\n    var promises = notifyManager.batch(function () {\n      return _this7.mutationCache.getAll().map(function (mutation) {\n        return mutation.cancel();\n      });\n    });\n    return Promise.all(promises).then(noop).catch(noop);\n  };\n\n  _proto.resumePausedMutations = function resumePausedMutations() {\n    return this.getMutationCache().resumePausedMutations();\n  };\n\n  _proto.executeMutation = function executeMutation(options) {\n    return this.mutationCache.build(this, options).execute();\n  };\n\n  _proto.getQueryCache = function getQueryCache() {\n    return this.queryCache;\n  };\n\n  _proto.getMutationCache = function getMutationCache() {\n    return this.mutationCache;\n  };\n\n  _proto.getDefaultOptions = function getDefaultOptions() {\n    return this.defaultOptions;\n  };\n\n  _proto.setDefaultOptions = function setDefaultOptions(options) {\n    this.defaultOptions = options;\n  };\n\n  _proto.setQueryDefaults = function setQueryDefaults(queryKey, options) {\n    var result = this.queryDefaults.find(function (x) {\n      return hashQueryKey(queryKey) === hashQueryKey(x.queryKey);\n    });\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.queryDefaults.push({\n        queryKey: queryKey,\n        defaultOptions: options\n      });\n    }\n  };\n\n  _proto.getQueryDefaults = function getQueryDefaults(queryKey) {\n    var _this$queryDefaults$f;\n\n    return queryKey ? (_this$queryDefaults$f = this.queryDefaults.find(function (x) {\n      return partialMatchKey(queryKey, x.queryKey);\n    })) == null ? void 0 : _this$queryDefaults$f.defaultOptions : undefined;\n  };\n\n  _proto.setMutationDefaults = function setMutationDefaults(mutationKey, options) {\n    var result = this.mutationDefaults.find(function (x) {\n      return hashQueryKey(mutationKey) === hashQueryKey(x.mutationKey);\n    });\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.mutationDefaults.push({\n        mutationKey: mutationKey,\n        defaultOptions: options\n      });\n    }\n  };\n\n  _proto.getMutationDefaults = function getMutationDefaults(mutationKey) {\n    var _this$mutationDefault;\n\n    return mutationKey ? (_this$mutationDefault = this.mutationDefaults.find(function (x) {\n      return partialMatchKey(mutationKey, x.mutationKey);\n    })) == null ? void 0 : _this$mutationDefault.defaultOptions : undefined;\n  };\n\n  _proto.defaultQueryOptions = function defaultQueryOptions(options) {\n    if (options == null ? void 0 : options._defaulted) {\n      return options;\n    }\n\n    var defaultedOptions = _extends({}, this.defaultOptions.queries, this.getQueryDefaults(options == null ? void 0 : options.queryKey), options, {\n      _defaulted: true\n    });\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(defaultedOptions.queryKey, defaultedOptions);\n    }\n\n    return defaultedOptions;\n  };\n\n  _proto.defaultQueryObserverOptions = function defaultQueryObserverOptions(options) {\n    return this.defaultQueryOptions(options);\n  };\n\n  _proto.defaultMutationOptions = function defaultMutationOptions(options) {\n    if (options == null ? void 0 : options._defaulted) {\n      return options;\n    }\n\n    return _extends({}, this.defaultOptions.mutations, this.getMutationDefaults(options == null ? void 0 : options.mutationKey), options, {\n      _defaulted: true\n    });\n  };\n\n  _proto.clear = function clear() {\n    this.queryCache.clear();\n    this.mutationCache.clear();\n  };\n\n  return QueryClient;\n}();"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,YAAY,EAAEC,IAAI,EAAEC,eAAe,EAAEC,cAAc,EAAEC,eAAe,EAAEC,qBAAqB,QAAQ,SAAS;AACrH,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D;AACA,OAAO,IAAIC,WAAW,GAAG,aAAa,YAAY;EAChD,SAASA,WAAWA,CAACC,MAAM,EAAE;IAC3B,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MACrBA,MAAM,GAAG,CAAC,CAAC;IACb;IAEA,IAAI,CAACC,UAAU,GAAGD,MAAM,CAACC,UAAU,IAAI,IAAIR,UAAU,CAAC,CAAC;IACvD,IAAI,CAACS,aAAa,GAAGF,MAAM,CAACE,aAAa,IAAI,IAAIR,aAAa,CAAC,CAAC;IAChE,IAAI,CAACS,cAAc,GAAGH,MAAM,CAACG,cAAc,IAAI,CAAC,CAAC;IACjD,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,gBAAgB,GAAG,EAAE;EAC5B;EAEA,IAAIC,MAAM,GAAGP,WAAW,CAACQ,SAAS;EAElCD,MAAM,CAACE,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC9B,IAAIC,KAAK,GAAG,IAAI;IAEhB,IAAI,CAACC,gBAAgB,GAAGf,YAAY,CAACgB,SAAS,CAAC,YAAY;MACzD,IAAIhB,YAAY,CAACiB,SAAS,CAAC,CAAC,IAAIhB,aAAa,CAACiB,QAAQ,CAAC,CAAC,EAAE;QACxDJ,KAAK,CAACP,aAAa,CAACY,OAAO,CAAC,CAAC;QAE7BL,KAAK,CAACR,UAAU,CAACa,OAAO,CAAC,CAAC;MAC5B;IACF,CAAC,CAAC;IACF,IAAI,CAACC,iBAAiB,GAAGnB,aAAa,CAACe,SAAS,CAAC,YAAY;MAC3D,IAAIhB,YAAY,CAACiB,SAAS,CAAC,CAAC,IAAIhB,aAAa,CAACiB,QAAQ,CAAC,CAAC,EAAE;QACxDJ,KAAK,CAACP,aAAa,CAACc,QAAQ,CAAC,CAAC;QAE9BP,KAAK,CAACR,UAAU,CAACe,QAAQ,CAAC,CAAC;MAC7B;IACF,CAAC,CAAC;EACJ,CAAC;EAEDV,MAAM,CAACW,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAClC,IAAIC,qBAAqB,EAAEC,qBAAqB;IAEhD,CAACD,qBAAqB,GAAG,IAAI,CAACR,gBAAgB,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGQ,qBAAqB,CAACE,IAAI,CAAC,IAAI,CAAC;IACnG,CAACD,qBAAqB,GAAG,IAAI,CAACJ,iBAAiB,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,qBAAqB,CAACC,IAAI,CAAC,IAAI,CAAC;EACtG,CAAC;EAEDd,MAAM,CAACe,UAAU,GAAG,SAASA,UAAUA,CAACC,IAAI,EAAEC,IAAI,EAAE;IAClD,IAAIC,gBAAgB,GAAGnC,eAAe,CAACiC,IAAI,EAAEC,IAAI,CAAC;MAC9CE,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAEjCC,OAAO,CAACC,QAAQ,GAAG,IAAI;IACvB,OAAO,IAAI,CAACzB,UAAU,CAAC0B,OAAO,CAACF,OAAO,CAAC,CAACG,MAAM;EAChD,CAAC;EAEDtB,MAAM,CAACuB,UAAU,GAAG,SAASA,UAAUA,CAACJ,OAAO,EAAE;IAC/C,OAAO,IAAI,CAACvB,aAAa,CAACyB,OAAO,CAACzC,QAAQ,CAAC,CAAC,CAAC,EAAEuC,OAAO,EAAE;MACtDC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC,CAACE,MAAM;EACZ,CAAC;EAEDtB,MAAM,CAACwB,YAAY,GAAG,SAASA,YAAYA,CAACC,QAAQ,EAAEN,OAAO,EAAE;IAC7D,IAAIO,qBAAqB;IAEzB,OAAO,CAACA,qBAAqB,GAAG,IAAI,CAAC/B,UAAU,CAACgC,IAAI,CAACF,QAAQ,EAAEN,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGO,qBAAqB,CAACE,KAAK,CAACC,IAAI;EAC9H,CAAC;EAED7B,MAAM,CAAC8B,cAAc,GAAG,SAASA,cAAcA,CAACC,iBAAiB,EAAE;IACjE,OAAO,IAAI,CAACC,aAAa,CAAC,CAAC,CAACX,OAAO,CAACU,iBAAiB,CAAC,CAACE,GAAG,CAAC,UAAUC,IAAI,EAAE;MACzE,IAAIT,QAAQ,GAAGS,IAAI,CAACT,QAAQ;QACxBG,KAAK,GAAGM,IAAI,CAACN,KAAK;MACtB,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;MACrB,OAAO,CAACJ,QAAQ,EAAEI,IAAI,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC;EAED7B,MAAM,CAACmC,YAAY,GAAG,SAASA,YAAYA,CAACV,QAAQ,EAAEW,OAAO,EAAEC,OAAO,EAAE;IACtE,IAAIC,aAAa,GAAGtD,cAAc,CAACyC,QAAQ,CAAC;IAC5C,IAAIc,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAACF,aAAa,CAAC;IAC9D,OAAO,IAAI,CAAC3C,UAAU,CAAC8C,KAAK,CAAC,IAAI,EAAEF,gBAAgB,CAAC,CAACG,OAAO,CAACN,OAAO,EAAEC,OAAO,CAAC;EAChF,CAAC;EAEDrC,MAAM,CAAC2C,cAAc,GAAG,SAASA,cAAcA,CAACZ,iBAAiB,EAAEK,OAAO,EAAEC,OAAO,EAAE;IACnF,IAAIO,MAAM,GAAG,IAAI;IAEjB,OAAOrD,aAAa,CAACsD,KAAK,CAAC,YAAY;MACrC,OAAOD,MAAM,CAACZ,aAAa,CAAC,CAAC,CAACX,OAAO,CAACU,iBAAiB,CAAC,CAACE,GAAG,CAAC,UAAUa,KAAK,EAAE;QAC5E,IAAIrB,QAAQ,GAAGqB,KAAK,CAACrB,QAAQ;QAC7B,OAAO,CAACA,QAAQ,EAAEmB,MAAM,CAACT,YAAY,CAACV,QAAQ,EAAEW,OAAO,EAAEC,OAAO,CAAC,CAAC;MACpE,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAEDrC,MAAM,CAAC+C,aAAa,GAAG,SAASA,aAAaA,CAACtB,QAAQ,EAAEN,OAAO,EAAE;IAC/D,IAAI6B,sBAAsB;IAE1B,OAAO,CAACA,sBAAsB,GAAG,IAAI,CAACrD,UAAU,CAACgC,IAAI,CAACF,QAAQ,EAAEN,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG6B,sBAAsB,CAACpB,KAAK;EAC3H,CAAC;EAED5B,MAAM,CAACiD,aAAa,GAAG,SAASA,aAAaA,CAACjC,IAAI,EAAEC,IAAI,EAAE;IACxD,IAAIiC,iBAAiB,GAAGnE,eAAe,CAACiC,IAAI,EAAEC,IAAI,CAAC;MAC/CE,OAAO,GAAG+B,iBAAiB,CAAC,CAAC,CAAC;IAElC,IAAIvD,UAAU,GAAG,IAAI,CAACA,UAAU;IAChCJ,aAAa,CAACsD,KAAK,CAAC,YAAY;MAC9BlD,UAAU,CAAC0B,OAAO,CAACF,OAAO,CAAC,CAACgC,OAAO,CAAC,UAAUC,KAAK,EAAE;QACnDzD,UAAU,CAAC0D,MAAM,CAACD,KAAK,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAEDpD,MAAM,CAACsD,YAAY,GAAG,SAASA,YAAYA,CAACtC,IAAI,EAAEC,IAAI,EAAEsC,IAAI,EAAE;IAC5D,IAAIC,MAAM,GAAG,IAAI;IAEjB,IAAIC,iBAAiB,GAAG1E,eAAe,CAACiC,IAAI,EAAEC,IAAI,EAAEsC,IAAI,CAAC;MACrDpC,OAAO,GAAGsC,iBAAiB,CAAC,CAAC,CAAC;MAC9BpB,OAAO,GAAGoB,iBAAiB,CAAC,CAAC,CAAC;IAElC,IAAI9D,UAAU,GAAG,IAAI,CAACA,UAAU;IAEhC,IAAI+D,cAAc,GAAG9E,QAAQ,CAAC,CAAC,CAAC,EAAEuC,OAAO,EAAE;MACzCwC,MAAM,EAAE;IACV,CAAC,CAAC;IAEF,OAAOpE,aAAa,CAACsD,KAAK,CAAC,YAAY;MACrClD,UAAU,CAAC0B,OAAO,CAACF,OAAO,CAAC,CAACgC,OAAO,CAAC,UAAUC,KAAK,EAAE;QACnDA,KAAK,CAACQ,KAAK,CAAC,CAAC;MACf,CAAC,CAAC;MACF,OAAOJ,MAAM,CAACK,cAAc,CAACH,cAAc,EAAErB,OAAO,CAAC;IACvD,CAAC,CAAC;EACJ,CAAC;EAEDrC,MAAM,CAAC8D,aAAa,GAAG,SAASA,aAAaA,CAAC9C,IAAI,EAAEC,IAAI,EAAEsC,IAAI,EAAE;IAC9D,IAAIQ,MAAM,GAAG,IAAI;IAEjB,IAAIC,iBAAiB,GAAGjF,eAAe,CAACiC,IAAI,EAAEC,IAAI,EAAEsC,IAAI,CAAC;MACrDpC,OAAO,GAAG6C,iBAAiB,CAAC,CAAC,CAAC;MAC9BC,kBAAkB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;MACzCE,aAAa,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,kBAAkB;IAE3E,IAAI,OAAOC,aAAa,CAACC,MAAM,KAAK,WAAW,EAAE;MAC/CD,aAAa,CAACC,MAAM,GAAG,IAAI;IAC7B;IAEA,IAAIC,QAAQ,GAAG7E,aAAa,CAACsD,KAAK,CAAC,YAAY;MAC7C,OAAOkB,MAAM,CAACpE,UAAU,CAAC0B,OAAO,CAACF,OAAO,CAAC,CAACc,GAAG,CAAC,UAAUmB,KAAK,EAAE;QAC7D,OAAOA,KAAK,CAACiB,MAAM,CAACH,aAAa,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOI,OAAO,CAACC,GAAG,CAACH,QAAQ,CAAC,CAACI,IAAI,CAAC1F,IAAI,CAAC,CAAC2F,KAAK,CAAC3F,IAAI,CAAC;EACrD,CAAC;EAEDkB,MAAM,CAAC0E,iBAAiB,GAAG,SAASA,iBAAiBA,CAAC1D,IAAI,EAAEC,IAAI,EAAEsC,IAAI,EAAE;IACtE,IAAIoB,KAAK;MACLC,qBAAqB;MACrBC,qBAAqB;MACrBC,MAAM,GAAG,IAAI;IAEjB,IAAIC,iBAAiB,GAAGhG,eAAe,CAACiC,IAAI,EAAEC,IAAI,EAAEsC,IAAI,CAAC;MACrDpC,OAAO,GAAG4D,iBAAiB,CAAC,CAAC,CAAC;MAC9B1C,OAAO,GAAG0C,iBAAiB,CAAC,CAAC,CAAC;IAElC,IAAIrB,cAAc,GAAG9E,QAAQ,CAAC,CAAC,CAAC,EAAEuC,OAAO,EAAE;MACzC;MACA;MACAwC,MAAM,EAAE,CAACgB,KAAK,GAAG,CAACC,qBAAqB,GAAGzD,OAAO,CAAC6D,aAAa,KAAK,IAAI,GAAGJ,qBAAqB,GAAGzD,OAAO,CAACwC,MAAM,KAAK,IAAI,GAAGgB,KAAK,GAAG,IAAI;MACzIM,QAAQ,EAAE,CAACJ,qBAAqB,GAAG1D,OAAO,CAAC+D,eAAe,KAAK,IAAI,GAAGL,qBAAqB,GAAG;IAChG,CAAC,CAAC;IAEF,OAAOtF,aAAa,CAACsD,KAAK,CAAC,YAAY;MACrCiC,MAAM,CAACnF,UAAU,CAAC0B,OAAO,CAACF,OAAO,CAAC,CAACgC,OAAO,CAAC,UAAUC,KAAK,EAAE;QAC1DA,KAAK,CAAC+B,UAAU,CAAC,CAAC;MACpB,CAAC,CAAC;MAEF,OAAOL,MAAM,CAACjB,cAAc,CAACH,cAAc,EAAErB,OAAO,CAAC;IACvD,CAAC,CAAC;EACJ,CAAC;EAEDrC,MAAM,CAAC6D,cAAc,GAAG,SAASA,cAAcA,CAAC7C,IAAI,EAAEC,IAAI,EAAEsC,IAAI,EAAE;IAChE,IAAI6B,MAAM,GAAG,IAAI;IAEjB,IAAIC,iBAAiB,GAAGtG,eAAe,CAACiC,IAAI,EAAEC,IAAI,EAAEsC,IAAI,CAAC;MACrDpC,OAAO,GAAGkE,iBAAiB,CAAC,CAAC,CAAC;MAC9BhD,OAAO,GAAGgD,iBAAiB,CAAC,CAAC,CAAC;IAElC,IAAIjB,QAAQ,GAAG7E,aAAa,CAACsD,KAAK,CAAC,YAAY;MAC7C,OAAOuC,MAAM,CAACzF,UAAU,CAAC0B,OAAO,CAACF,OAAO,CAAC,CAACc,GAAG,CAAC,UAAUmB,KAAK,EAAE;QAC7D,OAAOA,KAAK,CAACkC,KAAK,CAACC,SAAS,EAAE3G,QAAQ,CAAC,CAAC,CAAC,EAAEyD,OAAO,EAAE;UAClDmD,IAAI,EAAE;YACJC,WAAW,EAAEtE,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACsE;UAClD;QACF,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIC,OAAO,GAAGpB,OAAO,CAACC,GAAG,CAACH,QAAQ,CAAC,CAACI,IAAI,CAAC1F,IAAI,CAAC;IAE9C,IAAI,EAAEuD,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACsD,YAAY,CAAC,EAAE;MACtDD,OAAO,GAAGA,OAAO,CAACjB,KAAK,CAAC3F,IAAI,CAAC;IAC/B;IAEA,OAAO4G,OAAO;EAChB,CAAC;EAED1F,MAAM,CAAC4F,UAAU,GAAG,SAASA,UAAUA,CAAC5E,IAAI,EAAEC,IAAI,EAAEsC,IAAI,EAAE;IACxD,IAAIjB,aAAa,GAAGtD,cAAc,CAACgC,IAAI,EAAEC,IAAI,EAAEsC,IAAI,CAAC;IACpD,IAAIhB,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAACF,aAAa,CAAC,CAAC,CAAC;;IAEhE,IAAI,OAAOC,gBAAgB,CAACsD,KAAK,KAAK,WAAW,EAAE;MACjDtD,gBAAgB,CAACsD,KAAK,GAAG,KAAK;IAChC;IAEA,IAAIzC,KAAK,GAAG,IAAI,CAACzD,UAAU,CAAC8C,KAAK,CAAC,IAAI,EAAEF,gBAAgB,CAAC;IACzD,OAAOa,KAAK,CAAC0C,aAAa,CAACvD,gBAAgB,CAACwD,SAAS,CAAC,GAAG3C,KAAK,CAACkC,KAAK,CAAC/C,gBAAgB,CAAC,GAAG+B,OAAO,CAAC0B,OAAO,CAAC5C,KAAK,CAACxB,KAAK,CAACC,IAAI,CAAC;EAC5H,CAAC;EAED7B,MAAM,CAACiG,aAAa,GAAG,SAASA,aAAaA,CAACjF,IAAI,EAAEC,IAAI,EAAEsC,IAAI,EAAE;IAC9D,OAAO,IAAI,CAACqC,UAAU,CAAC5E,IAAI,EAAEC,IAAI,EAAEsC,IAAI,CAAC,CAACiB,IAAI,CAAC1F,IAAI,CAAC,CAAC2F,KAAK,CAAC3F,IAAI,CAAC;EACjE,CAAC;EAEDkB,MAAM,CAACkG,kBAAkB,GAAG,SAASA,kBAAkBA,CAAClF,IAAI,EAAEC,IAAI,EAAEsC,IAAI,EAAE;IACxE,IAAIjB,aAAa,GAAGtD,cAAc,CAACgC,IAAI,EAAEC,IAAI,EAAEsC,IAAI,CAAC;IACpDjB,aAAa,CAAC6D,QAAQ,GAAG3G,qBAAqB,CAAC,CAAC;IAChD,OAAO,IAAI,CAACoG,UAAU,CAACtD,aAAa,CAAC;EACvC,CAAC;EAEDtC,MAAM,CAACoG,qBAAqB,GAAG,SAASA,qBAAqBA,CAACpF,IAAI,EAAEC,IAAI,EAAEsC,IAAI,EAAE;IAC9E,OAAO,IAAI,CAAC2C,kBAAkB,CAAClF,IAAI,EAAEC,IAAI,EAAEsC,IAAI,CAAC,CAACiB,IAAI,CAAC1F,IAAI,CAAC,CAAC2F,KAAK,CAAC3F,IAAI,CAAC;EACzE,CAAC;EAEDkB,MAAM,CAACqG,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAClD,IAAIC,MAAM,GAAG,IAAI;IAEjB,IAAIlC,QAAQ,GAAG7E,aAAa,CAACsD,KAAK,CAAC,YAAY;MAC7C,OAAOyD,MAAM,CAAC1G,aAAa,CAAC2G,MAAM,CAAC,CAAC,CAACtE,GAAG,CAAC,UAAUuE,QAAQ,EAAE;QAC3D,OAAOA,QAAQ,CAACnC,MAAM,CAAC,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOC,OAAO,CAACC,GAAG,CAACH,QAAQ,CAAC,CAACI,IAAI,CAAC1F,IAAI,CAAC,CAAC2F,KAAK,CAAC3F,IAAI,CAAC;EACrD,CAAC;EAEDkB,MAAM,CAACyG,qBAAqB,GAAG,SAASA,qBAAqBA,CAAA,EAAG;IAC9D,OAAO,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAACD,qBAAqB,CAAC,CAAC;EACxD,CAAC;EAEDzG,MAAM,CAAC2G,eAAe,GAAG,SAASA,eAAeA,CAACtE,OAAO,EAAE;IACzD,OAAO,IAAI,CAACzC,aAAa,CAAC6C,KAAK,CAAC,IAAI,EAAEJ,OAAO,CAAC,CAACuE,OAAO,CAAC,CAAC;EAC1D,CAAC;EAED5G,MAAM,CAACgC,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC9C,OAAO,IAAI,CAACrC,UAAU;EACxB,CAAC;EAEDK,MAAM,CAAC0G,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACpD,OAAO,IAAI,CAAC9G,aAAa;EAC3B,CAAC;EAEDI,MAAM,CAAC6G,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACtD,OAAO,IAAI,CAAChH,cAAc;EAC5B,CAAC;EAEDG,MAAM,CAAC8G,iBAAiB,GAAG,SAASA,iBAAiBA,CAACzE,OAAO,EAAE;IAC7D,IAAI,CAACxC,cAAc,GAAGwC,OAAO;EAC/B,CAAC;EAEDrC,MAAM,CAAC+G,gBAAgB,GAAG,SAASA,gBAAgBA,CAACtF,QAAQ,EAAEY,OAAO,EAAE;IACrE,IAAI2E,MAAM,GAAG,IAAI,CAAClH,aAAa,CAAC6B,IAAI,CAAC,UAAUsF,CAAC,EAAE;MAChD,OAAOpI,YAAY,CAAC4C,QAAQ,CAAC,KAAK5C,YAAY,CAACoI,CAAC,CAACxF,QAAQ,CAAC;IAC5D,CAAC,CAAC;IAEF,IAAIuF,MAAM,EAAE;MACVA,MAAM,CAACnH,cAAc,GAAGwC,OAAO;IACjC,CAAC,MAAM;MACL,IAAI,CAACvC,aAAa,CAACoH,IAAI,CAAC;QACtBzF,QAAQ,EAAEA,QAAQ;QAClB5B,cAAc,EAAEwC;MAClB,CAAC,CAAC;IACJ;EACF,CAAC;EAEDrC,MAAM,CAACmH,gBAAgB,GAAG,SAASA,gBAAgBA,CAAC1F,QAAQ,EAAE;IAC5D,IAAI2F,qBAAqB;IAEzB,OAAO3F,QAAQ,GAAG,CAAC2F,qBAAqB,GAAG,IAAI,CAACtH,aAAa,CAAC6B,IAAI,CAAC,UAAUsF,CAAC,EAAE;MAC9E,OAAOhI,eAAe,CAACwC,QAAQ,EAAEwF,CAAC,CAACxF,QAAQ,CAAC;IAC9C,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2F,qBAAqB,CAACvH,cAAc,GAAG0F,SAAS;EACzE,CAAC;EAEDvF,MAAM,CAACqH,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,WAAW,EAAEjF,OAAO,EAAE;IAC9E,IAAI2E,MAAM,GAAG,IAAI,CAACjH,gBAAgB,CAAC4B,IAAI,CAAC,UAAUsF,CAAC,EAAE;MACnD,OAAOpI,YAAY,CAACyI,WAAW,CAAC,KAAKzI,YAAY,CAACoI,CAAC,CAACK,WAAW,CAAC;IAClE,CAAC,CAAC;IAEF,IAAIN,MAAM,EAAE;MACVA,MAAM,CAACnH,cAAc,GAAGwC,OAAO;IACjC,CAAC,MAAM;MACL,IAAI,CAACtC,gBAAgB,CAACmH,IAAI,CAAC;QACzBI,WAAW,EAAEA,WAAW;QACxBzH,cAAc,EAAEwC;MAClB,CAAC,CAAC;IACJ;EACF,CAAC;EAEDrC,MAAM,CAACuH,mBAAmB,GAAG,SAASA,mBAAmBA,CAACD,WAAW,EAAE;IACrE,IAAIE,qBAAqB;IAEzB,OAAOF,WAAW,GAAG,CAACE,qBAAqB,GAAG,IAAI,CAACzH,gBAAgB,CAAC4B,IAAI,CAAC,UAAUsF,CAAC,EAAE;MACpF,OAAOhI,eAAe,CAACqI,WAAW,EAAEL,CAAC,CAACK,WAAW,CAAC;IACpD,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,qBAAqB,CAAC3H,cAAc,GAAG0F,SAAS;EACzE,CAAC;EAEDvF,MAAM,CAACwC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACH,OAAO,EAAE;IACjE,IAAIA,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACoF,UAAU,EAAE;MACjD,OAAOpF,OAAO;IAChB;IAEA,IAAIE,gBAAgB,GAAG3D,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACiB,cAAc,CAAC6H,OAAO,EAAE,IAAI,CAACP,gBAAgB,CAAC9E,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACZ,QAAQ,CAAC,EAAEY,OAAO,EAAE;MAC5IoF,UAAU,EAAE;IACd,CAAC,CAAC;IAEF,IAAI,CAAClF,gBAAgB,CAACoF,SAAS,IAAIpF,gBAAgB,CAACd,QAAQ,EAAE;MAC5Dc,gBAAgB,CAACoF,SAAS,GAAGzI,qBAAqB,CAACqD,gBAAgB,CAACd,QAAQ,EAAEc,gBAAgB,CAAC;IACjG;IAEA,OAAOA,gBAAgB;EACzB,CAAC;EAEDvC,MAAM,CAAC4H,2BAA2B,GAAG,SAASA,2BAA2BA,CAACvF,OAAO,EAAE;IACjF,OAAO,IAAI,CAACG,mBAAmB,CAACH,OAAO,CAAC;EAC1C,CAAC;EAEDrC,MAAM,CAAC6H,sBAAsB,GAAG,SAASA,sBAAsBA,CAACxF,OAAO,EAAE;IACvE,IAAIA,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACoF,UAAU,EAAE;MACjD,OAAOpF,OAAO;IAChB;IAEA,OAAOzD,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACiB,cAAc,CAACiI,SAAS,EAAE,IAAI,CAACP,mBAAmB,CAAClF,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACiF,WAAW,CAAC,EAAEjF,OAAO,EAAE;MACpIoF,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC;EAEDzH,MAAM,CAAC+H,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC9B,IAAI,CAACpI,UAAU,CAACoI,KAAK,CAAC,CAAC;IACvB,IAAI,CAACnI,aAAa,CAACmI,KAAK,CAAC,CAAC;EAC5B,CAAC;EAED,OAAOtI,WAAW;AACpB,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}