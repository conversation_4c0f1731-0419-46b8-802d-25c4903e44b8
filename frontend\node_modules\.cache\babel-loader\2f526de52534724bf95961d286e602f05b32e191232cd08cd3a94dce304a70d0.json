{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aidesk\\\\frontend\\\\src\\\\index.tsx\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { Toaster } from 'react-hot-toast';\n\n// React Query 클라이언트 설정\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000 // 5분\n    }\n  }\n});\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n    client: queryClient,\n    children: [/*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n      position: \"top-right\",\n      toastOptions: {\n        duration: 4000,\n        style: {\n          background: '#363636',\n          color: '#fff'\n        },\n        success: {\n          duration: 3000,\n          iconTheme: {\n            primary: '#22c55e',\n            secondary: '#fff'\n          }\n        },\n        error: {\n          duration: 5000,\n          iconTheme: {\n            primary: '#ef4444',\n            secondary: '#fff'\n          }\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 24,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "QueryClient", "QueryClientProvider", "Toaster", "jsxDEV", "_jsxDEV", "queryClient", "defaultOptions", "queries", "retry", "refetchOnWindowFocus", "staleTime", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "client", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "toastOptions", "duration", "style", "background", "color", "success", "iconTheme", "primary", "secondary", "error"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/src/index.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { Toaster } from 'react-hot-toast';\n\n// React Query 클라이언트 설정\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000, // 5분\n    },\n  },\n});\n\nconst root = ReactDOM.createRoot(\n  document.getElementById('root') as HTMLElement\n);\n\nroot.render(\n  <React.StrictMode>\n    <QueryClientProvider client={queryClient}>\n      <App />\n      <Toaster\n        position=\"top-right\"\n        toastOptions={{\n          duration: 4000,\n          style: {\n            background: '#363636',\n            color: '#fff',\n          },\n          success: {\n            duration: 3000,\n            iconTheme: {\n              primary: '#22c55e',\n              secondary: '#fff',\n            },\n          },\n          error: {\n            duration: 5000,\n            iconTheme: {\n              primary: '#ef4444',\n              secondary: '#fff',\n            },\n          },\n        }}\n      />\n    </QueryClientProvider>\n  </React.StrictMode>\n);\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;AACvB,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,aAAa;AAC9D,SAASC,OAAO,QAAQ,iBAAiB;;AAEzC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,IAAIL,WAAW,CAAC;EAClCM,cAAc,EAAE;IACdC,OAAO,EAAE;MACPC,KAAK,EAAE,CAAC;MACRC,oBAAoB,EAAE,KAAK;MAC3BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAE;IAC5B;EACF;AACF,CAAC,CAAC;AAEF,MAAMC,IAAI,GAAGb,QAAQ,CAACc,UAAU,CAC9BC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC;AAEDH,IAAI,CAACI,MAAM,cACTX,OAAA,CAACP,KAAK,CAACmB,UAAU;EAAAC,QAAA,eACfb,OAAA,CAACH,mBAAmB;IAACiB,MAAM,EAAEb,WAAY;IAAAY,QAAA,gBACvCb,OAAA,CAACL,GAAG;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACPlB,OAAA,CAACF,OAAO;MACNqB,QAAQ,EAAC,WAAW;MACpBC,YAAY,EAAE;QACZC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE;UACLC,UAAU,EAAE,SAAS;UACrBC,KAAK,EAAE;QACT,CAAC;QACDC,OAAO,EAAE;UACPJ,QAAQ,EAAE,IAAI;UACdK,SAAS,EAAE;YACTC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE;UACb;QACF,CAAC;QACDC,KAAK,EAAE;UACLR,QAAQ,EAAE,IAAI;UACdK,SAAS,EAAE;YACTC,OAAO,EAAE,SAAS;YAClBC,SAAS,EAAE;UACb;QACF;MACF;IAAE;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACiB;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACN,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}