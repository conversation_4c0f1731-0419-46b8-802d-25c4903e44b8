{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aidesk\\\\frontend\\\\src\\\\pages\\\\VulnerabilityAnalysis.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { useMutation } from '@tanstack/react-query';\nimport { useNavigate } from 'react-router-dom';\nimport toast from 'react-hot-toast';\nimport { BeakerIcon, DocumentTextIcon, InformationCircleIcon } from '@heroicons/react/24/outline';\nimport { analysisApi } from '../services/api.ts';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VulnerabilityAnalysis = () => {\n  _s();\n  const navigate = useNavigate();\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    },\n    watch,\n    reset\n  } = useForm({\n    defaultValues: {\n      include_reasoning: true,\n      include_recommendations: true,\n      severity: 'medium',\n      tags: []\n    }\n  });\n\n  // AI 분석 뮤테이션\n  const analysisMutation = useMutation(analysisApi.analyzeVulnerability, {\n    onSuccess: response => {\n      const {\n        data\n      } = response;\n      if (data.success) {\n        toast.success('분석이 시작되었습니다!');\n        if (data.analysis_id) {\n          navigate(`/results/${data.analysis_id}`);\n        } else {\n          navigate('/results');\n        }\n      } else {\n        toast.error(data.message || '분석 요청에 실패했습니다.');\n      }\n      setIsAnalyzing(false);\n    },\n    onError: error => {\n      var _error$response, _error$response$data;\n      console.error('Analysis error:', error);\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || '분석 중 오류가 발생했습니다.');\n      setIsAnalyzing(false);\n    }\n  });\n  const onSubmit = async data => {\n    setIsAnalyzing(true);\n    const analysisRequest = {\n      report: {\n        title: data.title,\n        description: data.description,\n        severity: data.severity,\n        affected_system: data.affected_system,\n        vulnerability_type: data.vulnerability_type,\n        attack_vector: data.attack_vector,\n        impact: data.impact,\n        steps_to_reproduce: data.steps_to_reproduce,\n        proof_of_concept: data.proof_of_concept,\n        reporter: data.reporter,\n        tags: data.tags || []\n      },\n      ai_model: data.ai_model,\n      include_reasoning: data.include_reasoning,\n      include_recommendations: data.include_recommendations\n    };\n    analysisMutation.mutate(analysisRequest);\n  };\n  const severityOptions = [{\n    value: 'critical',\n    label: 'Critical',\n    color: 'text-red-600'\n  }, {\n    value: 'high',\n    label: 'High',\n    color: 'text-orange-600'\n  }, {\n    value: 'medium',\n    label: 'Medium',\n    color: 'text-yellow-600'\n  }, {\n    value: 'low',\n    label: 'Low',\n    color: 'text-blue-600'\n  }, {\n    value: 'info',\n    label: 'Info',\n    color: 'text-gray-600'\n  }];\n  const aiModelOptions = [{\n    value: '',\n    label: '자동 선택 (권장)'\n  }, {\n    value: 'gpt-4',\n    label: 'GPT-4'\n  }, {\n    value: 'gpt-3.5-turbo',\n    label: 'GPT-3.5 Turbo'\n  }, {\n    value: 'ollama',\n    label: 'Ollama (로컬)'\n  }, {\n    value: 'custom',\n    label: '커스텀 모델'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(BeakerIcon, {\n          className: \"h-8 w-8 text-primary-600 mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"\\uCDE8\\uC57D\\uC810 \\uBD84\\uC11D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-sm text-gray-500\",\n            children: \"AI\\uB97C \\uD65C\\uC6A9\\uD558\\uC5EC \\uCDE8\\uC57D\\uC810\\uC744 \\uC790\\uB3D9\\uC73C\\uB85C \\uBD84\\uC11D\\uD558\\uACE0 \\uBD84\\uB958\\uD569\\uB2C8\\uB2E4.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit(onSubmit),\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-medium text-gray-900 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(DocumentTextIcon, {\n              className: \"h-5 w-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), \"\\uCDE8\\uC57D\\uC810 \\uC815\\uBCF4\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: [\"\\uC81C\\uBAA9 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-red-500\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 20\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-input\",\n              placeholder: \"\\uCDE8\\uC57D\\uC810 \\uC81C\\uBAA9\\uC744 \\uC785\\uB825\\uD558\\uC138\\uC694\",\n              ...register('title', {\n                required: '제목은 필수입니다',\n                minLength: {\n                  value: 5,\n                  message: '제목은 최소 5자 이상이어야 합니다'\n                }\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), errors.title && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-error\",\n              children: errors.title.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: [\"\\uC0C1\\uC138 \\uC124\\uBA85 \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-red-500\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              rows: 6,\n              className: \"form-textarea\",\n              placeholder: \"\\uCDE8\\uC57D\\uC810\\uC5D0 \\uB300\\uD55C \\uC0C1\\uC138\\uD55C \\uC124\\uBA85\\uC744 \\uC785\\uB825\\uD558\\uC138\\uC694...\",\n              ...register('description', {\n                required: '설명은 필수입니다',\n                minLength: {\n                  value: 20,\n                  message: '설명은 최소 20자 이상이어야 합니다'\n                }\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), errors.description && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-error\",\n              children: errors.description.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"\\uC2EC\\uAC01\\uB3C4 (\\uC608\\uC0C1)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-select\",\n                ...register('severity'),\n                children: severityOptions.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: option.value,\n                  children: option.label\n                }, option.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"\\uC601\\uD5A5\\uBC1B\\uB294 \\uC2DC\\uC2A4\\uD15C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"form-input\",\n                placeholder: \"\\uC608: \\uC6F9 \\uC560\\uD50C\\uB9AC\\uCF00\\uC774\\uC158, API \\uC11C\\uBC84\",\n                ...register('affected_system')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"\\uCDE8\\uC57D\\uC810 \\uC720\\uD615\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"form-input\",\n                placeholder: \"\\uC608: XSS, SQL Injection, CSRF\",\n                ...register('vulnerability_type')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"\\uACF5\\uACA9 \\uBCA1\\uD130\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"form-input\",\n                placeholder: \"\\uC608: Network, Local, Physical\",\n                ...register('attack_vector')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\uC601\\uD5A5\\uB3C4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              rows: 3,\n              className: \"form-textarea\",\n              placeholder: \"\\uC774 \\uCDE8\\uC57D\\uC810\\uC774 \\uC2DC\\uC2A4\\uD15C\\uC5D0 \\uBBF8\\uCE58\\uB294 \\uC601\\uD5A5\\uC744 \\uC124\\uBA85\\uD558\\uC138\\uC694...\",\n              ...register('impact')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\uC7AC\\uD604 \\uB2E8\\uACC4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              rows: 4,\n              className: \"form-textarea\",\n              placeholder: \"\\uCDE8\\uC57D\\uC810\\uC744 \\uC7AC\\uD604\\uD558\\uB294 \\uB2E8\\uACC4\\uBCC4 \\uBC29\\uBC95\\uC744 \\uC124\\uBA85\\uD558\\uC138\\uC694...\",\n              ...register('steps_to_reproduce')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\uAC1C\\uB150 \\uC99D\\uBA85 (PoC)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              rows: 4,\n              className: \"form-textarea\",\n              placeholder: \"\\uD398\\uC774\\uB85C\\uB4DC, \\uC2A4\\uD06C\\uB9BD\\uD2B8, \\uB610\\uB294 \\uC99D\\uBA85 \\uCF54\\uB4DC\\uB97C \\uC785\\uB825\\uD558\\uC138\\uC694...\",\n              ...register('proof_of_concept')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\uBCF4\\uACE0\\uC790\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-input\",\n              placeholder: \"\\uBCF4\\uACE0\\uC790 \\uC774\\uB984 \\uB610\\uB294 ID\",\n              ...register('reporter')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-medium text-gray-900 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(BeakerIcon, {\n              className: \"h-5 w-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this), \"AI \\uBD84\\uC11D \\uC124\\uC815\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"AI \\uBAA8\\uB378\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-select\",\n              ...register('ai_model'),\n              children: aiModelOptions.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: option.value,\n                children: option.label\n              }, option.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-500\",\n              children: \"\\uC790\\uB3D9 \\uC120\\uD0DD \\uC2DC \\uAC00\\uC7A5 \\uC801\\uD569\\uD55C \\uBAA8\\uB378\\uC774 \\uC0AC\\uC6A9\\uB429\\uB2C8\\uB2E4.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\",\n                ...register('include_reasoning')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"ml-2 block text-sm text-gray-900\",\n                children: \"\\uBD84\\uC11D \\uADFC\\uAC70 \\uD3EC\\uD568\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\",\n                ...register('include_recommendations')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"ml-2 block text-sm text-gray-900\",\n                children: \"\\uBCF4\\uC548 \\uAD8C\\uC7A5\\uC0AC\\uD56D \\uD3EC\\uD568\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-end space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"btn-outline\",\n          onClick: () => reset(),\n          disabled: isAnalyzing,\n          children: \"\\uCD08\\uAE30\\uD654\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn-primary\",\n          disabled: isAnalyzing,\n          children: isAnalyzing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-spinner mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 17\n            }, this), \"\\uBD84\\uC11D \\uC911...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(BeakerIcon, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this), \"AI \\uBD84\\uC11D \\uC2DC\\uC791\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-8 card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex\",\n          children: [/*#__PURE__*/_jsxDEV(InformationCircleIcon, {\n            className: \"h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-gray-900\",\n              children: \"\\uBD84\\uC11D \\uD301\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2 text-sm text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"list-disc list-inside space-y-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\uC0C1\\uC138\\uD55C \\uC124\\uBA85\\uACFC \\uC7AC\\uD604 \\uB2E8\\uACC4\\uB97C \\uC81C\\uACF5\\uD560\\uC218\\uB85D \\uB354 \\uC815\\uD655\\uD55C \\uBD84\\uC11D \\uACB0\\uACFC\\uB97C \\uC5BB\\uC744 \\uC218 \\uC788\\uC2B5\\uB2C8\\uB2E4.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\uAC1C\\uB150 \\uC99D\\uBA85(PoC) \\uCF54\\uB4DC\\uB098 \\uD398\\uC774\\uB85C\\uB4DC\\uB97C \\uD3EC\\uD568\\uD558\\uBA74 \\uBD84\\uB958 \\uC815\\uD655\\uB3C4\\uAC00 \\uD5A5\\uC0C1\\uB429\\uB2C8\\uB2E4.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\uBD84\\uC11D\\uC740 \\uBCF4\\uD1B5 1-3\\uBD84 \\uC815\\uB3C4 \\uC18C\\uC694\\uB418\\uBA70, \\uBCF5\\uC7A1\\uD55C \\uCDE8\\uC57D\\uC810\\uC758 \\uACBD\\uC6B0 \\uB354 \\uC624\\uB798 \\uAC78\\uB9B4 \\uC218 \\uC788\\uC2B5\\uB2C8\\uB2E4.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n};\n_s(VulnerabilityAnalysis, \"m711l2hFMw7gixET8orAHYHIKpI=\", false, function () {\n  return [useNavigate, useForm, useMutation];\n});\n_c = VulnerabilityAnalysis;\nexport default VulnerabilityAnalysis;\nvar _c;\n$RefreshReg$(_c, \"VulnerabilityAnalysis\");", "map": {"version": 3, "names": ["React", "useState", "useForm", "useMutation", "useNavigate", "toast", "BeakerIcon", "DocumentTextIcon", "InformationCircleIcon", "analysisApi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VulnerabilityAnalysis", "_s", "navigate", "isAnalyzing", "setIsAnalyzing", "register", "handleSubmit", "formState", "errors", "watch", "reset", "defaultValues", "include_reasoning", "include_recommendations", "severity", "tags", "analysisMutation", "analyzeVulnerability", "onSuccess", "response", "data", "success", "analysis_id", "error", "message", "onError", "_error$response", "_error$response$data", "console", "detail", "onSubmit", "analysisRequest", "report", "title", "description", "affected_system", "vulnerability_type", "attack_vector", "impact", "steps_to_reproduce", "proof_of_concept", "reporter", "ai_model", "mutate", "severityOptions", "value", "label", "color", "aiModelOptions", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "required", "<PERSON><PERSON><PERSON><PERSON>", "rows", "map", "option", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/src/pages/VulnerabilityAnalysis.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { useMutation, useQuery } from '@tanstack/react-query';\nimport { useNavigate } from 'react-router-dom';\nimport toast from 'react-hot-toast';\nimport {\n  BeakerIcon,\n  DocumentTextIcon,\n  ExclamationTriangleIcon,\n  InformationCircleIcon,\n} from '@heroicons/react/24/outline';\nimport { analysisApi, VulnerabilityReport, AnalysisRequest } from '../services/api.ts';\n\ninterface FormData extends VulnerabilityReport {\n  ai_model?: string;\n  include_reasoning: boolean;\n  include_recommendations: boolean;\n}\n\nconst VulnerabilityAnalysis: React.FC = () => {\n  const navigate = useNavigate();\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  \n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    watch,\n    reset,\n  } = useForm<FormData>({\n    defaultValues: {\n      include_reasoning: true,\n      include_recommendations: true,\n      severity: 'medium',\n      tags: [],\n    },\n  });\n\n  // AI 분석 뮤테이션\n  const analysisMutation = useMutation(analysisApi.analyzeVulnerability, {\n    onSuccess: (response) => {\n      const { data } = response;\n      if (data.success) {\n        toast.success('분석이 시작되었습니다!');\n        if (data.analysis_id) {\n          navigate(`/results/${data.analysis_id}`);\n        } else {\n          navigate('/results');\n        }\n      } else {\n        toast.error(data.message || '분석 요청에 실패했습니다.');\n      }\n      setIsAnalyzing(false);\n    },\n    onError: (error: any) => {\n      console.error('Analysis error:', error);\n      toast.error(error.response?.data?.detail || '분석 중 오류가 발생했습니다.');\n      setIsAnalyzing(false);\n    },\n  });\n\n  const onSubmit = async (data: FormData) => {\n    setIsAnalyzing(true);\n    \n    const analysisRequest: AnalysisRequest = {\n      report: {\n        title: data.title,\n        description: data.description,\n        severity: data.severity,\n        affected_system: data.affected_system,\n        vulnerability_type: data.vulnerability_type,\n        attack_vector: data.attack_vector,\n        impact: data.impact,\n        steps_to_reproduce: data.steps_to_reproduce,\n        proof_of_concept: data.proof_of_concept,\n        reporter: data.reporter,\n        tags: data.tags || [],\n      },\n      ai_model: data.ai_model,\n      include_reasoning: data.include_reasoning,\n      include_recommendations: data.include_recommendations,\n    };\n\n    analysisMutation.mutate(analysisRequest);\n  };\n\n  const severityOptions = [\n    { value: 'critical', label: 'Critical', color: 'text-red-600' },\n    { value: 'high', label: 'High', color: 'text-orange-600' },\n    { value: 'medium', label: 'Medium', color: 'text-yellow-600' },\n    { value: 'low', label: 'Low', color: 'text-blue-600' },\n    { value: 'info', label: 'Info', color: 'text-gray-600' },\n  ];\n\n  const aiModelOptions = [\n    { value: '', label: '자동 선택 (권장)' },\n    { value: 'gpt-4', label: 'GPT-4' },\n    { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },\n    { value: 'ollama', label: 'Ollama (로컬)' },\n    { value: 'custom', label: '커스텀 모델' },\n  ];\n\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      {/* 헤더 */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center\">\n          <BeakerIcon className=\"h-8 w-8 text-primary-600 mr-3\" />\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">취약점 분석</h1>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              AI를 활용하여 취약점을 자동으로 분석하고 분류합니다.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* 분석 폼 */}\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h2 className=\"text-lg font-medium text-gray-900 flex items-center\">\n              <DocumentTextIcon className=\"h-5 w-5 mr-2\" />\n              취약점 정보\n            </h2>\n          </div>\n          <div className=\"card-body space-y-6\">\n            {/* 제목 */}\n            <div>\n              <label className=\"form-label\">\n                제목 <span className=\"text-red-500\">*</span>\n              </label>\n              <input\n                type=\"text\"\n                className=\"form-input\"\n                placeholder=\"취약점 제목을 입력하세요\"\n                {...register('title', { \n                  required: '제목은 필수입니다',\n                  minLength: { value: 5, message: '제목은 최소 5자 이상이어야 합니다' }\n                })}\n              />\n              {errors.title && (\n                <p className=\"form-error\">{errors.title.message}</p>\n              )}\n            </div>\n\n            {/* 설명 */}\n            <div>\n              <label className=\"form-label\">\n                상세 설명 <span className=\"text-red-500\">*</span>\n              </label>\n              <textarea\n                rows={6}\n                className=\"form-textarea\"\n                placeholder=\"취약점에 대한 상세한 설명을 입력하세요...\"\n                {...register('description', { \n                  required: '설명은 필수입니다',\n                  minLength: { value: 20, message: '설명은 최소 20자 이상이어야 합니다' }\n                })}\n              />\n              {errors.description && (\n                <p className=\"form-error\">{errors.description.message}</p>\n              )}\n            </div>\n\n            {/* 심각도 및 영향받는 시스템 */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"form-label\">심각도 (예상)</label>\n                <select className=\"form-select\" {...register('severity')}>\n                  {severityOptions.map((option) => (\n                    <option key={option.value} value={option.value}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n              <div>\n                <label className=\"form-label\">영향받는 시스템</label>\n                <input\n                  type=\"text\"\n                  className=\"form-input\"\n                  placeholder=\"예: 웹 애플리케이션, API 서버\"\n                  {...register('affected_system')}\n                />\n              </div>\n            </div>\n\n            {/* 취약점 유형 및 공격 벡터 */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"form-label\">취약점 유형</label>\n                <input\n                  type=\"text\"\n                  className=\"form-input\"\n                  placeholder=\"예: XSS, SQL Injection, CSRF\"\n                  {...register('vulnerability_type')}\n                />\n              </div>\n              <div>\n                <label className=\"form-label\">공격 벡터</label>\n                <input\n                  type=\"text\"\n                  className=\"form-input\"\n                  placeholder=\"예: Network, Local, Physical\"\n                  {...register('attack_vector')}\n                />\n              </div>\n            </div>\n\n            {/* 영향도 */}\n            <div>\n              <label className=\"form-label\">영향도</label>\n              <textarea\n                rows={3}\n                className=\"form-textarea\"\n                placeholder=\"이 취약점이 시스템에 미치는 영향을 설명하세요...\"\n                {...register('impact')}\n              />\n            </div>\n\n            {/* 재현 단계 */}\n            <div>\n              <label className=\"form-label\">재현 단계</label>\n              <textarea\n                rows={4}\n                className=\"form-textarea\"\n                placeholder=\"취약점을 재현하는 단계별 방법을 설명하세요...\"\n                {...register('steps_to_reproduce')}\n              />\n            </div>\n\n            {/* 개념 증명 */}\n            <div>\n              <label className=\"form-label\">개념 증명 (PoC)</label>\n              <textarea\n                rows={4}\n                className=\"form-textarea\"\n                placeholder=\"페이로드, 스크립트, 또는 증명 코드를 입력하세요...\"\n                {...register('proof_of_concept')}\n              />\n            </div>\n\n            {/* 보고자 */}\n            <div>\n              <label className=\"form-label\">보고자</label>\n              <input\n                type=\"text\"\n                className=\"form-input\"\n                placeholder=\"보고자 이름 또는 ID\"\n                {...register('reporter')}\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* AI 분석 설정 */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h2 className=\"text-lg font-medium text-gray-900 flex items-center\">\n              <BeakerIcon className=\"h-5 w-5 mr-2\" />\n              AI 분석 설정\n            </h2>\n          </div>\n          <div className=\"card-body space-y-6\">\n            {/* AI 모델 선택 */}\n            <div>\n              <label className=\"form-label\">AI 모델</label>\n              <select className=\"form-select\" {...register('ai_model')}>\n                {aiModelOptions.map((option) => (\n                  <option key={option.value} value={option.value}>\n                    {option.label}\n                  </option>\n                ))}\n              </select>\n              <p className=\"mt-1 text-sm text-gray-500\">\n                자동 선택 시 가장 적합한 모델이 사용됩니다.\n              </p>\n            </div>\n\n            {/* 분석 옵션 */}\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                  {...register('include_reasoning')}\n                />\n                <label className=\"ml-2 block text-sm text-gray-900\">\n                  분석 근거 포함\n                </label>\n              </div>\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                  {...register('include_recommendations')}\n                />\n                <label className=\"ml-2 block text-sm text-gray-900\">\n                  보안 권장사항 포함\n                </label>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 제출 버튼 */}\n        <div className=\"flex justify-end space-x-3\">\n          <button\n            type=\"button\"\n            className=\"btn-outline\"\n            onClick={() => reset()}\n            disabled={isAnalyzing}\n          >\n            초기화\n          </button>\n          <button\n            type=\"submit\"\n            className=\"btn-primary\"\n            disabled={isAnalyzing}\n          >\n            {isAnalyzing ? (\n              <>\n                <div className=\"loading-spinner mr-2\" />\n                분석 중...\n              </>\n            ) : (\n              <>\n                <BeakerIcon className=\"h-4 w-4 mr-2\" />\n                AI 분석 시작\n              </>\n            )}\n          </button>\n        </div>\n      </form>\n\n      {/* 도움말 */}\n      <div className=\"mt-8 card\">\n        <div className=\"card-body\">\n          <div className=\"flex\">\n            <InformationCircleIcon className=\"h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0\" />\n            <div>\n              <h3 className=\"text-sm font-medium text-gray-900\">분석 팁</h3>\n              <div className=\"mt-2 text-sm text-gray-500\">\n                <ul className=\"list-disc list-inside space-y-1\">\n                  <li>상세한 설명과 재현 단계를 제공할수록 더 정확한 분석 결과를 얻을 수 있습니다.</li>\n                  <li>개념 증명(PoC) 코드나 페이로드를 포함하면 분류 정확도가 향상됩니다.</li>\n                  <li>분석은 보통 1-3분 정도 소요되며, 복잡한 취약점의 경우 더 오래 걸릴 수 있습니다.</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default VulnerabilityAnalysis;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAkB,uBAAuB;AAC7D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SACEC,UAAU,EACVC,gBAAgB,EAEhBC,qBAAqB,QAChB,6BAA6B;AACpC,SAASC,WAAW,QAA8C,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQvF,MAAMC,qBAA+B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5C,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAM;IACJkB,QAAQ;IACRC,YAAY;IACZC,SAAS,EAAE;MAAEC;IAAO,CAAC;IACrBC,KAAK;IACLC;EACF,CAAC,GAAGtB,OAAO,CAAW;IACpBuB,aAAa,EAAE;MACbC,iBAAiB,EAAE,IAAI;MACvBC,uBAAuB,EAAE,IAAI;MAC7BC,QAAQ,EAAE,QAAQ;MAClBC,IAAI,EAAE;IACR;EACF,CAAC,CAAC;;EAEF;EACA,MAAMC,gBAAgB,GAAG3B,WAAW,CAACM,WAAW,CAACsB,oBAAoB,EAAE;IACrEC,SAAS,EAAGC,QAAQ,IAAK;MACvB,MAAM;QAAEC;MAAK,CAAC,GAAGD,QAAQ;MACzB,IAAIC,IAAI,CAACC,OAAO,EAAE;QAChB9B,KAAK,CAAC8B,OAAO,CAAC,cAAc,CAAC;QAC7B,IAAID,IAAI,CAACE,WAAW,EAAE;UACpBpB,QAAQ,CAAC,YAAYkB,IAAI,CAACE,WAAW,EAAE,CAAC;QAC1C,CAAC,MAAM;UACLpB,QAAQ,CAAC,UAAU,CAAC;QACtB;MACF,CAAC,MAAM;QACLX,KAAK,CAACgC,KAAK,CAACH,IAAI,CAACI,OAAO,IAAI,gBAAgB,CAAC;MAC/C;MACApB,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC;IACDqB,OAAO,EAAGF,KAAU,IAAK;MAAA,IAAAG,eAAA,EAAAC,oBAAA;MACvBC,OAAO,CAACL,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvChC,KAAK,CAACgC,KAAK,CAAC,EAAAG,eAAA,GAAAH,KAAK,CAACJ,QAAQ,cAAAO,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBN,IAAI,cAAAO,oBAAA,uBAApBA,oBAAA,CAAsBE,MAAM,KAAI,kBAAkB,CAAC;MAC/DzB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,CAAC;EAEF,MAAM0B,QAAQ,GAAG,MAAOV,IAAc,IAAK;IACzChB,cAAc,CAAC,IAAI,CAAC;IAEpB,MAAM2B,eAAgC,GAAG;MACvCC,MAAM,EAAE;QACNC,KAAK,EAAEb,IAAI,CAACa,KAAK;QACjBC,WAAW,EAAEd,IAAI,CAACc,WAAW;QAC7BpB,QAAQ,EAAEM,IAAI,CAACN,QAAQ;QACvBqB,eAAe,EAAEf,IAAI,CAACe,eAAe;QACrCC,kBAAkB,EAAEhB,IAAI,CAACgB,kBAAkB;QAC3CC,aAAa,EAAEjB,IAAI,CAACiB,aAAa;QACjCC,MAAM,EAAElB,IAAI,CAACkB,MAAM;QACnBC,kBAAkB,EAAEnB,IAAI,CAACmB,kBAAkB;QAC3CC,gBAAgB,EAAEpB,IAAI,CAACoB,gBAAgB;QACvCC,QAAQ,EAAErB,IAAI,CAACqB,QAAQ;QACvB1B,IAAI,EAAEK,IAAI,CAACL,IAAI,IAAI;MACrB,CAAC;MACD2B,QAAQ,EAAEtB,IAAI,CAACsB,QAAQ;MACvB9B,iBAAiB,EAAEQ,IAAI,CAACR,iBAAiB;MACzCC,uBAAuB,EAAEO,IAAI,CAACP;IAChC,CAAC;IAEDG,gBAAgB,CAAC2B,MAAM,CAACZ,eAAe,CAAC;EAC1C,CAAC;EAED,MAAMa,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAe,CAAC,EAC/D;IAAEF,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAkB,CAAC,EAC1D;IAAEF,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAkB,CAAC,EAC9D;IAAEF,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAgB,CAAC,EACtD;IAAEF,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAgB,CAAC,CACzD;EAED,MAAMC,cAAc,GAAG,CACrB;IAAEH,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAa,CAAC,EAClC;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAClC;IAAED,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAClD;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAc,CAAC,EACzC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,CACrC;EAED,oBACEjD,OAAA;IAAKoD,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAEhCrD,OAAA;MAAKoD,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBrD,OAAA;QAAKoD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCrD,OAAA,CAACL,UAAU;UAACyD,SAAS,EAAC;QAA+B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxDzD,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAIoD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5DzD,OAAA;YAAGoD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzD,OAAA;MAAMiC,QAAQ,EAAExB,YAAY,CAACwB,QAAQ,CAAE;MAACmB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAC3DrD,OAAA;QAAKoD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBrD,OAAA;UAAKoD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BrD,OAAA;YAAIoD,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBACjErD,OAAA,CAACJ,gBAAgB;cAACwD,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mCAE/C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNzD,OAAA;UAAKoD,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAElCrD,OAAA;YAAAqD,QAAA,gBACErD,OAAA;cAAOoD,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,eACzB,eAAArD,OAAA;gBAAMoD,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACRzD,OAAA;cACE0D,IAAI,EAAC,MAAM;cACXN,SAAS,EAAC,YAAY;cACtBO,WAAW,EAAC,sEAAe;cAAA,GACvBnD,QAAQ,CAAC,OAAO,EAAE;gBACpBoD,QAAQ,EAAE,WAAW;gBACrBC,SAAS,EAAE;kBAAEb,KAAK,EAAE,CAAC;kBAAErB,OAAO,EAAE;gBAAsB;cACxD,CAAC;YAAC;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACD9C,MAAM,CAACyB,KAAK,iBACXpC,OAAA;cAAGoD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE1C,MAAM,CAACyB,KAAK,CAACT;YAAO;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACpD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNzD,OAAA;YAAAqD,QAAA,gBACErD,OAAA;cAAOoD,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,4BACtB,eAAArD,OAAA;gBAAMoD,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACRzD,OAAA;cACE8D,IAAI,EAAE,CAAE;cACRV,SAAS,EAAC,eAAe;cACzBO,WAAW,EAAC,+GAA0B;cAAA,GAClCnD,QAAQ,CAAC,aAAa,EAAE;gBAC1BoD,QAAQ,EAAE,WAAW;gBACrBC,SAAS,EAAE;kBAAEb,KAAK,EAAE,EAAE;kBAAErB,OAAO,EAAE;gBAAuB;cAC1D,CAAC;YAAC;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACD9C,MAAM,CAAC0B,WAAW,iBACjBrC,OAAA;cAAGoD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAE1C,MAAM,CAAC0B,WAAW,CAACV;YAAO;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC1D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNzD,OAAA;YAAKoD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDrD,OAAA;cAAAqD,QAAA,gBACErD,OAAA;gBAAOoD,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9CzD,OAAA;gBAAQoD,SAAS,EAAC,aAAa;gBAAA,GAAK5C,QAAQ,CAAC,UAAU,CAAC;gBAAA6C,QAAA,EACrDN,eAAe,CAACgB,GAAG,CAAEC,MAAM,iBAC1BhE,OAAA;kBAA2BgD,KAAK,EAAEgB,MAAM,CAAChB,KAAM;kBAAAK,QAAA,EAC5CW,MAAM,CAACf;gBAAK,GADFe,MAAM,CAAChB,KAAK;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNzD,OAAA;cAAAqD,QAAA,gBACErD,OAAA;gBAAOoD,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9CzD,OAAA;gBACE0D,IAAI,EAAC,MAAM;gBACXN,SAAS,EAAC,YAAY;gBACtBO,WAAW,EAAC,uEAAqB;gBAAA,GAC7BnD,QAAQ,CAAC,iBAAiB;cAAC;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNzD,OAAA;YAAKoD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDrD,OAAA;cAAAqD,QAAA,gBACErD,OAAA;gBAAOoD,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5CzD,OAAA;gBACE0D,IAAI,EAAC,MAAM;gBACXN,SAAS,EAAC,YAAY;gBACtBO,WAAW,EAAC,kCAA6B;gBAAA,GACrCnD,QAAQ,CAAC,oBAAoB;cAAC;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzD,OAAA;cAAAqD,QAAA,gBACErD,OAAA;gBAAOoD,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3CzD,OAAA;gBACE0D,IAAI,EAAC,MAAM;gBACXN,SAAS,EAAC,YAAY;gBACtBO,WAAW,EAAC,kCAA6B;gBAAA,GACrCnD,QAAQ,CAAC,eAAe;cAAC;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNzD,OAAA;YAAAqD,QAAA,gBACErD,OAAA;cAAOoD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzCzD,OAAA;cACE8D,IAAI,EAAE,CAAE;cACRV,SAAS,EAAC,eAAe;cACzBO,WAAW,EAAC,kIAA8B;cAAA,GACtCnD,QAAQ,CAAC,QAAQ;YAAC;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNzD,OAAA;YAAAqD,QAAA,gBACErD,OAAA;cAAOoD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3CzD,OAAA;cACE8D,IAAI,EAAE,CAAE;cACRV,SAAS,EAAC,eAAe;cACzBO,WAAW,EAAC,2HAA4B;cAAA,GACpCnD,QAAQ,CAAC,oBAAoB;YAAC;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNzD,OAAA;YAAAqD,QAAA,gBACErD,OAAA;cAAOoD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjDzD,OAAA;cACE8D,IAAI,EAAE,CAAE;cACRV,SAAS,EAAC,eAAe;cACzBO,WAAW,EAAC,oIAAgC;cAAA,GACxCnD,QAAQ,CAAC,kBAAkB;YAAC;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNzD,OAAA;YAAAqD,QAAA,gBACErD,OAAA;cAAOoD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzCzD,OAAA;cACE0D,IAAI,EAAC,MAAM;cACXN,SAAS,EAAC,YAAY;cACtBO,WAAW,EAAC,iDAAc;cAAA,GACtBnD,QAAQ,CAAC,UAAU;YAAC;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA;QAAKoD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBrD,OAAA;UAAKoD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BrD,OAAA;YAAIoD,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBACjErD,OAAA,CAACL,UAAU;cAACyD,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gCAEzC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNzD,OAAA;UAAKoD,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAElCrD,OAAA;YAAAqD,QAAA,gBACErD,OAAA;cAAOoD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3CzD,OAAA;cAAQoD,SAAS,EAAC,aAAa;cAAA,GAAK5C,QAAQ,CAAC,UAAU,CAAC;cAAA6C,QAAA,EACrDF,cAAc,CAACY,GAAG,CAAEC,MAAM,iBACzBhE,OAAA;gBAA2BgD,KAAK,EAAEgB,MAAM,CAAChB,KAAM;gBAAAK,QAAA,EAC5CW,MAAM,CAACf;cAAK,GADFe,MAAM,CAAChB,KAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACTzD,OAAA;cAAGoD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNzD,OAAA;YAAKoD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrD,OAAA;cAAKoD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCrD,OAAA;gBACE0D,IAAI,EAAC,UAAU;gBACfN,SAAS,EAAC,yEAAyE;gBAAA,GAC/E5C,QAAQ,CAAC,mBAAmB;cAAC;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACFzD,OAAA;gBAAOoD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNzD,OAAA;cAAKoD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCrD,OAAA;gBACE0D,IAAI,EAAC,UAAU;gBACfN,SAAS,EAAC,yEAAyE;gBAAA,GAC/E5C,QAAQ,CAAC,yBAAyB;cAAC;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACFzD,OAAA;gBAAOoD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA;QAAKoD,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzCrD,OAAA;UACE0D,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,aAAa;UACvBa,OAAO,EAAEA,CAAA,KAAMpD,KAAK,CAAC,CAAE;UACvBqD,QAAQ,EAAE5D,WAAY;UAAA+C,QAAA,EACvB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzD,OAAA;UACE0D,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,aAAa;UACvBc,QAAQ,EAAE5D,WAAY;UAAA+C,QAAA,EAErB/C,WAAW,gBACVN,OAAA,CAAAE,SAAA;YAAAmD,QAAA,gBACErD,OAAA;cAAKoD,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,0BAE1C;UAAA,eAAE,CAAC,gBAEHzD,OAAA,CAAAE,SAAA;YAAAmD,QAAA,gBACErD,OAAA,CAACL,UAAU;cAACyD,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gCAEzC;UAAA,eAAE;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPzD,OAAA;MAAKoD,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBrD,OAAA;QAAKoD,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBrD,OAAA;UAAKoD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBrD,OAAA,CAACH,qBAAqB;YAACuD,SAAS,EAAC;UAAiD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrFzD,OAAA;YAAAqD,QAAA,gBACErD,OAAA;cAAIoD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DzD,OAAA;cAAKoD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,eACzCrD,OAAA;gBAAIoD,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC7CrD,OAAA;kBAAAqD,QAAA,EAAI;gBAA4C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrDzD,OAAA;kBAAAqD,QAAA,EAAI;gBAAwC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDzD,OAAA;kBAAAqD,QAAA,EAAI;gBAAgD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrD,EAAA,CAjVID,qBAA+B;EAAA,QAClBV,WAAW,EASxBF,OAAO,EAUcC,WAAW;AAAA;AAAA2E,EAAA,GApBhChE,qBAA+B;AAmVrC,eAAeA,qBAAqB;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}