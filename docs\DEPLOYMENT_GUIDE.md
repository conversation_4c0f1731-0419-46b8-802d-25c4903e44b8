# 배포 가이드

Bugcrowd AI 취약점 검증 툴의 프로덕션 환경 배포 가이드입니다.

## 배포 옵션

### 1. Docker Compose (권장)
### 2. Kubernetes
### 3. 클라우드 서비스 (AWS, GCP, Azure)
### 4. 수동 배포

## Docker Compose 배포

### 사전 요구사항
- Docker 20.10+
- Docker Compose 2.0+
- 최소 4GB RAM
- 최소 10GB 디스크 공간

### 배포 단계

1. **저장소 클론**
```bash
git clone <repository-url>
cd aidesk
```

2. **환경 변수 설정**
```bash
cp .env.example .env
```

`.env` 파일 편집:
```env
# 프로덕션 설정
DEBUG=false
SECRET_KEY=your-production-secret-key-here

# 데이터베이스 (PostgreSQL)
DATABASE_URL=*********************************************/vulnerability_db

# AI 모델 설정
OPENAI_API_KEY=your-openai-api-key
OLLAMA_HOST=http://ollama:11434

# 보안 설정
JWT_SECRET_KEY=your-jwt-secret-key
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# 로깅
LOG_LEVEL=INFO
```

3. **SSL 인증서 설정** (선택사항)
```bash
mkdir -p nginx/ssl
# SSL 인증서 파일을 nginx/ssl/ 디렉토리에 복사
```

4. **배포 실행**
```bash
docker-compose -f docker-compose.prod.yml up -d
```

5. **초기 데이터 로드**
```bash
docker-compose exec backend python -c "
from app.services.taxonomy_service import TaxonomyService
service = TaxonomyService()
print('분류 체계 로드 완료')
"
```

## Kubernetes 배포

### 사전 요구사항
- Kubernetes 1.20+
- kubectl 설정 완료
- Helm 3.0+ (선택사항)

### 배포 파일 생성

1. **네임스페이스 생성**
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: bugcrowd-ai
```

2. **ConfigMap 생성**
```yaml
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: bugcrowd-ai
data:
  DATABASE_URL: "********************************************/vulnerability_db"
  OLLAMA_HOST: "http://ollama:11434"
  LOG_LEVEL: "INFO"
```

3. **Secret 생성**
```yaml
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
  namespace: bugcrowd-ai
type: Opaque
data:
  SECRET_KEY: <base64-encoded-secret>
  OPENAI_API_KEY: <base64-encoded-api-key>
  JWT_SECRET_KEY: <base64-encoded-jwt-secret>
```

4. **PostgreSQL 배포**
```yaml
# k8s/postgres.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: bugcrowd-ai
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15
        env:
        - name: POSTGRES_DB
          value: vulnerability_db
        - name: POSTGRES_USER
          value: postgres
        - name: POSTGRES_PASSWORD
          value: password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: bugcrowd-ai
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
```

5. **백엔드 배포**
```yaml
# k8s/backend.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: bugcrowd-ai
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: your-registry/bugcrowd-ai-backend:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: app-config
        - secretRef:
            name: app-secrets
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: backend
  namespace: bugcrowd-ai
spec:
  selector:
    app: backend
  ports:
  - port: 8000
    targetPort: 8000
```

6. **프론트엔드 배포**
```yaml
# k8s/frontend.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: bugcrowd-ai
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
      - name: frontend
        image: your-registry/bugcrowd-ai-frontend:latest
        ports:
        - containerPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: frontend
  namespace: bugcrowd-ai
spec:
  selector:
    app: frontend
  ports:
  - port: 80
    targetPort: 80
```

7. **Ingress 설정**
```yaml
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: app-ingress
  namespace: bugcrowd-ai
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - yourdomain.com
    secretName: app-tls
  rules:
  - host: yourdomain.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: backend
            port:
              number: 8000
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend
            port:
              number: 80
```

### 배포 실행
```bash
kubectl apply -f k8s/
```

## 클라우드 배포

### AWS ECS 배포

1. **ECR 리포지토리 생성**
```bash
aws ecr create-repository --repository-name bugcrowd-ai-backend
aws ecr create-repository --repository-name bugcrowd-ai-frontend
```

2. **이미지 빌드 및 푸시**
```bash
# 백엔드
docker build -t bugcrowd-ai-backend ./backend
docker tag bugcrowd-ai-backend:latest <account-id>.dkr.ecr.<region>.amazonaws.com/bugcrowd-ai-backend:latest
docker push <account-id>.dkr.ecr.<region>.amazonaws.com/bugcrowd-ai-backend:latest

# 프론트엔드
docker build -t bugcrowd-ai-frontend ./frontend
docker tag bugcrowd-ai-frontend:latest <account-id>.dkr.ecr.<region>.amazonaws.com/bugcrowd-ai-frontend:latest
docker push <account-id>.dkr.ecr.<region>.amazonaws.com/bugcrowd-ai-frontend:latest
```

3. **ECS 태스크 정의 생성**
```json
{
  "family": "bugcrowd-ai",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::<account-id>:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "backend",
      "image": "<account-id>.dkr.ecr.<region>.amazonaws.com/bugcrowd-ai-backend:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "DATABASE_URL",
          "value": "************************************************/vulnerability_db"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/bugcrowd-ai",
          "awslogs-region": "<region>",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

### Google Cloud Run 배포

1. **프로젝트 설정**
```bash
gcloud config set project your-project-id
gcloud services enable run.googleapis.com
```

2. **이미지 빌드 및 배포**
```bash
# 백엔드
gcloud builds submit --tag gcr.io/your-project-id/bugcrowd-ai-backend ./backend
gcloud run deploy backend --image gcr.io/your-project-id/bugcrowd-ai-backend --platform managed

# 프론트엔드
gcloud builds submit --tag gcr.io/your-project-id/bugcrowd-ai-frontend ./frontend
gcloud run deploy frontend --image gcr.io/your-project-id/bugcrowd-ai-frontend --platform managed
```

## 모니터링 및 로깅

### Prometheus + Grafana 설정

1. **Prometheus 설정**
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'bugcrowd-ai-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
```

2. **Grafana 대시보드**
- API 응답 시간
- 분석 처리량
- 오류율
- 시스템 리소스 사용량

### 로그 집계

1. **ELK Stack 설정**
```yaml
# logging/elasticsearch.yml
version: '3'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"

  logstash:
    image: docker.elastic.co/logstash/logstash:8.5.0
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf

  kibana:
    image: docker.elastic.co/kibana/kibana:8.5.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
```

## 보안 고려사항

### 1. 네트워크 보안
- HTTPS 강제 사용
- 방화벽 설정
- VPN 또는 프라이빗 네트워크 사용

### 2. 인증 및 권한
- JWT 토큰 기반 인증
- 역할 기반 접근 제어 (RBAC)
- API 키 관리

### 3. 데이터 보안
- 데이터베이스 암호화
- 백업 암호화
- 민감 정보 마스킹

### 4. 컨테이너 보안
- 최소 권한 원칙
- 보안 스캔
- 정기적인 이미지 업데이트

## 백업 및 복구

### 데이터베이스 백업
```bash
# PostgreSQL 백업
docker-compose exec db pg_dump -U postgres vulnerability_db > backup.sql

# 복구
docker-compose exec -T db psql -U postgres vulnerability_db < backup.sql
```

### 설정 파일 백업
```bash
# 중요 설정 파일들
tar -czf config-backup.tar.gz .env docker-compose.yml nginx/
```

## 성능 최적화

### 1. 데이터베이스 최적화
- 인덱스 최적화
- 쿼리 성능 튜닝
- 연결 풀 설정

### 2. 캐싱
- Redis 캐시 활용
- CDN 사용
- 정적 파일 캐싱

### 3. 로드 밸런싱
- 다중 인스턴스 배포
- 헬스 체크 설정
- 자동 스케일링

## 문제 해결

### 일반적인 문제들

1. **메모리 부족**
```bash
# 컨테이너 메모리 사용량 확인
docker stats

# 메모리 제한 증가
docker-compose up --scale backend=2
```

2. **데이터베이스 연결 오류**
```bash
# 연결 상태 확인
docker-compose exec backend python -c "
from app.database import engine
print(engine.execute('SELECT 1').scalar())
"
```

3. **AI 모델 응답 지연**
```bash
# 모델 상태 확인
curl http://localhost:11434/api/tags  # Ollama
```

## 업데이트 및 유지보수

### 롤링 업데이트
```bash
# 새 이미지 빌드
docker-compose build

# 무중단 업데이트
docker-compose up -d --no-deps backend
docker-compose up -d --no-deps frontend
```

### 정기 유지보수
- 로그 로테이션
- 데이터베이스 정리
- 보안 패치 적용
- 성능 모니터링
