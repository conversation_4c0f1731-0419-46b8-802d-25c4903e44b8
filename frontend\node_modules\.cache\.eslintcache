[{"C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\App.tsx": "2"}, {"size": 1224, "mtime": 1751960398779, "results": "3", "hashOfConfig": "4"}, {"size": 1539, "mtime": 1751960476249, "results": "5", "hashOfConfig": "4"}, {"filePath": "6", "messages": "7", "suppressedMessages": "8", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "14kq382", {"filePath": "9", "messages": "10", "suppressedMessages": "11", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\App.tsx", [], []]