[{"C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\pages\\Dashboard.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\pages\\VulnerabilityAnalysis.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\pages\\AnalysisResults.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\pages\\Reports.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\pages\\TaxonomyBrowser.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\pages\\Settings.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\components\\Layout.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\services\\api.ts": "10"}, {"size": 1234, "mtime": 1751961586885, "results": "11", "hashOfConfig": "12"}, {"size": 1077, "mtime": 1751961708402, "results": "13", "hashOfConfig": "12"}, {"size": 10023, "mtime": 1751961833663, "results": "14", "hashOfConfig": "12"}, {"size": 12587, "mtime": 1751961901238, "results": "15", "hashOfConfig": "12"}, {"size": 12613, "mtime": 1751961853375, "results": "16", "hashOfConfig": "12"}, {"size": 13692, "mtime": 1751961864619, "results": "17", "hashOfConfig": "12"}, {"size": 13470, "mtime": 1751961882745, "results": "18", "hashOfConfig": "12"}, {"size": 16893, "mtime": 1751959013130, "results": "19", "hashOfConfig": "12"}, {"size": 6366, "mtime": 1751958482735, "results": "20", "hashOfConfig": "12"}, {"size": 6251, "mtime": 1751958454711, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "14kq382", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\pages\\Dashboard.tsx", ["52"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\pages\\VulnerabilityAnalysis.tsx", ["53", "54", "55"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\pages\\AnalysisResults.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\pages\\Reports.tsx", ["56", "57", "58", "59", "60", "61", "62", "63"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\pages\\TaxonomyBrowser.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\pages\\Settings.tsx", ["64"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\components\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\aidesk\\frontend\\src\\services\\api.ts", [], [], {"ruleId": "65", "severity": 1, "message": "66", "line": 9, "column": 3, "nodeType": "67", "messageId": "68", "endLine": 9, "endColumn": 26}, {"ruleId": "65", "severity": 1, "message": "69", "line": 3, "column": 23, "nodeType": "67", "messageId": "68", "endLine": 3, "endColumn": 31}, {"ruleId": "65", "severity": 1, "message": "66", "line": 9, "column": 3, "nodeType": "67", "messageId": "68", "endLine": 9, "endColumn": 26}, {"ruleId": "65", "severity": 1, "message": "70", "line": 28, "column": 5, "nodeType": "67", "messageId": "68", "endLine": 28, "endColumn": 10}, {"ruleId": "65", "severity": 1, "message": "71", "line": 7, "column": 3, "nodeType": "67", "messageId": "68", "endLine": 7, "endColumn": 15}, {"ruleId": "65", "severity": 1, "message": "72", "line": 8, "column": 3, "nodeType": "67", "messageId": "68", "endLine": 8, "endColumn": 13}, {"ruleId": "65", "severity": 1, "message": "73", "line": 11, "column": 27, "nodeType": "67", "messageId": "68", "endLine": 11, "endColumn": 37}, {"ruleId": "65", "severity": 1, "message": "74", "line": 11, "column": 39, "nodeType": "67", "messageId": "68", "endLine": 11, "endColumn": 47}, {"ruleId": "65", "severity": 1, "message": "75", "line": 12, "column": 10, "nodeType": "67", "messageId": "68", "endLine": 12, "endColumn": 12}, {"ruleId": "65", "severity": 1, "message": "76", "line": 37, "column": 45, "nodeType": "67", "messageId": "68", "endLine": 37, "endColumn": 59}, {"ruleId": "65", "severity": 1, "message": "77", "line": 52, "column": 17, "nodeType": "67", "messageId": "68", "endLine": 52, "endColumn": 28}, {"ruleId": "65", "severity": 1, "message": "78", "line": 58, "column": 9, "nodeType": "67", "messageId": "68", "endLine": 58, "endColumn": 15}, {"ruleId": "65", "severity": 1, "message": "79", "line": 50, "column": 5, "nodeType": "67", "messageId": "68", "endLine": 50, "endColumn": 13}, "@typescript-eslint/no-unused-vars", "'ExclamationTriangleIcon' is defined but never used.", "Identifier", "unusedVar", "'useQuery' is defined but never used.", "'watch' is assigned a value but never used.", "'CalendarIcon' is defined but never used.", "'FunnelIcon' is defined but never used.", "'startOfDay' is defined but never used.", "'endOfDay' is defined but never used.", "'ko' is defined but never used.", "'summaryLoading' is assigned a value but never used.", "'allAnalyses' is assigned a value but never used.", "'COLORS' is assigned a value but never used.", "'setValue' is assigned a value but never used."]