import React from 'react';

function App() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                🛡️ Bugcrowd AI 취약점 검증 툴
              </h1>
              <p className="text-xl text-gray-600 mb-8">
                AI를 활용한 자동 취약점 분석 및 분류 시스템
              </p>
              <div className="space-y-4">
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                  ✅ 백엔드 서버: 정상 작동 (http://localhost:8000)
                </div>
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                  ✅ 프론트엔드 서버: 정상 작동 (http://localhost:3000)
                </div>
                <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
                  📚 API 문서: <a href="http://localhost:8000/docs" className="underline" target="_blank" rel="noopener noreferrer">http://localhost:8000/docs</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
