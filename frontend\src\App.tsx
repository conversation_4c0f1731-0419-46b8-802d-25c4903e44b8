import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import VulnerabilityAnalysis from './pages/VulnerabilityAnalysis';
import AnalysisResults from './pages/AnalysisResults';
import TaxonomyBrowser from './pages/TaxonomyBrowser';
import Reports from './pages/Reports';
import Settings from './pages/Settings';

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/analysis" element={<VulnerabilityAnalysis />} />
          <Route path="/results" element={<AnalysisResults />} />
          <Route path="/results/:analysisId" element={<AnalysisResults />} />
          <Route path="/taxonomy" element={<TaxonomyBrowser />} />
          <Route path="/reports" element={<Reports />} />
          <Route path="/settings" element={<Settings />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;
