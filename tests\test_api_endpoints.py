"""
API 엔드포인트 테스트
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch
import json

# 테스트용 앱 임포트 (실제 구현에서는 main 앱을 임포트)
from backend.app.main import app

client = TestClient(app)

class TestVulnerabilityAPI:
    """취약점 API 테스트"""
    
    def test_create_vulnerability_report(self):
        """취약점 보고서 생성 테스트"""
        report_data = {
            "title": "Test XSS Vulnerability",
            "description": "This is a test XSS vulnerability for testing purposes",
            "severity": "medium",
            "affected_system": "Web Application",
            "vulnerability_type": "Cross-Site Scripting",
            "attack_vector": "Network"
        }
        
        response = client.post("/api/v1/vulnerability/reports", json=report_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == report_data["title"]
        assert data["description"] == report_data["description"]
        assert "id" in data
    
    def test_create_vulnerability_report_invalid_data(self):
        """잘못된 데이터로 취약점 보고서 생성 테스트"""
        invalid_data = {
            "title": "",  # 빈 제목
            "description": ""  # 빈 설명
        }
        
        response = client.post("/api/v1/vulnerability/reports", json=invalid_data)
        
        assert response.status_code == 400
        data = response.json()
        assert "errors" in data["detail"]
    
    def test_get_vulnerability_reports(self):
        """취약점 보고서 목록 조회 테스트"""
        response = client.get("/api/v1/vulnerability/reports")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_vulnerability_reports_with_pagination(self):
        """페이지네이션을 사용한 취약점 보고서 조회 테스트"""
        response = client.get("/api/v1/vulnerability/reports?skip=0&limit=5")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) <= 5
    
    def test_get_vulnerability_report_by_id(self):
        """ID로 취약점 보고서 조회 테스트"""
        # 먼저 보고서 생성
        report_data = {
            "title": "Test Report for ID lookup",
            "description": "Test description"
        }
        create_response = client.post("/api/v1/vulnerability/reports", json=report_data)
        created_report = create_response.json()
        
        # 생성된 보고서 조회
        response = client.get(f"/api/v1/vulnerability/reports/{created_report['id']}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == created_report["id"]
    
    def test_get_nonexistent_vulnerability_report(self):
        """존재하지 않는 취약점 보고서 조회 테스트"""
        response = client.get("/api/v1/vulnerability/reports/nonexistent-id")
        
        assert response.status_code == 404
    
    def test_update_vulnerability_report(self):
        """취약점 보고서 업데이트 테스트"""
        # 먼저 보고서 생성
        report_data = {
            "title": "Original Title",
            "description": "Original description"
        }
        create_response = client.post("/api/v1/vulnerability/reports", json=report_data)
        created_report = create_response.json()
        
        # 보고서 업데이트
        update_data = {
            "title": "Updated Title",
            "severity": "high"
        }
        response = client.put(f"/api/v1/vulnerability/reports/{created_report['id']}", json=update_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == "Updated Title"
        assert data["severity"] == "high"
    
    def test_delete_vulnerability_report(self):
        """취약점 보고서 삭제 테스트"""
        # 먼저 보고서 생성
        report_data = {
            "title": "Report to Delete",
            "description": "This report will be deleted"
        }
        create_response = client.post("/api/v1/vulnerability/reports", json=report_data)
        created_report = create_response.json()
        
        # 보고서 삭제
        response = client.delete(f"/api/v1/vulnerability/reports/{created_report['id']}")
        
        assert response.status_code == 200
        data = response.json()
        assert "성공적으로 삭제" in data["message"]
    
    def test_get_vulnerability_statistics(self):
        """취약점 통계 조회 테스트"""
        response = client.get("/api/v1/vulnerability/statistics")
        
        assert response.status_code == 200
        data = response.json()
        assert "total_reports" in data
        assert "severity_distribution" in data

class TestAnalysisAPI:
    """분석 API 테스트"""
    
    def test_analyze_vulnerability(self):
        """취약점 분석 테스트"""
        analysis_request = {
            "report": {
                "title": "XSS in search form",
                "description": "Cross-site scripting vulnerability in the search functionality",
                "steps_to_reproduce": "1. Go to search\n2. Enter <script>alert(1)</script>\n3. Submit",
                "proof_of_concept": "<script>alert('XSS')</script>"
            },
            "ai_model": "gpt-4",
            "include_reasoning": True,
            "include_recommendations": True
        }
        
        response = client.post("/api/v1/analysis/analyze", json=analysis_request)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "analysis_id" in data
    
    def test_analyze_vulnerability_invalid_data(self):
        """잘못된 데이터로 취약점 분석 테스트"""
        invalid_request = {
            "report": {
                "title": "",  # 빈 제목
                "description": ""  # 빈 설명
            }
        }
        
        response = client.post("/api/v1/analysis/analyze", json=invalid_request)
        
        assert response.status_code == 400
    
    def test_get_analysis_result(self):
        """분석 결과 조회 테스트"""
        # 먼저 분석 요청
        analysis_request = {
            "report": {
                "title": "Test Analysis",
                "description": "Test description for analysis"
            }
        }
        analyze_response = client.post("/api/v1/analysis/analyze", json=analysis_request)
        analysis_data = analyze_response.json()
        
        if analysis_data["success"] and "analysis_id" in analysis_data:
            # 분석 결과 조회
            response = client.get(f"/api/v1/analysis/results/{analysis_data['analysis_id']}")
            
            # 분석이 아직 진행 중일 수 있으므로 200 또는 404 모두 허용
            assert response.status_code in [200, 404]
    
    def test_get_all_analysis_results(self):
        """모든 분석 결과 조회 테스트"""
        response = client.get("/api/v1/analysis/results")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_analysis_results_by_status(self):
        """상태별 분석 결과 조회 테스트"""
        response = client.get("/api/v1/analysis/results?status=completed")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_verify_analysis_result(self):
        """분석 결과 검증 테스트"""
        # 실제 구현에서는 먼저 완료된 분석 결과가 필요
        verification_data = {
            "verified_by": "test_user",
            "notes": "Manual verification completed"
        }
        
        # 더미 분석 ID로 테스트 (실제로는 존재하는 ID 사용)
        response = client.put("/api/v1/analysis/results/dummy-id/verify", json=verification_data)
        
        # 존재하지 않는 ID이므로 404 예상
        assert response.status_code == 404

class TestReportsAPI:
    """리포트 API 테스트"""
    
    def test_get_taxonomy(self):
        """분류 체계 조회 테스트"""
        response = client.get("/api/v1/reports/taxonomy")
        
        assert response.status_code == 200
        data = response.json()
        assert "metadata" in data
        assert "content" in data
    
    def test_get_categories(self):
        """카테고리 목록 조회 테스트"""
        response = client.get("/api/v1/reports/taxonomy/categories")
        
        assert response.status_code == 200
        data = response.json()
        assert "total_count" in data
        assert "categories" in data
    
    def test_search_taxonomy(self):
        """분류 체계 검색 테스트"""
        response = client.get("/api/v1/reports/taxonomy/search?q=xss")
        
        assert response.status_code == 200
        data = response.json()
        assert "query" in data
        assert "total_count" in data
        assert "results" in data
    
    def test_search_taxonomy_short_query(self):
        """짧은 검색어로 분류 체계 검색 테스트"""
        response = client.get("/api/v1/reports/taxonomy/search?q=x")
        
        assert response.status_code == 400
    
    def test_get_high_priority_vulnerabilities(self):
        """높은 우선순위 취약점 조회 테스트"""
        response = client.get("/api/v1/reports/taxonomy/high-priority")
        
        assert response.status_code == 200
        data = response.json()
        assert "total_count" in data
        assert "vulnerabilities" in data
    
    def test_get_taxonomy_statistics(self):
        """분류 체계 통계 조회 테스트"""
        response = client.get("/api/v1/reports/taxonomy/statistics")
        
        assert response.status_code == 200
        data = response.json()
        assert "total_categories" in data
        assert "total_subcategories" in data
    
    def test_get_analysis_summary(self):
        """분석 요약 조회 테스트"""
        response = client.get("/api/v1/reports/analysis-summary")
        
        assert response.status_code == 200
        data = response.json()
        assert "summary" in data
        assert "distributions" in data

class TestHealthCheck:
    """헬스 체크 테스트"""
    
    def test_root_endpoint(self):
        """루트 엔드포인트 테스트"""
        response = client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
        assert "status" in data
    
    def test_health_check(self):
        """헬스 체크 엔드포인트 테스트"""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"

if __name__ == '__main__':
    pytest.main([__file__])
