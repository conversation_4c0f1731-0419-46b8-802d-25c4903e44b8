{"ast": null, "code": "import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport toInteger from \"../toInteger/index.js\";\nexport default function setUTCISODay(dirtyDate, dirtyDay) {\n  requiredArgs(2, arguments);\n  var day = toInteger(dirtyDay);\n  if (day % 7 === 0) {\n    day = day - 7;\n  }\n  var weekStartsOn = 1;\n  var date = toDate(dirtyDate);\n  var currentDay = date.getUTCDay();\n  var remainder = day % 7;\n  var dayIndex = (remainder + 7) % 7;\n  var diff = (dayIndex < weekStartsOn ? 7 : 0) + day - currentDay;\n  date.setUTCDate(date.getUTCDate() + diff);\n  return date;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "toInteger", "setUTCISODay", "dirtyDate", "dirtyDay", "arguments", "day", "weekStartsOn", "date", "currentDay", "getUTCDay", "remainder", "dayIndex", "diff", "setUTCDate", "getUTCDate"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/node_modules/date-fns/esm/_lib/setUTCISODay/index.js"], "sourcesContent": ["import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport toInteger from \"../toInteger/index.js\";\nexport default function setUTCISODay(dirtyDate, dirtyDay) {\n  requiredArgs(2, arguments);\n  var day = toInteger(dirtyDay);\n  if (day % 7 === 0) {\n    day = day - 7;\n  }\n  var weekStartsOn = 1;\n  var date = toDate(dirtyDate);\n  var currentDay = date.getUTCDay();\n  var remainder = day % 7;\n  var dayIndex = (remainder + 7) % 7;\n  var diff = (dayIndex < weekStartsOn ? 7 : 0) + day - currentDay;\n  date.setUTCDate(date.getUTCDate() + diff);\n  return date;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,eAAe,SAASC,YAAYA,CAACC,SAAS,EAAEC,QAAQ,EAAE;EACxDJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,GAAG,GAAGL,SAAS,CAACG,QAAQ,CAAC;EAC7B,IAAIE,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;IACjBA,GAAG,GAAGA,GAAG,GAAG,CAAC;EACf;EACA,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAIC,IAAI,GAAGT,MAAM,CAACI,SAAS,CAAC;EAC5B,IAAIM,UAAU,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC;EACjC,IAAIC,SAAS,GAAGL,GAAG,GAAG,CAAC;EACvB,IAAIM,QAAQ,GAAG,CAACD,SAAS,GAAG,CAAC,IAAI,CAAC;EAClC,IAAIE,IAAI,GAAG,CAACD,QAAQ,GAAGL,YAAY,GAAG,CAAC,GAAG,CAAC,IAAID,GAAG,GAAGG,UAAU;EAC/DD,IAAI,CAACM,UAAU,CAACN,IAAI,CAACO,UAAU,CAAC,CAAC,GAAGF,IAAI,CAAC;EACzC,OAAOL,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}