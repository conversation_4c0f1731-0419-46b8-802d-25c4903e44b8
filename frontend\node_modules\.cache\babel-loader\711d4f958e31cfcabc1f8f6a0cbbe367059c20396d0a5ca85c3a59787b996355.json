{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aidesk\\\\frontend\\\\src\\\\pages\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { useQuery } from '@tanstack/react-query';\nimport { ChartBarIcon, BeakerIcon, ShieldCheckIcon, DocumentTextIcon, CheckCircleIcon, ClockIcon } from '@heroicons/react/24/outline';\nimport { vulnerabilityApi, analysisApi, reportsApi } from '../services/api.ts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  // 통계 데이터 조회\n  const {\n    data: vulnerabilityStats\n  } = useQuery('vulnerability-statistics', vulnerabilityApi.getStatistics, {\n    refetchInterval: 30000\n  });\n  const {\n    data: analysisResults\n  } = useQuery('analysis-results', () => analysisApi.getAllAnalysisResults(), {\n    refetchInterval: 30000\n  });\n  const {\n    data: taxonomyStats\n  } = useQuery('taxonomy-statistics', reportsApi.getTaxonomyStatistics);\n\n  // 통계 계산\n  const stats = React.useMemo(() => {\n    const vulnData = (vulnerabilityStats === null || vulnerabilityStats === void 0 ? void 0 : vulnerabilityStats.data) || {};\n    const analysisData = (analysisResults === null || analysisResults === void 0 ? void 0 : analysisResults.data) || [];\n    const taxonomyData = (taxonomyStats === null || taxonomyStats === void 0 ? void 0 : taxonomyStats.data) || {};\n    const totalAnalyses = analysisData.length;\n    const completedAnalyses = analysisData.filter(a => a.status === 'completed').length;\n    const pendingAnalyses = analysisData.filter(a => a.status === 'pending').length;\n    const verifiedAnalyses = analysisData.filter(a => a.is_verified).length;\n    return {\n      totalReports: vulnData.total_reports || 0,\n      totalAnalyses,\n      completedAnalyses,\n      pendingAnalyses,\n      verifiedAnalyses,\n      totalCategories: taxonomyData.total_categories || 0,\n      severityDistribution: vulnData.severity_distribution || {},\n      completionRate: totalAnalyses > 0 ? (completedAnalyses / totalAnalyses * 100).toFixed(1) : '0'\n    };\n  }, [vulnerabilityStats, analysisResults, taxonomyStats]);\n  const quickActions = [{\n    name: '새 취약점 분석',\n    description: 'AI를 활용한 취약점 분석 시작',\n    href: '/analysis',\n    icon: BeakerIcon,\n    color: 'bg-primary-500'\n  }, {\n    name: '분석 결과 보기',\n    description: '완료된 분석 결과 확인',\n    href: '/results',\n    icon: ChartBarIcon,\n    color: 'bg-success-500'\n  }, {\n    name: '분류 체계 탐색',\n    description: 'Bugcrowd 취약점 분류 체계',\n    href: '/taxonomy',\n    icon: ShieldCheckIcon,\n    color: 'bg-secondary-500'\n  }, {\n    name: '리포트 생성',\n    description: '분석 결과 리포트 생성',\n    href: '/reports',\n    icon: DocumentTextIcon,\n    color: 'bg-warning-500'\n  }];\n  const recentAnalyses = React.useMemo(() => {\n    if (!(analysisResults !== null && analysisResults !== void 0 && analysisResults.data)) return [];\n    return analysisResults.data.sort((a, b) => new Date(b.started_at || 0).getTime() - new Date(a.started_at || 0).getTime()).slice(0, 5);\n  }, [analysisResults]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-900\",\n        children: \"\\uB300\\uC2DC\\uBCF4\\uB4DC\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-500\",\n        children: \"Bugcrowd AI \\uCDE8\\uC57D\\uC810 \\uAC80\\uC99D \\uD234\\uC758 \\uC804\\uCCB4 \\uD604\\uD669\\uC744 \\uD655\\uC778\\uD558\\uC138\\uC694.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(DocumentTextIcon, {\n                className: \"h-6 w-6 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"\\uCD1D \\uBCF4\\uACE0\\uC11C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: stats.totalReports\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(BeakerIcon, {\n                className: \"h-6 w-6 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"\\uCD1D \\uBD84\\uC11D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: stats.totalAnalyses\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                className: \"h-6 w-6 text-success-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"\\uC644\\uB8CC\\uC728\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: [stats.completionRate, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(ClockIcon, {\n                className: \"h-6 w-6 text-warning-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500 truncate\",\n                  children: \"\\uB300\\uAE30 \\uC911\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-gray-900\",\n                  children: stats.pendingAnalyses\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-lg font-medium text-gray-900 mb-4\",\n        children: \"\\uBE60\\uB978 \\uC791\\uC5C5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4\",\n        children: quickActions.map(action => /*#__PURE__*/_jsxDEV(Link, {\n          to: action.href,\n          className: \"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg shadow hover:shadow-md transition-shadow\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `rounded-lg inline-flex p-3 ${action.color} text-white`,\n              children: /*#__PURE__*/_jsxDEV(action.icon, {\n                className: \"h-6 w-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 group-hover:text-primary-600\",\n              children: action.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-sm text-gray-500\",\n              children: action.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this)]\n        }, action.name, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"\\uCD5C\\uADFC \\uBD84\\uC11D \\uACB0\\uACFC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: recentAnalyses.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: recentAnalyses.map(analysis => {\n              var _analysis$id, _analysis$primary_cat;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-gray-900 truncate\",\n                    children: [\"\\uBD84\\uC11D #\", (_analysis$id = analysis.id) === null || _analysis$id === void 0 ? void 0 : _analysis$id.slice(-8)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: ((_analysis$primary_cat = analysis.primary_category) === null || _analysis$primary_cat === void 0 ? void 0 : _analysis$primary_cat.name) || '분류 중...'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [analysis.predicted_severity && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `badge severity-${analysis.predicted_severity}`,\n                    children: analysis.predicted_severity\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `badge ${analysis.status === 'completed' ? 'badge-success' : analysis.status === 'in_progress' ? 'badge-warning' : analysis.status === 'failed' ? 'badge-danger' : 'badge-secondary'}`,\n                    children: analysis.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 21\n                }, this)]\n              }, analysis.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"\\uC544\\uC9C1 \\uBD84\\uC11D \\uACB0\\uACFC\\uAC00 \\uC5C6\\uC2B5\\uB2C8\\uB2E4.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-footer\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/results\",\n            className: \"text-sm font-medium text-primary-600 hover:text-primary-500\",\n            children: \"\\uBAA8\\uB4E0 \\uACB0\\uACFC \\uBCF4\\uAE30 \\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"\\uC2EC\\uAC01\\uB3C4 \\uBD84\\uD3EC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: Object.entries(stats.severityDistribution).map(([severity, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `badge severity-${severity} mr-2`,\n                  children: severity\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600 capitalize\",\n                  children: severity\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: count\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this)]\n            }, severity, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"7NwVAJLIbgIIfxHKOPoZsFPmzMc=\", false, function () {\n  return [useQuery, useQuery, useQuery];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "Link", "useQuery", "ChartBarIcon", "BeakerIcon", "ShieldCheckIcon", "DocumentTextIcon", "CheckCircleIcon", "ClockIcon", "vulnerabilityApi", "analysisApi", "reportsApi", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "data", "vulnerabilityStats", "getStatistics", "refetchInterval", "analysisResults", "getAllAnalysisResults", "taxonomyStats", "getTaxonomyStatistics", "stats", "useMemo", "vulnData", "analysisData", "taxonomyData", "totalAnalyses", "length", "completedAnalyses", "filter", "a", "status", "pendingAnalyses", "verifiedAnalyses", "is_verified", "totalReports", "total_reports", "totalCategories", "total_categories", "severityDistribution", "severity_distribution", "completionRate", "toFixed", "quickActions", "name", "description", "href", "icon", "color", "recentAnalyses", "sort", "b", "Date", "started_at", "getTime", "slice", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "action", "to", "analysis", "_analysis$id", "_analysis$primary_cat", "id", "primary_category", "predicted_severity", "Object", "entries", "severity", "count", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/src/pages/Dashboard.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { useQuery } from '@tanstack/react-query';\nimport {\n  ChartBarIcon,\n  BeakerIcon,\n  ShieldCheckIcon,\n  DocumentTextIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon,\n  ClockIcon,\n} from '@heroicons/react/24/outline';\nimport { vulnerabilityApi, analysisApi, reportsApi } from '../services/api.ts';\n\nconst Dashboard: React.FC = () => {\n  // 통계 데이터 조회\n  const { data: vulnerabilityStats } = useQuery(\n    'vulnerability-statistics',\n    vulnerabilityApi.getStatistics,\n    { refetchInterval: 30000 }\n  );\n\n  const { data: analysisResults } = useQuery(\n    'analysis-results',\n    () => analysisApi.getAllAnalysisResults(),\n    { refetchInterval: 30000 }\n  );\n\n  const { data: taxonomyStats } = useQuery(\n    'taxonomy-statistics',\n    reportsApi.getTaxonomyStatistics\n  );\n\n  // 통계 계산\n  const stats = React.useMemo(() => {\n    const vulnData = vulnerabilityStats?.data || {};\n    const analysisData = analysisResults?.data || [];\n    const taxonomyData = taxonomyStats?.data || {};\n\n    const totalAnalyses = analysisData.length;\n    const completedAnalyses = analysisData.filter((a: any) => a.status === 'completed').length;\n    const pendingAnalyses = analysisData.filter((a: any) => a.status === 'pending').length;\n    const verifiedAnalyses = analysisData.filter((a: any) => a.is_verified).length;\n\n    return {\n      totalReports: vulnData.total_reports || 0,\n      totalAnalyses,\n      completedAnalyses,\n      pendingAnalyses,\n      verifiedAnalyses,\n      totalCategories: taxonomyData.total_categories || 0,\n      severityDistribution: vulnData.severity_distribution || {},\n      completionRate: totalAnalyses > 0 ? (completedAnalyses / totalAnalyses * 100).toFixed(1) : '0',\n    };\n  }, [vulnerabilityStats, analysisResults, taxonomyStats]);\n\n  const quickActions = [\n    {\n      name: '새 취약점 분석',\n      description: 'AI를 활용한 취약점 분석 시작',\n      href: '/analysis',\n      icon: BeakerIcon,\n      color: 'bg-primary-500',\n    },\n    {\n      name: '분석 결과 보기',\n      description: '완료된 분석 결과 확인',\n      href: '/results',\n      icon: ChartBarIcon,\n      color: 'bg-success-500',\n    },\n    {\n      name: '분류 체계 탐색',\n      description: 'Bugcrowd 취약점 분류 체계',\n      href: '/taxonomy',\n      icon: ShieldCheckIcon,\n      color: 'bg-secondary-500',\n    },\n    {\n      name: '리포트 생성',\n      description: '분석 결과 리포트 생성',\n      href: '/reports',\n      icon: DocumentTextIcon,\n      color: 'bg-warning-500',\n    },\n  ];\n\n  const recentAnalyses = React.useMemo(() => {\n    if (!analysisResults?.data) return [];\n    return analysisResults.data\n      .sort((a: any, b: any) => new Date(b.started_at || 0).getTime() - new Date(a.started_at || 0).getTime())\n      .slice(0, 5);\n  }, [analysisResults]);\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 헤더 */}\n      <div>\n        <h1 className=\"text-2xl font-bold text-gray-900\">대시보드</h1>\n        <p className=\"mt-1 text-sm text-gray-500\">\n          Bugcrowd AI 취약점 검증 툴의 전체 현황을 확인하세요.\n        </p>\n      </div>\n\n      {/* 통계 카드 */}\n      <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n        <div className=\"card\">\n          <div className=\"card-body\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <DocumentTextIcon className=\"h-6 w-6 text-gray-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">총 보고서</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats.totalReports}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"card-body\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <BeakerIcon className=\"h-6 w-6 text-gray-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">총 분석</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats.totalAnalyses}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"card-body\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <CheckCircleIcon className=\"h-6 w-6 text-success-500\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">완료율</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats.completionRate}%</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"card-body\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <ClockIcon className=\"h-6 w-6 text-warning-500\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">대기 중</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats.pendingAnalyses}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 빠른 작업 */}\n      <div>\n        <h2 className=\"text-lg font-medium text-gray-900 mb-4\">빠른 작업</h2>\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4\">\n          {quickActions.map((action) => (\n            <Link\n              key={action.name}\n              to={action.href}\n              className=\"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg shadow hover:shadow-md transition-shadow\"\n            >\n              <div>\n                <span className={`rounded-lg inline-flex p-3 ${action.color} text-white`}>\n                  <action.icon className=\"h-6 w-6\" />\n                </span>\n              </div>\n              <div className=\"mt-4\">\n                <h3 className=\"text-lg font-medium text-gray-900 group-hover:text-primary-600\">\n                  {action.name}\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-500\">{action.description}</p>\n              </div>\n            </Link>\n          ))}\n        </div>\n      </div>\n\n      {/* 최근 분석 결과 */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"text-lg font-medium text-gray-900\">최근 분석 결과</h3>\n          </div>\n          <div className=\"card-body\">\n            {recentAnalyses.length > 0 ? (\n              <div className=\"space-y-3\">\n                {recentAnalyses.map((analysis: any) => (\n                  <div key={analysis.id} className=\"flex items-center justify-between\">\n                    <div className=\"flex-1 min-w-0\">\n                      <p className=\"text-sm font-medium text-gray-900 truncate\">\n                        분석 #{analysis.id?.slice(-8)}\n                      </p>\n                      <p className=\"text-sm text-gray-500\">\n                        {analysis.primary_category?.name || '분류 중...'}\n                      </p>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      {analysis.predicted_severity && (\n                        <span className={`badge severity-${analysis.predicted_severity}`}>\n                          {analysis.predicted_severity}\n                        </span>\n                      )}\n                      <span className={`badge ${\n                        analysis.status === 'completed' ? 'badge-success' :\n                        analysis.status === 'in_progress' ? 'badge-warning' :\n                        analysis.status === 'failed' ? 'badge-danger' : 'badge-secondary'\n                      }`}>\n                        {analysis.status}\n                      </span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <p className=\"text-sm text-gray-500\">아직 분석 결과가 없습니다.</p>\n            )}\n          </div>\n          <div className=\"card-footer\">\n            <Link\n              to=\"/results\"\n              className=\"text-sm font-medium text-primary-600 hover:text-primary-500\"\n            >\n              모든 결과 보기 →\n            </Link>\n          </div>\n        </div>\n\n        {/* 심각도 분포 */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"text-lg font-medium text-gray-900\">심각도 분포</h3>\n          </div>\n          <div className=\"card-body\">\n            <div className=\"space-y-3\">\n              {Object.entries(stats.severityDistribution).map(([severity, count]) => (\n                <div key={severity} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <span className={`badge severity-${severity} mr-2`}>\n                      {severity}\n                    </span>\n                    <span className=\"text-sm text-gray-600 capitalize\">{severity}</span>\n                  </div>\n                  <span className=\"text-sm font-medium text-gray-900\">{count as number}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SACEC,YAAY,EACZC,UAAU,EACVC,eAAe,EACfC,gBAAgB,EAEhBC,eAAe,EACfC,SAAS,QACJ,6BAA6B;AACpC,SAASC,gBAAgB,EAAEC,WAAW,EAAEC,UAAU,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/E,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC;EACA,MAAM;IAAEC,IAAI,EAAEC;EAAmB,CAAC,GAAGf,QAAQ,CAC3C,0BAA0B,EAC1BO,gBAAgB,CAACS,aAAa,EAC9B;IAAEC,eAAe,EAAE;EAAM,CAC3B,CAAC;EAED,MAAM;IAAEH,IAAI,EAAEI;EAAgB,CAAC,GAAGlB,QAAQ,CACxC,kBAAkB,EAClB,MAAMQ,WAAW,CAACW,qBAAqB,CAAC,CAAC,EACzC;IAAEF,eAAe,EAAE;EAAM,CAC3B,CAAC;EAED,MAAM;IAAEH,IAAI,EAAEM;EAAc,CAAC,GAAGpB,QAAQ,CACtC,qBAAqB,EACrBS,UAAU,CAACY,qBACb,CAAC;;EAED;EACA,MAAMC,KAAK,GAAGxB,KAAK,CAACyB,OAAO,CAAC,MAAM;IAChC,MAAMC,QAAQ,GAAG,CAAAT,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAED,IAAI,KAAI,CAAC,CAAC;IAC/C,MAAMW,YAAY,GAAG,CAAAP,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEJ,IAAI,KAAI,EAAE;IAChD,MAAMY,YAAY,GAAG,CAAAN,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEN,IAAI,KAAI,CAAC,CAAC;IAE9C,MAAMa,aAAa,GAAGF,YAAY,CAACG,MAAM;IACzC,MAAMC,iBAAiB,GAAGJ,YAAY,CAACK,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACC,MAAM,KAAK,WAAW,CAAC,CAACJ,MAAM;IAC1F,MAAMK,eAAe,GAAGR,YAAY,CAACK,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACC,MAAM,KAAK,SAAS,CAAC,CAACJ,MAAM;IACtF,MAAMM,gBAAgB,GAAGT,YAAY,CAACK,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACI,WAAW,CAAC,CAACP,MAAM;IAE9E,OAAO;MACLQ,YAAY,EAAEZ,QAAQ,CAACa,aAAa,IAAI,CAAC;MACzCV,aAAa;MACbE,iBAAiB;MACjBI,eAAe;MACfC,gBAAgB;MAChBI,eAAe,EAAEZ,YAAY,CAACa,gBAAgB,IAAI,CAAC;MACnDC,oBAAoB,EAAEhB,QAAQ,CAACiB,qBAAqB,IAAI,CAAC,CAAC;MAC1DC,cAAc,EAAEf,aAAa,GAAG,CAAC,GAAG,CAACE,iBAAiB,GAAGF,aAAa,GAAG,GAAG,EAAEgB,OAAO,CAAC,CAAC,CAAC,GAAG;IAC7F,CAAC;EACH,CAAC,EAAE,CAAC5B,kBAAkB,EAAEG,eAAe,EAAEE,aAAa,CAAC,CAAC;EAExD,MAAMwB,YAAY,GAAG,CACnB;IACEC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,mBAAmB;IAChCC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE9C,UAAU;IAChB+C,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,cAAc;IAC3BC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE/C,YAAY;IAClBgD,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,oBAAoB;IACjCC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE7C,eAAe;IACrB8C,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAE,cAAc;IAC3BC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE5C,gBAAgB;IACtB6C,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,cAAc,GAAGpD,KAAK,CAACyB,OAAO,CAAC,MAAM;IACzC,IAAI,EAACL,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEJ,IAAI,GAAE,OAAO,EAAE;IACrC,OAAOI,eAAe,CAACJ,IAAI,CACxBqC,IAAI,CAAC,CAACpB,CAAM,EAAEqB,CAAM,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,UAAU,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAIF,IAAI,CAACtB,CAAC,CAACuB,UAAU,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CACvGC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB,CAAC,EAAE,CAACtC,eAAe,CAAC,CAAC;EAErB,oBACEP,OAAA;IAAK8C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB/C,OAAA;MAAA+C,QAAA,gBACE/C,OAAA;QAAI8C,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1DnD,OAAA;QAAG8C,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNnD,OAAA;MAAK8C,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnE/C,OAAA;QAAK8C,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB/C,OAAA;UAAK8C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB/C,OAAA;YAAK8C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC/C,OAAA;cAAK8C,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B/C,OAAA,CAACP,gBAAgB;gBAACqD,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNnD,OAAA;cAAK8C,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B/C,OAAA;gBAAA+C,QAAA,gBACE/C,OAAA;kBAAI8C,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrEnD,OAAA;kBAAI8C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEpC,KAAK,CAACc;gBAAY;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnD,OAAA;QAAK8C,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB/C,OAAA;UAAK8C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB/C,OAAA;YAAK8C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC/C,OAAA;cAAK8C,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B/C,OAAA,CAACT,UAAU;gBAACuD,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNnD,OAAA;cAAK8C,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B/C,OAAA;gBAAA+C,QAAA,gBACE/C,OAAA;kBAAI8C,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpEnD,OAAA;kBAAI8C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEpC,KAAK,CAACK;gBAAa;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnD,OAAA;QAAK8C,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB/C,OAAA;UAAK8C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB/C,OAAA;YAAK8C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC/C,OAAA;cAAK8C,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B/C,OAAA,CAACN,eAAe;gBAACoD,SAAS,EAAC;cAA0B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACNnD,OAAA;cAAK8C,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B/C,OAAA;gBAAA+C,QAAA,gBACE/C,OAAA;kBAAI8C,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnEnD,OAAA;kBAAI8C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAAEpC,KAAK,CAACoB,cAAc,EAAC,GAAC;gBAAA;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnD,OAAA;QAAK8C,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB/C,OAAA;UAAK8C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB/C,OAAA;YAAK8C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC/C,OAAA;cAAK8C,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B/C,OAAA,CAACL,SAAS;gBAACmD,SAAS,EAAC;cAA0B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACNnD,OAAA;cAAK8C,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B/C,OAAA;gBAAA+C,QAAA,gBACE/C,OAAA;kBAAI8C,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpEnD,OAAA;kBAAI8C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEpC,KAAK,CAACW;gBAAe;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnD,OAAA;MAAA+C,QAAA,gBACE/C,OAAA;QAAI8C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjEnD,OAAA;QAAK8C,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEd,YAAY,CAACmB,GAAG,CAAEC,MAAM,iBACvBrD,OAAA,CAACZ,IAAI;UAEHkE,EAAE,EAAED,MAAM,CAACjB,IAAK;UAChBU,SAAS,EAAC,2JAA2J;UAAAC,QAAA,gBAErK/C,OAAA;YAAA+C,QAAA,eACE/C,OAAA;cAAM8C,SAAS,EAAE,8BAA8BO,MAAM,CAACf,KAAK,aAAc;cAAAS,QAAA,eACvE/C,OAAA,CAACqD,MAAM,CAAChB,IAAI;gBAACS,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNnD,OAAA;YAAK8C,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB/C,OAAA;cAAI8C,SAAS,EAAC,gEAAgE;cAAAC,QAAA,EAC3EM,MAAM,CAACnB;YAAI;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACLnD,OAAA;cAAG8C,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAEM,MAAM,CAAClB;YAAW;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA,GAdDE,MAAM,CAACnB,IAAI;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAeZ,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnD,OAAA;MAAK8C,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpD/C,OAAA;QAAK8C,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB/C,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B/C,OAAA;YAAI8C,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eACNnD,OAAA;UAAK8C,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBR,cAAc,CAACtB,MAAM,GAAG,CAAC,gBACxBjB,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBR,cAAc,CAACa,GAAG,CAAEG,QAAa;cAAA,IAAAC,YAAA,EAAAC,qBAAA;cAAA,oBAChCzD,OAAA;gBAAuB8C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAClE/C,OAAA;kBAAK8C,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B/C,OAAA;oBAAG8C,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,GAAC,gBACpD,GAAAS,YAAA,GAACD,QAAQ,CAACG,EAAE,cAAAF,YAAA,uBAAXA,YAAA,CAAaX,KAAK,CAAC,CAAC,CAAC,CAAC;kBAAA;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC,eACJnD,OAAA;oBAAG8C,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EACjC,EAAAU,qBAAA,GAAAF,QAAQ,CAACI,gBAAgB,cAAAF,qBAAA,uBAAzBA,qBAAA,CAA2BvB,IAAI,KAAI;kBAAS;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNnD,OAAA;kBAAK8C,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,GACzCQ,QAAQ,CAACK,kBAAkB,iBAC1B5D,OAAA;oBAAM8C,SAAS,EAAE,kBAAkBS,QAAQ,CAACK,kBAAkB,EAAG;oBAAAb,QAAA,EAC9DQ,QAAQ,CAACK;kBAAkB;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CACP,eACDnD,OAAA;oBAAM8C,SAAS,EAAE,SACfS,QAAQ,CAAClC,MAAM,KAAK,WAAW,GAAG,eAAe,GACjDkC,QAAQ,CAAClC,MAAM,KAAK,aAAa,GAAG,eAAe,GACnDkC,QAAQ,CAAClC,MAAM,KAAK,QAAQ,GAAG,cAAc,GAAG,iBAAiB,EAChE;oBAAA0B,QAAA,EACAQ,QAAQ,CAAClC;kBAAM;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAtBEI,QAAQ,CAACG,EAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuBhB,CAAC;YAAA,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENnD,OAAA;YAAG8C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QACxD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNnD,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B/C,OAAA,CAACZ,IAAI;YACHkE,EAAE,EAAC,UAAU;YACbR,SAAS,EAAC,6DAA6D;YAAAC,QAAA,EACxE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnD,OAAA;QAAK8C,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB/C,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B/C,OAAA;YAAI8C,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACNnD,OAAA;UAAK8C,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB/C,OAAA;YAAK8C,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBc,MAAM,CAACC,OAAO,CAACnD,KAAK,CAACkB,oBAAoB,CAAC,CAACuB,GAAG,CAAC,CAAC,CAACW,QAAQ,EAAEC,KAAK,CAAC,kBAChEhE,OAAA;cAAoB8C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAC/D/C,OAAA;gBAAK8C,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC/C,OAAA;kBAAM8C,SAAS,EAAE,kBAAkBiB,QAAQ,OAAQ;kBAAAhB,QAAA,EAChDgB;gBAAQ;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACPnD,OAAA;kBAAM8C,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAEgB;gBAAQ;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNnD,OAAA;gBAAM8C,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAEiB;cAAK;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA,GAPpEY,QAAQ;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQb,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjD,EAAA,CAjQID,SAAmB;EAAA,QAEcZ,QAAQ,EAMXA,QAAQ,EAMVA,QAAQ;AAAA;AAAA4E,EAAA,GAdpChE,SAAmB;AAmQzB,eAAeA,SAAS;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}