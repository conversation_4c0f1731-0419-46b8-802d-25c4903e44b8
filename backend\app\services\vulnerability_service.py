"""
취약점 관리 서비스
"""

import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime
from app.models.vulnerability import (
    VulnerabilityReport, 
    AnalysisResult, 
    AnalysisStatus,
    VulnerabilityCategory,
    SeverityLevel
)
from app.services.taxonomy_service import TaxonomyService
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class VulnerabilityService:
    """취약점 관리 서비스"""
    
    def __init__(self, taxonomy_service: TaxonomyService):
        self.taxonomy_service = taxonomy_service
        # 실제 구현에서는 데이터베이스 연결을 사용
        self._reports: Dict[str, VulnerabilityReport] = {}
        self._analysis_results: Dict[str, AnalysisResult] = {}
    
    def create_report(self, report_data: Dict[str, Any]) -> VulnerabilityReport:
        """새 취약점 보고서 생성"""
        try:
            # ID 생성
            report_id = str(uuid.uuid4())
            
            # 보고서 객체 생성
            report = VulnerabilityReport(
                id=report_id,
                reported_date=datetime.now(),
                **report_data
            )
            
            # 저장 (실제로는 데이터베이스에 저장)
            self._reports[report_id] = report
            
            logger.info(f"새 취약점 보고서 생성: {report_id}")
            return report
            
        except Exception as e:
            logger.error(f"보고서 생성 실패: {str(e)}")
            raise
    
    def get_report(self, report_id: str) -> Optional[VulnerabilityReport]:
        """보고서 조회"""
        return self._reports.get(report_id)
    
    def get_all_reports(self) -> List[VulnerabilityReport]:
        """모든 보고서 조회"""
        return list(self._reports.values())
    
    def update_report(self, report_id: str, update_data: Dict[str, Any]) -> Optional[VulnerabilityReport]:
        """보고서 업데이트"""
        try:
            report = self._reports.get(report_id)
            if not report:
                return None
            
            # 업데이트 적용
            for key, value in update_data.items():
                if hasattr(report, key):
                    setattr(report, key, value)
            
            logger.info(f"보고서 업데이트: {report_id}")
            return report
            
        except Exception as e:
            logger.error(f"보고서 업데이트 실패: {str(e)}")
            return None
    
    def delete_report(self, report_id: str) -> bool:
        """보고서 삭제"""
        try:
            if report_id in self._reports:
                del self._reports[report_id]
                # 관련 분석 결과도 삭제
                analysis_ids_to_delete = [
                    aid for aid, result in self._analysis_results.items() 
                    if result.report_id == report_id
                ]
                for aid in analysis_ids_to_delete:
                    del self._analysis_results[aid]
                
                logger.info(f"보고서 삭제: {report_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"보고서 삭제 실패: {str(e)}")
            return False
    
    def create_analysis_result(self, report_id: str, ai_model: str) -> AnalysisResult:
        """분석 결과 생성"""
        try:
            analysis_id = str(uuid.uuid4())
            
            analysis_result = AnalysisResult(
                id=analysis_id,
                report_id=report_id,
                status=AnalysisStatus.PENDING,
                ai_model_used=ai_model,
                started_at=datetime.now()
            )
            
            self._analysis_results[analysis_id] = analysis_result
            
            logger.info(f"분석 결과 생성: {analysis_id}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"분석 결과 생성 실패: {str(e)}")
            raise
    
    def update_analysis_result(self, analysis_id: str, update_data: Dict[str, Any]) -> Optional[AnalysisResult]:
        """분석 결과 업데이트"""
        try:
            result = self._analysis_results.get(analysis_id)
            if not result:
                return None
            
            # 상태가 완료로 변경되면 완료 시간 설정
            if update_data.get('status') == AnalysisStatus.COMPLETED:
                update_data['completed_at'] = datetime.now()
            
            # 업데이트 적용
            for key, value in update_data.items():
                if hasattr(result, key):
                    setattr(result, key, value)
            
            logger.info(f"분석 결과 업데이트: {analysis_id}")
            return result
            
        except Exception as e:
            logger.error(f"분석 결과 업데이트 실패: {str(e)}")
            return None
    
    def get_analysis_result(self, analysis_id: str) -> Optional[AnalysisResult]:
        """분석 결과 조회"""
        return self._analysis_results.get(analysis_id)
    
    def get_analysis_results_by_report(self, report_id: str) -> List[AnalysisResult]:
        """보고서별 분석 결과 조회"""
        return [
            result for result in self._analysis_results.values()
            if result.report_id == report_id
        ]
    
    def get_all_analysis_results(self) -> List[AnalysisResult]:
        """모든 분석 결과 조회"""
        return list(self._analysis_results.values())
    
    def search_reports(self, query: str, filters: Optional[Dict[str, Any]] = None) -> List[VulnerabilityReport]:
        """보고서 검색"""
        results = []
        query_lower = query.lower()
        
        for report in self._reports.values():
            # 텍스트 검색
            if (query_lower in report.title.lower() or 
                query_lower in report.description.lower()):
                
                # 필터 적용
                if filters:
                    if self._apply_filters(report, filters):
                        results.append(report)
                else:
                    results.append(report)
        
        return results
    
    def _apply_filters(self, report: VulnerabilityReport, filters: Dict[str, Any]) -> bool:
        """필터 적용"""
        for key, value in filters.items():
            if key == 'severity' and report.severity != value:
                return False
            elif key == 'tags' and not any(tag in report.tags for tag in value):
                return False
            elif key == 'date_from' and report.reported_date and report.reported_date < value:
                return False
            elif key == 'date_to' and report.reported_date and report.reported_date > value:
                return False
        
        return True
    
    def get_statistics(self) -> Dict[str, Any]:
        """통계 정보 반환"""
        total_reports = len(self._reports)
        total_analyses = len(self._analysis_results)
        
        # 심각도별 통계
        severity_stats = {}
        for severity in SeverityLevel:
            count = sum(1 for report in self._reports.values() if report.severity == severity)
            severity_stats[severity.value] = count
        
        # 분석 상태별 통계
        status_stats = {}
        for status in AnalysisStatus:
            count = sum(1 for result in self._analysis_results.values() if result.status == status)
            status_stats[status.value] = count
        
        return {
            "total_reports": total_reports,
            "total_analyses": total_analyses,
            "severity_distribution": severity_stats,
            "analysis_status_distribution": status_stats,
            "reports_with_analysis": len(set(result.report_id for result in self._analysis_results.values())),
            "verified_analyses": sum(1 for result in self._analysis_results.values() if result.is_verified)
        }
