import axios, { AxiosResponse } from 'axios';

// API 기본 설정
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 요청 인터셉터
api.interceptors.request.use(
  (config) => {
    // 인증 토큰이 있다면 헤더에 추가
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 응답 인터셉터
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // 인증 오류 시 토큰 제거
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 타입 정의
export interface VulnerabilityReport {
  id?: string;
  title: string;
  description: string;
  severity?: 'critical' | 'high' | 'medium' | 'low' | 'info';
  affected_system?: string;
  vulnerability_type?: string;
  attack_vector?: string;
  impact?: string;
  steps_to_reproduce?: string;
  proof_of_concept?: string;
  reporter?: string;
  reported_date?: string;
  tags?: string[];
  attachments?: string[];
}

export interface VulnerabilityCategory {
  id: string;
  name: string;
  type: string;
  priority?: number;
  confidence_score: number;
}

export interface AnalysisResult {
  id?: string;
  report_id: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  started_at?: string;
  completed_at?: string;
  predicted_categories: VulnerabilityCategory[];
  primary_category?: VulnerabilityCategory;
  predicted_severity?: string;
  severity_confidence: number;
  ai_model_used: string;
  analysis_confidence: number;
  reasoning?: string;
  recommendations: string[];
  similar_cases: string[];
  is_verified: boolean;
  verified_by?: string;
  verified_at?: string;
  verification_notes?: string;
}

export interface AnalysisRequest {
  report: VulnerabilityReport;
  ai_model?: string;
  include_reasoning?: boolean;
  include_recommendations?: boolean;
}

export interface TaxonomyNode {
  id: string;
  name: string;
  type: 'category' | 'subcategory' | 'variant';
  priority?: number;
  children: TaxonomyNode[];
  parent_id?: string;
}

// API 함수들
export const vulnerabilityApi = {
  // 취약점 보고서 관련
  createReport: (data: VulnerabilityReport): Promise<AxiosResponse<VulnerabilityReport>> =>
    api.post('/vulnerability/reports', data),
  
  getReports: (params?: {
    skip?: number;
    limit?: number;
    search?: string;
    severity?: string;
  }): Promise<AxiosResponse<VulnerabilityReport[]>> =>
    api.get('/vulnerability/reports', { params }),
  
  getReport: (id: string): Promise<AxiosResponse<VulnerabilityReport>> =>
    api.get(`/vulnerability/reports/${id}`),
  
  updateReport: (id: string, data: Partial<VulnerabilityReport>): Promise<AxiosResponse<VulnerabilityReport>> =>
    api.put(`/vulnerability/reports/${id}`, data),
  
  deleteReport: (id: string): Promise<AxiosResponse<{ message: string }>> =>
    api.delete(`/vulnerability/reports/${id}`),
  
  getReportAnalysis: (id: string): Promise<AxiosResponse<AnalysisResult[]>> =>
    api.get(`/vulnerability/reports/${id}/analysis`),
  
  getStatistics: (): Promise<AxiosResponse<any>> =>
    api.get('/vulnerability/statistics'),
};

export const analysisApi = {
  // AI 분석 관련
  analyzeVulnerability: (data: AnalysisRequest): Promise<AxiosResponse<{
    success: boolean;
    message: string;
    analysis_id?: string;
    result?: AnalysisResult;
  }>> =>
    api.post('/analysis/analyze', data),
  
  getAnalysisResult: (id: string): Promise<AxiosResponse<AnalysisResult>> =>
    api.get(`/analysis/results/${id}`),
  
  getAllAnalysisResults: (status?: string): Promise<AxiosResponse<AnalysisResult[]>> =>
    api.get('/analysis/results', { params: status ? { status } : {} }),
  
  verifyAnalysisResult: (id: string, data: {
    verified_by: string;
    notes?: string;
  }): Promise<AxiosResponse<{ message: string }>> =>
    api.put(`/analysis/results/${id}/verify`, data),
  
  batchAnalyze: (requests: AnalysisRequest[]): Promise<AxiosResponse<{
    success: boolean;
    message: string;
    analysis_ids: string[];
  }>> =>
    api.post('/analysis/batch-analyze', requests),
};

export const reportsApi = {
  // 리포트 관련
  getTaxonomy: (): Promise<AxiosResponse<{ metadata: any; content: TaxonomyNode[] }>> =>
    api.get('/reports/taxonomy'),
  
  getCategories: (): Promise<AxiosResponse<{
    total_count: number;
    categories: TaxonomyNode[];
  }>> =>
    api.get('/reports/taxonomy/categories'),
  
  getCategoryDetail: (id: string): Promise<AxiosResponse<{
    category: TaxonomyNode;
    subcategories: TaxonomyNode[];
    subcategory_count: number;
    hierarchy: any;
  }>> =>
    api.get(`/reports/taxonomy/categories/${id}`),
  
  searchTaxonomy: (query: string, caseSensitive?: boolean): Promise<AxiosResponse<{
    query: string;
    total_count: number;
    results: TaxonomyNode[];
  }>> =>
    api.get('/reports/taxonomy/search', { 
      params: { q: query, case_sensitive: caseSensitive } 
    }),
  
  getHighPriorityVulnerabilities: (): Promise<AxiosResponse<{
    total_count: number;
    priority_groups: Record<string, TaxonomyNode[]>;
    vulnerabilities: TaxonomyNode[];
  }>> =>
    api.get('/reports/taxonomy/high-priority'),
  
  getTaxonomyStatistics: (): Promise<AxiosResponse<any>> =>
    api.get('/reports/taxonomy/statistics'),
  
  getAnalysisSummary: (params?: {
    start_date?: string;
    end_date?: string;
  }): Promise<AxiosResponse<any>> =>
    api.get('/reports/analysis-summary', { params }),
  
  exportTaxonomy: (format: string = 'json'): Promise<AxiosResponse<Blob>> =>
    api.get('/reports/export/taxonomy', { 
      params: { format },
      responseType: 'blob'
    }),
  
  exportAnalysisResults: (format: string = 'json'): Promise<AxiosResponse<Blob>> =>
    api.get('/reports/export/analysis-results', { 
      params: { format },
      responseType: 'blob'
    }),
};

export default api;
