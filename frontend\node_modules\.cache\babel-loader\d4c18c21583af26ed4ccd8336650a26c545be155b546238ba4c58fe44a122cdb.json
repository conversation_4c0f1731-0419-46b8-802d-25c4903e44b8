{"ast": null, "code": "var hashClear = require('./_hashClear'),\n  hashDelete = require('./_hashDelete'),\n  hashGet = require('./_hashGet'),\n  hashHas = require('./_hashHas'),\n  hashSet = require('./_hashSet');\n\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Hash(entries) {\n  var index = -1,\n    length = entries == null ? 0 : entries.length;\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype['delete'] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\nmodule.exports = Hash;", "map": {"version": 3, "names": ["hashClear", "require", "hashDelete", "hashGet", "hashHas", "hashSet", "Hash", "entries", "index", "length", "clear", "entry", "set", "prototype", "get", "has", "module", "exports"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/node_modules/lodash/_Hash.js"], "sourcesContent": ["var hashClear = require('./_hashClear'),\n    hashDelete = require('./_hashDelete'),\n    hashGet = require('./_hashGet'),\n    hashHas = require('./_hashHas'),\n    hashSet = require('./_hashSet');\n\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Hash(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype['delete'] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\n\nmodule.exports = Hash;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;EACnCC,UAAU,GAAGD,OAAO,CAAC,eAAe,CAAC;EACrCE,OAAO,GAAGF,OAAO,CAAC,YAAY,CAAC;EAC/BG,OAAO,GAAGH,OAAO,CAAC,YAAY,CAAC;EAC/BI,OAAO,GAAGJ,OAAO,CAAC,YAAY,CAAC;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,IAAIA,CAACC,OAAO,EAAE;EACrB,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGF,OAAO,IAAI,IAAI,GAAG,CAAC,GAAGA,OAAO,CAACE,MAAM;EAEjD,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,OAAO,EAAEF,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIE,KAAK,GAAGJ,OAAO,CAACC,KAAK,CAAC;IAC1B,IAAI,CAACI,GAAG,CAACD,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9B;AACF;;AAEA;AACAL,IAAI,CAACO,SAAS,CAACH,KAAK,GAAGV,SAAS;AAChCM,IAAI,CAACO,SAAS,CAAC,QAAQ,CAAC,GAAGX,UAAU;AACrCI,IAAI,CAACO,SAAS,CAACC,GAAG,GAAGX,OAAO;AAC5BG,IAAI,CAACO,SAAS,CAACE,GAAG,GAAGX,OAAO;AAC5BE,IAAI,CAACO,SAAS,CAACD,GAAG,GAAGP,OAAO;AAE5BW,MAAM,CAACC,OAAO,GAAGX,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}