import React, { useState } from 'react';
import { useQuery } from 'react-query';
import { usePara<PERSON>, Link } from 'react-router-dom';
import {
  ChartBarIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  EyeIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import { analysisApi, AnalysisResult } from '../services/api';
import { format } from 'date-fns';
import { ko } from 'date-fns/locale';

const AnalysisResults: React.FC = () => {
  const { analysisId } = useParams<{ analysisId: string }>();
  const [statusFilter, setStatusFilter] = useState<string>('');

  // 단일 분석 결과 조회
  const { data: singleResult, isLoading: singleLoading } = useQuery(
    ['analysis-result', analysisId],
    () => analysisApi.getAnalysisResult(analysisId!),
    { enabled: !!analysisId }
  );

  // 전체 분석 결과 조회
  const { data: allResults, isLoading: allLoading, refetch } = useQuery(
    ['analysis-results', statusFilter],
    () => analysisApi.getAllAnalysisResults(statusFilter || undefined),
    { 
      enabled: !analysisId,
      refetchInterval: 10000, // 10초마다 새로고침
    }
  );

  const isLoading = analysisId ? singleLoading : allLoading;
  const results = analysisId ? (singleResult ? [singleResult.data] : []) : (allResults?.data || []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-success-500" />;
      case 'in_progress':
        return <ClockIcon className="h-5 w-5 text-warning-500" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-danger-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "badge";
    switch (status) {
      case 'completed':
        return `${baseClasses} badge-success`;
      case 'in_progress':
        return `${baseClasses} badge-warning`;
      case 'failed':
        return `${baseClasses} badge-danger`;
      default:
        return `${baseClasses} badge-secondary`;
    }
  };

  const getSeverityBadge = (severity: string) => {
    return `badge severity-${severity}`;
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'yyyy년 MM월 dd일 HH:mm', { locale: ko });
  };

  const statusOptions = [
    { value: '', label: '전체' },
    { value: 'pending', label: '대기 중' },
    { value: 'in_progress', label: '진행 중' },
    { value: 'completed', label: '완료' },
    { value: 'failed', label: '실패' },
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="loading-spinner h-8 w-8 mx-auto mb-4" />
          <p className="text-gray-500">분석 결과를 불러오는 중...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 헤더 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <ChartBarIcon className="h-8 w-8 text-primary-600 mr-3" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {analysisId ? '분석 결과 상세' : '분석 결과'}
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              {analysisId 
                ? '선택한 분석의 상세 결과를 확인하세요.'
                : 'AI 취약점 분석 결과를 확인하고 관리하세요.'
              }
            </p>
          </div>
        </div>
        {!analysisId && (
          <div className="flex items-center space-x-3">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="form-select"
            >
              {statusOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <button
              onClick={() => refetch()}
              className="btn-outline"
            >
              <ArrowPathIcon className="h-4 w-4 mr-2" />
              새로고침
            </button>
          </div>
        )}
      </div>

      {/* 결과 목록 */}
      {results.length === 0 ? (
        <div className="text-center py-12">
          <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">분석 결과가 없습니다</h3>
          <p className="mt-1 text-sm text-gray-500">
            새로운 취약점 분석을 시작해보세요.
          </p>
          <div className="mt-6">
            <Link to="/analysis" className="btn-primary">
              새 분석 시작
            </Link>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {results.map((result: AnalysisResult) => (
            <div key={result.id} className="card">
              <div className="card-body">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      {getStatusIcon(result.status)}
                      <h3 className="text-lg font-medium text-gray-900">
                        분석 #{result.id?.slice(-8)}
                      </h3>
                      <span className={getStatusBadge(result.status)}>
                        {result.status}
                      </span>
                      {result.predicted_severity && (
                        <span className={getSeverityBadge(result.predicted_severity)}>
                          {result.predicted_severity}
                        </span>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                      <div>
                        <dt className="text-sm font-medium text-gray-500">AI 모델</dt>
                        <dd className="text-sm text-gray-900">{result.ai_model_used}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">신뢰도</dt>
                        <dd className="text-sm text-gray-900">
                          {(result.analysis_confidence * 100).toFixed(1)}%
                        </dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">시작 시간</dt>
                        <dd className="text-sm text-gray-900">
                          {result.started_at ? formatDate(result.started_at) : '-'}
                        </dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-gray-500">완료 시간</dt>
                        <dd className="text-sm text-gray-900">
                          {result.completed_at ? formatDate(result.completed_at) : '-'}
                        </dd>
                      </div>
                    </div>

                    {/* 주요 분류 */}
                    {result.primary_category && (
                      <div className="mb-4">
                        <dt className="text-sm font-medium text-gray-500 mb-1">주요 분류</dt>
                        <dd className="flex items-center space-x-2">
                          <span className="badge badge-primary">
                            {result.primary_category.name}
                          </span>
                          <span className="text-sm text-gray-500">
                            (신뢰도: {(result.primary_category.confidence_score * 100).toFixed(1)}%)
                          </span>
                        </dd>
                      </div>
                    )}

                    {/* 예측된 카테고리들 */}
                    {result.predicted_categories && result.predicted_categories.length > 0 && (
                      <div className="mb-4">
                        <dt className="text-sm font-medium text-gray-500 mb-2">예측된 카테고리</dt>
                        <dd className="flex flex-wrap gap-2">
                          {result.predicted_categories.slice(0, 3).map((category, index) => (
                            <span key={index} className="badge badge-secondary">
                              {category.name}
                            </span>
                          ))}
                          {result.predicted_categories.length > 3 && (
                            <span className="text-sm text-gray-500">
                              +{result.predicted_categories.length - 3}개 더
                            </span>
                          )}
                        </dd>
                      </div>
                    )}

                    {/* 분석 근거 (요약) */}
                    {result.reasoning && (
                      <div className="mb-4">
                        <dt className="text-sm font-medium text-gray-500 mb-1">분석 근거</dt>
                        <dd className="text-sm text-gray-700">
                          {result.reasoning.length > 150 
                            ? `${result.reasoning.substring(0, 150)}...`
                            : result.reasoning
                          }
                        </dd>
                      </div>
                    )}

                    {/* 권장사항 (요약) */}
                    {result.recommendations && result.recommendations.length > 0 && (
                      <div className="mb-4">
                        <dt className="text-sm font-medium text-gray-500 mb-1">권장사항</dt>
                        <dd className="text-sm text-gray-700">
                          <ul className="list-disc list-inside">
                            {result.recommendations.slice(0, 2).map((rec, index) => (
                              <li key={index}>{rec}</li>
                            ))}
                            {result.recommendations.length > 2 && (
                              <li className="text-gray-500">
                                +{result.recommendations.length - 2}개 더...
                              </li>
                            )}
                          </ul>
                        </dd>
                      </div>
                    )}

                    {/* 검증 상태 */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {result.is_verified ? (
                          <>
                            <CheckCircleIcon className="h-4 w-4 text-success-500" />
                            <span className="text-sm text-success-600">검증 완료</span>
                            {result.verified_by && (
                              <span className="text-sm text-gray-500">
                                by {result.verified_by}
                              </span>
                            )}
                          </>
                        ) : (
                          <>
                            <ExclamationTriangleIcon className="h-4 w-4 text-warning-500" />
                            <span className="text-sm text-warning-600">검증 대기</span>
                          </>
                        )}
                      </div>
                      
                      {!analysisId && (
                        <Link
                          to={`/results/${result.id}`}
                          className="btn-outline btn-sm"
                        >
                          <EyeIcon className="h-4 w-4 mr-1" />
                          상세 보기
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 단일 결과 상세 정보 */}
      {analysisId && results.length > 0 && (
        <div className="mt-8">
          <Link
            to="/results"
            className="text-sm text-primary-600 hover:text-primary-500"
          >
            ← 전체 결과로 돌아가기
          </Link>
        </div>
      )}
    </div>
  );
};

export default AnalysisResults;
