{"ast": null, "code": "import axios from 'axios';\n\n// API 기본 설정\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 요청 인터셉터\napi.interceptors.request.use(config => {\n  // 인증 토큰이 있다면 헤더에 추가\n  const token = localStorage.getItem('auth_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 응답 인터셉터\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // 인증 오류 시 토큰 제거\n    localStorage.removeItem('auth_token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// 타입 정의\n\n// API 함수들\nexport const vulnerabilityApi = {\n  // 취약점 보고서 관련\n  createReport: data => api.post('/vulnerability/reports', data),\n  getReports: params => api.get('/vulnerability/reports', {\n    params\n  }),\n  getReport: id => api.get(`/vulnerability/reports/${id}`),\n  updateReport: (id, data) => api.put(`/vulnerability/reports/${id}`, data),\n  deleteReport: id => api.delete(`/vulnerability/reports/${id}`),\n  getReportAnalysis: id => api.get(`/vulnerability/reports/${id}/analysis`),\n  getStatistics: () => api.get('/vulnerability/statistics')\n};\nexport const analysisApi = {\n  // AI 분석 관련\n  analyzeVulnerability: data => api.post('/analysis/analyze', data),\n  getAnalysisResult: id => api.get(`/analysis/results/${id}`),\n  getAllAnalysisResults: status => api.get('/analysis/results', {\n    params: status ? {\n      status\n    } : {}\n  }),\n  verifyAnalysisResult: (id, data) => api.put(`/analysis/results/${id}/verify`, data),\n  batchAnalyze: requests => api.post('/analysis/batch-analyze', requests)\n};\nexport const reportsApi = {\n  // 리포트 관련\n  getTaxonomy: () => api.get('/reports/taxonomy'),\n  getCategories: () => api.get('/reports/taxonomy/categories'),\n  getCategoryDetail: id => api.get(`/reports/taxonomy/categories/${id}`),\n  searchTaxonomy: (query, caseSensitive) => api.get('/reports/taxonomy/search', {\n    params: {\n      q: query,\n      case_sensitive: caseSensitive\n    }\n  }),\n  getHighPriorityVulnerabilities: () => api.get('/reports/taxonomy/high-priority'),\n  getTaxonomyStatistics: () => api.get('/reports/taxonomy/statistics'),\n  getAnalysisSummary: params => api.get('/reports/analysis-summary', {\n    params\n  }),\n  exportTaxonomy: (format = 'json') => api.get('/reports/export/taxonomy', {\n    params: {\n      format\n    },\n    responseType: 'blob'\n  }),\n  exportAnalysisResults: (format = 'json') => api.get('/reports/export/analysis-results', {\n    params: {\n      format\n    },\n    responseType: 'blob'\n  })\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "vulnerabilityApi", "createReport", "data", "post", "getReports", "params", "get", "getReport", "id", "updateReport", "put", "deleteReport", "delete", "getReportAnalysis", "getStatistics", "analysisApi", "analyzeVulnerability", "getAnalysisResult", "getAllAnalysisResults", "verifyAnalysisResult", "batchAnalyze", "requests", "reportsApi", "getTaxonomy", "getCategories", "getCategoryDetail", "searchTaxonomy", "query", "caseSensitive", "q", "case_sensitive", "getHighPriorityVulnerabilities", "getTaxonomyStatistics", "getAnalysisSummary", "exportTaxonomy", "format", "responseType", "exportAnalysisResults"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosResponse } from 'axios';\n\n// API 기본 설정\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 요청 인터셉터\napi.interceptors.request.use(\n  (config) => {\n    // 인증 토큰이 있다면 헤더에 추가\n    const token = localStorage.getItem('auth_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 응답 인터셉터\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      // 인증 오류 시 토큰 제거\n      localStorage.removeItem('auth_token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// 타입 정의\nexport interface VulnerabilityReport {\n  id?: string;\n  title: string;\n  description: string;\n  severity?: 'critical' | 'high' | 'medium' | 'low' | 'info';\n  affected_system?: string;\n  vulnerability_type?: string;\n  attack_vector?: string;\n  impact?: string;\n  steps_to_reproduce?: string;\n  proof_of_concept?: string;\n  reporter?: string;\n  reported_date?: string;\n  tags?: string[];\n  attachments?: string[];\n}\n\nexport interface VulnerabilityCategory {\n  id: string;\n  name: string;\n  type: string;\n  priority?: number;\n  confidence_score: number;\n}\n\nexport interface AnalysisResult {\n  id?: string;\n  report_id: string;\n  status: 'pending' | 'in_progress' | 'completed' | 'failed';\n  started_at?: string;\n  completed_at?: string;\n  predicted_categories: VulnerabilityCategory[];\n  primary_category?: VulnerabilityCategory;\n  predicted_severity?: string;\n  severity_confidence: number;\n  ai_model_used: string;\n  analysis_confidence: number;\n  reasoning?: string;\n  recommendations: string[];\n  similar_cases: string[];\n  is_verified: boolean;\n  verified_by?: string;\n  verified_at?: string;\n  verification_notes?: string;\n}\n\nexport interface AnalysisRequest {\n  report: VulnerabilityReport;\n  ai_model?: string;\n  include_reasoning?: boolean;\n  include_recommendations?: boolean;\n}\n\nexport interface TaxonomyNode {\n  id: string;\n  name: string;\n  type: 'category' | 'subcategory' | 'variant';\n  priority?: number;\n  children: TaxonomyNode[];\n  parent_id?: string;\n}\n\n// API 함수들\nexport const vulnerabilityApi = {\n  // 취약점 보고서 관련\n  createReport: (data: VulnerabilityReport): Promise<AxiosResponse<VulnerabilityReport>> =>\n    api.post('/vulnerability/reports', data),\n  \n  getReports: (params?: {\n    skip?: number;\n    limit?: number;\n    search?: string;\n    severity?: string;\n  }): Promise<AxiosResponse<VulnerabilityReport[]>> =>\n    api.get('/vulnerability/reports', { params }),\n  \n  getReport: (id: string): Promise<AxiosResponse<VulnerabilityReport>> =>\n    api.get(`/vulnerability/reports/${id}`),\n  \n  updateReport: (id: string, data: Partial<VulnerabilityReport>): Promise<AxiosResponse<VulnerabilityReport>> =>\n    api.put(`/vulnerability/reports/${id}`, data),\n  \n  deleteReport: (id: string): Promise<AxiosResponse<{ message: string }>> =>\n    api.delete(`/vulnerability/reports/${id}`),\n  \n  getReportAnalysis: (id: string): Promise<AxiosResponse<AnalysisResult[]>> =>\n    api.get(`/vulnerability/reports/${id}/analysis`),\n  \n  getStatistics: (): Promise<AxiosResponse<any>> =>\n    api.get('/vulnerability/statistics'),\n};\n\nexport const analysisApi = {\n  // AI 분석 관련\n  analyzeVulnerability: (data: AnalysisRequest): Promise<AxiosResponse<{\n    success: boolean;\n    message: string;\n    analysis_id?: string;\n    result?: AnalysisResult;\n  }>> =>\n    api.post('/analysis/analyze', data),\n  \n  getAnalysisResult: (id: string): Promise<AxiosResponse<AnalysisResult>> =>\n    api.get(`/analysis/results/${id}`),\n  \n  getAllAnalysisResults: (status?: string): Promise<AxiosResponse<AnalysisResult[]>> =>\n    api.get('/analysis/results', { params: status ? { status } : {} }),\n  \n  verifyAnalysisResult: (id: string, data: {\n    verified_by: string;\n    notes?: string;\n  }): Promise<AxiosResponse<{ message: string }>> =>\n    api.put(`/analysis/results/${id}/verify`, data),\n  \n  batchAnalyze: (requests: AnalysisRequest[]): Promise<AxiosResponse<{\n    success: boolean;\n    message: string;\n    analysis_ids: string[];\n  }>> =>\n    api.post('/analysis/batch-analyze', requests),\n};\n\nexport const reportsApi = {\n  // 리포트 관련\n  getTaxonomy: (): Promise<AxiosResponse<{ metadata: any; content: TaxonomyNode[] }>> =>\n    api.get('/reports/taxonomy'),\n  \n  getCategories: (): Promise<AxiosResponse<{\n    total_count: number;\n    categories: TaxonomyNode[];\n  }>> =>\n    api.get('/reports/taxonomy/categories'),\n  \n  getCategoryDetail: (id: string): Promise<AxiosResponse<{\n    category: TaxonomyNode;\n    subcategories: TaxonomyNode[];\n    subcategory_count: number;\n    hierarchy: any;\n  }>> =>\n    api.get(`/reports/taxonomy/categories/${id}`),\n  \n  searchTaxonomy: (query: string, caseSensitive?: boolean): Promise<AxiosResponse<{\n    query: string;\n    total_count: number;\n    results: TaxonomyNode[];\n  }>> =>\n    api.get('/reports/taxonomy/search', { \n      params: { q: query, case_sensitive: caseSensitive } \n    }),\n  \n  getHighPriorityVulnerabilities: (): Promise<AxiosResponse<{\n    total_count: number;\n    priority_groups: Record<string, TaxonomyNode[]>;\n    vulnerabilities: TaxonomyNode[];\n  }>> =>\n    api.get('/reports/taxonomy/high-priority'),\n  \n  getTaxonomyStatistics: (): Promise<AxiosResponse<any>> =>\n    api.get('/reports/taxonomy/statistics'),\n  \n  getAnalysisSummary: (params?: {\n    start_date?: string;\n    end_date?: string;\n  }): Promise<AxiosResponse<any>> =>\n    api.get('/reports/analysis-summary', { params }),\n  \n  exportTaxonomy: (format: string = 'json'): Promise<AxiosResponse<Blob>> =>\n    api.get('/reports/export/taxonomy', { \n      params: { format },\n      responseType: 'blob'\n    }),\n  \n  exportAnalysisResults: (format: string = 'json'): Promise<AxiosResponse<Blob>> =>\n    api.get('/reports/export/analysis-results', { \n      params: { format },\n      responseType: 'blob'\n    }),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAyB,OAAO;;AAE5C;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,8BAA8B;AAEpF,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV;EACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAChD,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,GAAG,CAACK,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAR,YAAY,CAACS,UAAU,CAAC,YAAY,CAAC;IACrCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;;AA+DA;AACA,OAAO,MAAMU,gBAAgB,GAAG;EAC9B;EACAC,YAAY,EAAGC,IAAyB,IACtCzB,GAAG,CAAC0B,IAAI,CAAC,wBAAwB,EAAED,IAAI,CAAC;EAE1CE,UAAU,EAAGC,MAKZ,IACC5B,GAAG,CAAC6B,GAAG,CAAC,wBAAwB,EAAE;IAAED;EAAO,CAAC,CAAC;EAE/CE,SAAS,EAAGC,EAAU,IACpB/B,GAAG,CAAC6B,GAAG,CAAC,0BAA0BE,EAAE,EAAE,CAAC;EAEzCC,YAAY,EAAEA,CAACD,EAAU,EAAEN,IAAkC,KAC3DzB,GAAG,CAACiC,GAAG,CAAC,0BAA0BF,EAAE,EAAE,EAAEN,IAAI,CAAC;EAE/CS,YAAY,EAAGH,EAAU,IACvB/B,GAAG,CAACmC,MAAM,CAAC,0BAA0BJ,EAAE,EAAE,CAAC;EAE5CK,iBAAiB,EAAGL,EAAU,IAC5B/B,GAAG,CAAC6B,GAAG,CAAC,0BAA0BE,EAAE,WAAW,CAAC;EAElDM,aAAa,EAAEA,CAAA,KACbrC,GAAG,CAAC6B,GAAG,CAAC,2BAA2B;AACvC,CAAC;AAED,OAAO,MAAMS,WAAW,GAAG;EACzB;EACAC,oBAAoB,EAAGd,IAAqB,IAM1CzB,GAAG,CAAC0B,IAAI,CAAC,mBAAmB,EAAED,IAAI,CAAC;EAErCe,iBAAiB,EAAGT,EAAU,IAC5B/B,GAAG,CAAC6B,GAAG,CAAC,qBAAqBE,EAAE,EAAE,CAAC;EAEpCU,qBAAqB,EAAGvB,MAAe,IACrClB,GAAG,CAAC6B,GAAG,CAAC,mBAAmB,EAAE;IAAED,MAAM,EAAEV,MAAM,GAAG;MAAEA;IAAO,CAAC,GAAG,CAAC;EAAE,CAAC,CAAC;EAEpEwB,oBAAoB,EAAEA,CAACX,EAAU,EAAEN,IAGlC,KACCzB,GAAG,CAACiC,GAAG,CAAC,qBAAqBF,EAAE,SAAS,EAAEN,IAAI,CAAC;EAEjDkB,YAAY,EAAGC,QAA2B,IAKxC5C,GAAG,CAAC0B,IAAI,CAAC,yBAAyB,EAAEkB,QAAQ;AAChD,CAAC;AAED,OAAO,MAAMC,UAAU,GAAG;EACxB;EACAC,WAAW,EAAEA,CAAA,KACX9C,GAAG,CAAC6B,GAAG,CAAC,mBAAmB,CAAC;EAE9BkB,aAAa,EAAEA,CAAA,KAIb/C,GAAG,CAAC6B,GAAG,CAAC,8BAA8B,CAAC;EAEzCmB,iBAAiB,EAAGjB,EAAU,IAM5B/B,GAAG,CAAC6B,GAAG,CAAC,gCAAgCE,EAAE,EAAE,CAAC;EAE/CkB,cAAc,EAAEA,CAACC,KAAa,EAAEC,aAAuB,KAKrDnD,GAAG,CAAC6B,GAAG,CAAC,0BAA0B,EAAE;IAClCD,MAAM,EAAE;MAAEwB,CAAC,EAAEF,KAAK;MAAEG,cAAc,EAAEF;IAAc;EACpD,CAAC,CAAC;EAEJG,8BAA8B,EAAEA,CAAA,KAK9BtD,GAAG,CAAC6B,GAAG,CAAC,iCAAiC,CAAC;EAE5C0B,qBAAqB,EAAEA,CAAA,KACrBvD,GAAG,CAAC6B,GAAG,CAAC,8BAA8B,CAAC;EAEzC2B,kBAAkB,EAAG5B,MAGpB,IACC5B,GAAG,CAAC6B,GAAG,CAAC,2BAA2B,EAAE;IAAED;EAAO,CAAC,CAAC;EAElD6B,cAAc,EAAEA,CAACC,MAAc,GAAG,MAAM,KACtC1D,GAAG,CAAC6B,GAAG,CAAC,0BAA0B,EAAE;IAClCD,MAAM,EAAE;MAAE8B;IAAO,CAAC;IAClBC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEJC,qBAAqB,EAAEA,CAACF,MAAc,GAAG,MAAM,KAC7C1D,GAAG,CAAC6B,GAAG,CAAC,kCAAkC,EAAE;IAC1CD,MAAM,EAAE;MAAE8B;IAAO,CAAC;IAClBC,YAAY,EAAE;EAChB,CAAC;AACL,CAAC;AAED,eAAe3D,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}