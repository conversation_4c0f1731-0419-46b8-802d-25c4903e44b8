import React from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from 'react-query';
import {
  ChartBarIcon,
  BeakerIcon,
  ShieldCheckIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { vulnerabilityApi, analysisApi, reportsApi } from '../services/api';

const Dashboard: React.FC = () => {
  // 통계 데이터 조회
  const { data: vulnerabilityStats } = useQuery(
    'vulnerability-statistics',
    vulnerabilityApi.getStatistics,
    { refetchInterval: 30000 }
  );

  const { data: analysisResults } = useQuery(
    'analysis-results',
    () => analysisApi.getAllAnalysisResults(),
    { refetchInterval: 30000 }
  );

  const { data: taxonomyStats } = useQuery(
    'taxonomy-statistics',
    reportsApi.getTaxonomyStatistics
  );

  // 통계 계산
  const stats = React.useMemo(() => {
    const vulnData = vulnerabilityStats?.data || {};
    const analysisData = analysisResults?.data || [];
    const taxonomyData = taxonomyStats?.data || {};

    const totalAnalyses = analysisData.length;
    const completedAnalyses = analysisData.filter((a: any) => a.status === 'completed').length;
    const pendingAnalyses = analysisData.filter((a: any) => a.status === 'pending').length;
    const verifiedAnalyses = analysisData.filter((a: any) => a.is_verified).length;

    return {
      totalReports: vulnData.total_reports || 0,
      totalAnalyses,
      completedAnalyses,
      pendingAnalyses,
      verifiedAnalyses,
      totalCategories: taxonomyData.total_categories || 0,
      severityDistribution: vulnData.severity_distribution || {},
      completionRate: totalAnalyses > 0 ? (completedAnalyses / totalAnalyses * 100).toFixed(1) : '0',
    };
  }, [vulnerabilityStats, analysisResults, taxonomyStats]);

  const quickActions = [
    {
      name: '새 취약점 분석',
      description: 'AI를 활용한 취약점 분석 시작',
      href: '/analysis',
      icon: BeakerIcon,
      color: 'bg-primary-500',
    },
    {
      name: '분석 결과 보기',
      description: '완료된 분석 결과 확인',
      href: '/results',
      icon: ChartBarIcon,
      color: 'bg-success-500',
    },
    {
      name: '분류 체계 탐색',
      description: 'Bugcrowd 취약점 분류 체계',
      href: '/taxonomy',
      icon: ShieldCheckIcon,
      color: 'bg-secondary-500',
    },
    {
      name: '리포트 생성',
      description: '분석 결과 리포트 생성',
      href: '/reports',
      icon: DocumentTextIcon,
      color: 'bg-warning-500',
    },
  ];

  const recentAnalyses = React.useMemo(() => {
    if (!analysisResults?.data) return [];
    return analysisResults.data
      .sort((a: any, b: any) => new Date(b.started_at || 0).getTime() - new Date(a.started_at || 0).getTime())
      .slice(0, 5);
  }, [analysisResults]);

  return (
    <div className="space-y-6">
      {/* 헤더 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">대시보드</h1>
        <p className="mt-1 text-sm text-gray-500">
          Bugcrowd AI 취약점 검증 툴의 전체 현황을 확인하세요.
        </p>
      </div>

      {/* 통계 카드 */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DocumentTextIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">총 보고서</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.totalReports}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BeakerIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">총 분석</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.totalAnalyses}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-6 w-6 text-success-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">완료율</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.completionRate}%</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-6 w-6 text-warning-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">대기 중</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.pendingAnalyses}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 빠른 작업 */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 mb-4">빠른 작업</h2>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {quickActions.map((action) => (
            <Link
              key={action.name}
              to={action.href}
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg shadow hover:shadow-md transition-shadow"
            >
              <div>
                <span className={`rounded-lg inline-flex p-3 ${action.color} text-white`}>
                  <action.icon className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium text-gray-900 group-hover:text-primary-600">
                  {action.name}
                </h3>
                <p className="mt-2 text-sm text-gray-500">{action.description}</p>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* 최근 분석 결과 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">최근 분석 결과</h3>
          </div>
          <div className="card-body">
            {recentAnalyses.length > 0 ? (
              <div className="space-y-3">
                {recentAnalyses.map((analysis: any) => (
                  <div key={analysis.id} className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        분석 #{analysis.id?.slice(-8)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {analysis.primary_category?.name || '분류 중...'}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {analysis.predicted_severity && (
                        <span className={`badge severity-${analysis.predicted_severity}`}>
                          {analysis.predicted_severity}
                        </span>
                      )}
                      <span className={`badge ${
                        analysis.status === 'completed' ? 'badge-success' :
                        analysis.status === 'in_progress' ? 'badge-warning' :
                        analysis.status === 'failed' ? 'badge-danger' : 'badge-secondary'
                      }`}>
                        {analysis.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">아직 분석 결과가 없습니다.</p>
            )}
          </div>
          <div className="card-footer">
            <Link
              to="/results"
              className="text-sm font-medium text-primary-600 hover:text-primary-500"
            >
              모든 결과 보기 →
            </Link>
          </div>
        </div>

        {/* 심각도 분포 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">심각도 분포</h3>
          </div>
          <div className="card-body">
            <div className="space-y-3">
              {Object.entries(stats.severityDistribution).map(([severity, count]) => (
                <div key={severity} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className={`badge severity-${severity} mr-2`}>
                      {severity}
                    </span>
                    <span className="text-sm text-gray-600 capitalize">{severity}</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">{count as number}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
