{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aidesk\\\\frontend\\\\src\\\\pages\\\\TaxonomyBrowser.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useQuery } from '@tanstack/react-query';\nimport { ShieldCheckIcon, MagnifyingGlassIcon, ChevronRightIcon, ChevronDownIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';\nimport { reportsApi } from '../services/api.ts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TaxonomyBrowser = () => {\n  _s();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [expandedNodes, setExpandedNodes] = useState(new Set());\n  const [selectedCategory, setSelectedCategory] = useState(null);\n\n  // 전체 분류 체계 조회\n  const {\n    data: taxonomy,\n    isLoading: taxonomyLoading\n  } = useQuery('taxonomy', reportsApi.getTaxonomy);\n\n  // 검색 결과 조회\n  const {\n    data: searchResults,\n    isLoading: searchLoading\n  } = useQuery(['taxonomy-search', searchQuery], () => reportsApi.searchTaxonomy(searchQuery), {\n    enabled: searchQuery.length >= 2\n  });\n\n  // 선택된 카테고리 상세 정보\n  const {\n    data: categoryDetail\n  } = useQuery(['category-detail', selectedCategory], () => reportsApi.getCategoryDetail(selectedCategory), {\n    enabled: !!selectedCategory\n  });\n\n  // 높은 우선순위 취약점들\n  const {\n    data: highPriorityVulns\n  } = useQuery('high-priority-vulnerabilities', reportsApi.getHighPriorityVulnerabilities);\n\n  // 통계 정보\n  const {\n    data: stats\n  } = useQuery('taxonomy-statistics', reportsApi.getTaxonomyStatistics);\n  const toggleNode = nodeId => {\n    const newExpanded = new Set(expandedNodes);\n    if (newExpanded.has(nodeId)) {\n      newExpanded.delete(nodeId);\n    } else {\n      newExpanded.add(nodeId);\n    }\n    setExpandedNodes(newExpanded);\n  };\n  const getPriorityBadge = priority => {\n    if (!priority) return null;\n    const colorMap = {\n      1: 'badge-danger',\n      2: 'badge bg-orange-100 text-orange-800',\n      3: 'badge-warning',\n      4: 'badge bg-blue-100 text-blue-800',\n      5: 'badge-secondary'\n    };\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `badge ${colorMap[priority] || 'badge-secondary'}`,\n      children: [\"P\", priority]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this);\n  };\n  const renderTaxonomyNode = (node, level = 0) => {\n    const isExpanded = expandedNodes.has(node.id);\n    const hasChildren = node.children && node.children.length > 0;\n    const isSelected = selectedCategory === node.id;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${level > 0 ? 'ml-6' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `flex items-center py-2 px-3 rounded-md cursor-pointer hover:bg-gray-50 ${isSelected ? 'bg-primary-50 border-l-4 border-primary-500' : ''}`,\n        onClick: () => {\n          if (hasChildren) {\n            toggleNode(node.id);\n          }\n          setSelectedCategory(node.id);\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center flex-1 min-w-0\",\n          children: [hasChildren ? isExpanded ? /*#__PURE__*/_jsxDEV(ChevronDownIcon, {\n            className: \"h-4 w-4 text-gray-400 mr-2 flex-shrink-0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(ChevronRightIcon, {\n            className: \"h-4 w-4 text-gray-400 mr-2 flex-shrink-0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-sm truncate ${node.type === 'category' ? 'font-medium text-gray-900' : node.type === 'subcategory' ? 'font-normal text-gray-700' : 'font-normal text-gray-600'}`,\n            children: node.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 ml-2\",\n          children: [getPriorityBadge(node.priority), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `badge ${node.type === 'category' ? 'badge-primary' : node.type === 'subcategory' ? 'badge-secondary' : 'badge bg-gray-100 text-gray-600'}`,\n            children: node.type\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), isExpanded && hasChildren && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-1\",\n        children: node.children.map(child => renderTaxonomyNode(child, level + 1))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 11\n      }, this)]\n    }, node.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this);\n  };\n  if (taxonomyLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner h-8 w-8 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"\\uBD84\\uB958 \\uCCB4\\uACC4\\uB97C \\uBD88\\uB7EC\\uC624\\uB294 \\uC911...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(ShieldCheckIcon, {\n          className: \"h-8 w-8 text-primary-600 mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"\\uCDE8\\uC57D\\uC810 \\uBD84\\uB958 \\uCCB4\\uACC4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-sm text-gray-500\",\n            children: \"Bugcrowd\\uC758 \\uCDE8\\uC57D\\uC810 \\uBD84\\uB958 \\uCCB4\\uACC4\\uB97C \\uD0D0\\uC0C9\\uD558\\uACE0 \\uAC80\\uC0C9\\uD558\\uC138\\uC694.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), (stats === null || stats === void 0 ? void 0 : stats.data) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: stats.data.total_categories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-500\",\n            children: \"\\uCE74\\uD14C\\uACE0\\uB9AC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: stats.data.total_subcategories\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-500\",\n            children: \"\\uD558\\uC704 \\uCE74\\uD14C\\uACE0\\uB9AC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: stats.data.total_variants\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-500\",\n            children: \"\\uBCC0\\uD615\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: stats.data.total_nodes\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-500\",\n            children: \"\\uC804\\uCCB4 \\uB178\\uB4DC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:col-span-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-medium text-gray-900\",\n                children: \"\\uBD84\\uB958 \\uCCB4\\uACC4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: \"h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"\\uBD84\\uB958 \\uAC80\\uC0C9...\",\n                  className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-primary-500 focus:border-primary-500\",\n                  value: searchQuery,\n                  onChange: e => setSearchQuery(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body max-h-96 overflow-y-auto\",\n            children: searchQuery.length >= 2 ?\n            /*#__PURE__*/\n            // 검색 결과 표시\n            _jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: searchLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center py-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"loading-spinner h-6 w-6 mx-auto\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 21\n              }, this) : (searchResults === null || searchResults === void 0 ? void 0 : searchResults.data.results.length) === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center py-4 text-gray-500\",\n                children: \"\\uAC80\\uC0C9 \\uACB0\\uACFC\\uAC00 \\uC5C6\\uC2B5\\uB2C8\\uB2E4.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 21\n              }, this) : searchResults === null || searchResults === void 0 ? void 0 : searchResults.data.results.map(node => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-3 border rounded-md hover:bg-gray-50 cursor-pointer\",\n                onClick: () => setSelectedCategory(node.id),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-medium text-gray-900\",\n                    children: node.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\"ID: \", node.id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [getPriorityBadge(node.priority), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge badge-secondary\",\n                    children: node.type\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 25\n                }, this)]\n              }, node.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this) :\n            /*#__PURE__*/\n            // 전체 분류 체계 트리 표시\n            _jsxDEV(\"div\", {\n              className: \"space-y-1\",\n              children: taxonomy === null || taxonomy === void 0 ? void 0 : taxonomy.data.content.map(node => renderTaxonomyNode(node))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [selectedCategory && (categoryDetail === null || categoryDetail === void 0 ? void 0 : categoryDetail.data) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900\",\n              children: \"\\uC0C1\\uC138 \\uC815\\uBCF4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900\",\n                children: categoryDetail.data.category.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500 mt-1\",\n                children: [\"ID: \", categoryDetail.data.category.id]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 mt-2\",\n                children: [getPriorityBadge(categoryDetail.data.category.priority), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge badge-primary\",\n                  children: categoryDetail.data.category.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this), categoryDetail.data.subcategories.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"font-medium text-gray-700 mb-2\",\n                children: [\"\\uD558\\uC704 \\uCE74\\uD14C\\uACE0\\uB9AC (\", categoryDetail.data.subcategory_count, \"\\uAC1C)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [categoryDetail.data.subcategories.slice(0, 5).map(sub => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-700\",\n                    children: sub.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 27\n                  }, this), getPriorityBadge(sub.priority)]\n                }, sub.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 25\n                }, this)), categoryDetail.data.subcategory_count > 5 && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500\",\n                  children: [\"+\", categoryDetail.data.subcategory_count - 5, \"\\uAC1C \\uB354...\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n                className: \"h-5 w-5 text-warning-500 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), \"\\uB192\\uC740 \\uC6B0\\uC120\\uC21C\\uC704 \\uCDE8\\uC57D\\uC810\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: highPriorityVulns !== null && highPriorityVulns !== void 0 && highPriorityVulns.data.vulnerabilities ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [highPriorityVulns.data.vulnerabilities.slice(0, 10).map(vuln => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-2 border rounded hover:bg-gray-50 cursor-pointer\",\n                onClick: () => setSelectedCategory(vuln.id),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-900 truncate\",\n                    children: vuln.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: vuln.type\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 23\n                }, this), getPriorityBadge(vuln.priority)]\n              }, vuln.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 21\n              }, this)), highPriorityVulns.data.total_count > 10 && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500 text-center\",\n                children: [\"+\", highPriorityVulns.data.total_count - 10, \"\\uAC1C \\uB354...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500\",\n              children: \"\\uB192\\uC740 \\uC6B0\\uC120\\uC21C\\uC704 \\uCDE8\\uC57D\\uC810 \\uC815\\uBCF4\\uB97C \\uBD88\\uB7EC\\uC624\\uB294 \\uC911...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 5\n  }, this);\n};\n_s(TaxonomyBrowser, \"vM3CA4iq2P10I/3fRUVir+9Q0J8=\", false, function () {\n  return [useQuery, useQuery, useQuery, useQuery, useQuery];\n});\n_c = TaxonomyBrowser;\nexport default TaxonomyBrowser;\nvar _c;\n$RefreshReg$(_c, \"TaxonomyBrowser\");", "map": {"version": 3, "names": ["React", "useState", "useQuery", "ShieldCheckIcon", "MagnifyingGlassIcon", "ChevronRightIcon", "ChevronDownIcon", "ExclamationTriangleIcon", "reportsApi", "jsxDEV", "_jsxDEV", "TaxonomyBrowser", "_s", "searchQuery", "setSearch<PERSON>uery", "expandedNodes", "setExpandedNodes", "Set", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "data", "taxonomy", "isLoading", "taxonomyLoading", "getTaxonomy", "searchResults", "searchLoading", "searchTaxonomy", "enabled", "length", "categoryDetail", "getCategoryDetail", "highPriorityVulns", "getHighPriorityVulnerabilities", "stats", "getTaxonomyStatistics", "toggleNode", "nodeId", "newExpanded", "has", "delete", "add", "getPriorityBadge", "priority", "colorMap", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderTaxonomyNode", "node", "level", "isExpanded", "id", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSelected", "onClick", "type", "name", "map", "child", "total_categories", "total_subcategories", "total_variants", "total_nodes", "placeholder", "value", "onChange", "e", "target", "results", "content", "category", "subcategories", "subcategory_count", "slice", "sub", "vulnerabilities", "vuln", "total_count", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/src/pages/TaxonomyBrowser.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useQuery } from '@tanstack/react-query';\nimport {\n  ShieldCheckIcon,\n  MagnifyingGlassIcon,\n  ChevronRightIcon,\n  ChevronDownIcon,\n  ExclamationTriangleIcon,\n} from '@heroicons/react/24/outline';\nimport { reportsApi, TaxonomyNode } from '../services/api.ts';\n\nconst TaxonomyBrowser: React.FC = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());\n  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);\n\n  // 전체 분류 체계 조회\n  const { data: taxonomy, isLoading: taxonomyLoading } = useQuery(\n    'taxonomy',\n    reportsApi.getTaxonomy\n  );\n\n  // 검색 결과 조회\n  const { data: searchResults, isLoading: searchLoading } = useQuery(\n    ['taxonomy-search', searchQuery],\n    () => reportsApi.searchTaxonomy(searchQuery),\n    { enabled: searchQuery.length >= 2 }\n  );\n\n  // 선택된 카테고리 상세 정보\n  const { data: categoryDetail } = useQuery(\n    ['category-detail', selectedCategory],\n    () => reportsApi.getCategoryDetail(selectedCategory!),\n    { enabled: !!selectedCategory }\n  );\n\n  // 높은 우선순위 취약점들\n  const { data: highPriorityVulns } = useQuery(\n    'high-priority-vulnerabilities',\n    reportsApi.getHighPriorityVulnerabilities\n  );\n\n  // 통계 정보\n  const { data: stats } = useQuery(\n    'taxonomy-statistics',\n    reportsApi.getTaxonomyStatistics\n  );\n\n  const toggleNode = (nodeId: string) => {\n    const newExpanded = new Set(expandedNodes);\n    if (newExpanded.has(nodeId)) {\n      newExpanded.delete(nodeId);\n    } else {\n      newExpanded.add(nodeId);\n    }\n    setExpandedNodes(newExpanded);\n  };\n\n  const getPriorityBadge = (priority: number | undefined) => {\n    if (!priority) return null;\n    \n    const colorMap: Record<number, string> = {\n      1: 'badge-danger',\n      2: 'badge bg-orange-100 text-orange-800',\n      3: 'badge-warning',\n      4: 'badge bg-blue-100 text-blue-800',\n      5: 'badge-secondary',\n    };\n\n    return (\n      <span className={`badge ${colorMap[priority] || 'badge-secondary'}`}>\n        P{priority}\n      </span>\n    );\n  };\n\n  const renderTaxonomyNode = (node: TaxonomyNode, level: number = 0) => {\n    const isExpanded = expandedNodes.has(node.id);\n    const hasChildren = node.children && node.children.length > 0;\n    const isSelected = selectedCategory === node.id;\n\n    return (\n      <div key={node.id} className={`${level > 0 ? 'ml-6' : ''}`}>\n        <div\n          className={`flex items-center py-2 px-3 rounded-md cursor-pointer hover:bg-gray-50 ${\n            isSelected ? 'bg-primary-50 border-l-4 border-primary-500' : ''\n          }`}\n          onClick={() => {\n            if (hasChildren) {\n              toggleNode(node.id);\n            }\n            setSelectedCategory(node.id);\n          }}\n        >\n          <div className=\"flex items-center flex-1 min-w-0\">\n            {hasChildren ? (\n              isExpanded ? (\n                <ChevronDownIcon className=\"h-4 w-4 text-gray-400 mr-2 flex-shrink-0\" />\n              ) : (\n                <ChevronRightIcon className=\"h-4 w-4 text-gray-400 mr-2 flex-shrink-0\" />\n              )\n            ) : (\n              <div className=\"w-6 mr-2\" />\n            )}\n            \n            <span className={`text-sm truncate ${\n              node.type === 'category' ? 'font-medium text-gray-900' :\n              node.type === 'subcategory' ? 'font-normal text-gray-700' :\n              'font-normal text-gray-600'\n            }`}>\n              {node.name}\n            </span>\n          </div>\n          \n          <div className=\"flex items-center space-x-2 ml-2\">\n            {getPriorityBadge(node.priority)}\n            <span className={`badge ${\n              node.type === 'category' ? 'badge-primary' :\n              node.type === 'subcategory' ? 'badge-secondary' :\n              'badge bg-gray-100 text-gray-600'\n            }`}>\n              {node.type}\n            </span>\n          </div>\n        </div>\n        \n        {isExpanded && hasChildren && (\n          <div className=\"mt-1\">\n            {node.children.map((child) => renderTaxonomyNode(child, level + 1))}\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  if (taxonomyLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-center\">\n          <div className=\"loading-spinner h-8 w-8 mx-auto mb-4\" />\n          <p className=\"text-gray-500\">분류 체계를 불러오는 중...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 헤더 */}\n      <div>\n        <div className=\"flex items-center\">\n          <ShieldCheckIcon className=\"h-8 w-8 text-primary-600 mr-3\" />\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">취약점 분류 체계</h1>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              Bugcrowd의 취약점 분류 체계를 탐색하고 검색하세요.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* 통계 요약 */}\n      {stats?.data && (\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <div className=\"text-2xl font-bold text-gray-900\">\n                {stats.data.total_categories}\n              </div>\n              <div className=\"text-sm text-gray-500\">카테고리</div>\n            </div>\n          </div>\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <div className=\"text-2xl font-bold text-gray-900\">\n                {stats.data.total_subcategories}\n              </div>\n              <div className=\"text-sm text-gray-500\">하위 카테고리</div>\n            </div>\n          </div>\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <div className=\"text-2xl font-bold text-gray-900\">\n                {stats.data.total_variants}\n              </div>\n              <div className=\"text-sm text-gray-500\">변형</div>\n            </div>\n          </div>\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <div className=\"text-2xl font-bold text-gray-900\">\n                {stats.data.total_nodes}\n              </div>\n              <div className=\"text-sm text-gray-500\">전체 노드</div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* 왼쪽: 분류 체계 트리 */}\n        <div className=\"lg:col-span-2\">\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <div className=\"flex items-center justify-between\">\n                <h2 className=\"text-lg font-medium text-gray-900\">분류 체계</h2>\n                <div className=\"relative\">\n                  <MagnifyingGlassIcon className=\"h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"분류 검색...\"\n                    className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-primary-500 focus:border-primary-500\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                  />\n                </div>\n              </div>\n            </div>\n            <div className=\"card-body max-h-96 overflow-y-auto\">\n              {searchQuery.length >= 2 ? (\n                // 검색 결과 표시\n                <div className=\"space-y-2\">\n                  {searchLoading ? (\n                    <div className=\"text-center py-4\">\n                      <div className=\"loading-spinner h-6 w-6 mx-auto\" />\n                    </div>\n                  ) : searchResults?.data.results.length === 0 ? (\n                    <div className=\"text-center py-4 text-gray-500\">\n                      검색 결과가 없습니다.\n                    </div>\n                  ) : (\n                    searchResults?.data.results.map((node) => (\n                      <div\n                        key={node.id}\n                        className=\"flex items-center justify-between p-3 border rounded-md hover:bg-gray-50 cursor-pointer\"\n                        onClick={() => setSelectedCategory(node.id)}\n                      >\n                        <div>\n                          <div className=\"font-medium text-gray-900\">{node.name}</div>\n                          <div className=\"text-sm text-gray-500\">ID: {node.id}</div>\n                        </div>\n                        <div className=\"flex items-center space-x-2\">\n                          {getPriorityBadge(node.priority)}\n                          <span className=\"badge badge-secondary\">{node.type}</span>\n                        </div>\n                      </div>\n                    ))\n                  )}\n                </div>\n              ) : (\n                // 전체 분류 체계 트리 표시\n                <div className=\"space-y-1\">\n                  {taxonomy?.data.content.map((node) => renderTaxonomyNode(node))}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* 오른쪽: 상세 정보 및 높은 우선순위 취약점 */}\n        <div className=\"space-y-6\">\n          {/* 선택된 카테고리 상세 정보 */}\n          {selectedCategory && categoryDetail?.data && (\n            <div className=\"card\">\n              <div className=\"card-header\">\n                <h3 className=\"text-lg font-medium text-gray-900\">상세 정보</h3>\n              </div>\n              <div className=\"card-body space-y-4\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">\n                    {categoryDetail.data.category.name}\n                  </h4>\n                  <p className=\"text-sm text-gray-500 mt-1\">\n                    ID: {categoryDetail.data.category.id}\n                  </p>\n                  <div className=\"flex items-center space-x-2 mt-2\">\n                    {getPriorityBadge(categoryDetail.data.category.priority)}\n                    <span className=\"badge badge-primary\">\n                      {categoryDetail.data.category.type}\n                    </span>\n                  </div>\n                </div>\n\n                {categoryDetail.data.subcategories.length > 0 && (\n                  <div>\n                    <h5 className=\"font-medium text-gray-700 mb-2\">\n                      하위 카테고리 ({categoryDetail.data.subcategory_count}개)\n                    </h5>\n                    <div className=\"space-y-2\">\n                      {categoryDetail.data.subcategories.slice(0, 5).map((sub) => (\n                        <div\n                          key={sub.id}\n                          className=\"flex items-center justify-between text-sm\"\n                        >\n                          <span className=\"text-gray-700\">{sub.name}</span>\n                          {getPriorityBadge(sub.priority)}\n                        </div>\n                      ))}\n                      {categoryDetail.data.subcategory_count > 5 && (\n                        <p className=\"text-sm text-gray-500\">\n                          +{categoryDetail.data.subcategory_count - 5}개 더...\n                        </p>\n                      )}\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n\n          {/* 높은 우선순위 취약점 */}\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"text-lg font-medium text-gray-900 flex items-center\">\n                <ExclamationTriangleIcon className=\"h-5 w-5 text-warning-500 mr-2\" />\n                높은 우선순위 취약점\n              </h3>\n            </div>\n            <div className=\"card-body\">\n              {highPriorityVulns?.data.vulnerabilities ? (\n                <div className=\"space-y-3\">\n                  {highPriorityVulns.data.vulnerabilities.slice(0, 10).map((vuln) => (\n                    <div\n                      key={vuln.id}\n                      className=\"flex items-center justify-between p-2 border rounded hover:bg-gray-50 cursor-pointer\"\n                      onClick={() => setSelectedCategory(vuln.id)}\n                    >\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"text-sm font-medium text-gray-900 truncate\">\n                          {vuln.name}\n                        </div>\n                        <div className=\"text-xs text-gray-500\">{vuln.type}</div>\n                      </div>\n                      {getPriorityBadge(vuln.priority)}\n                    </div>\n                  ))}\n                  {highPriorityVulns.data.total_count > 10 && (\n                    <p className=\"text-sm text-gray-500 text-center\">\n                      +{highPriorityVulns.data.total_count - 10}개 더...\n                    </p>\n                  )}\n                </div>\n              ) : (\n                <p className=\"text-sm text-gray-500\">\n                  높은 우선순위 취약점 정보를 불러오는 중...\n                </p>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TaxonomyBrowser;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SACEC,eAAe,EACfC,mBAAmB,EACnBC,gBAAgB,EAChBC,eAAe,EACfC,uBAAuB,QAClB,6BAA6B;AACpC,SAASC,UAAU,QAAsB,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACc,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAc,IAAIgB,GAAG,CAAC,CAAC,CAAC;EAC1E,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;;EAE7E;EACA,MAAM;IAAEmB,IAAI,EAAEC,QAAQ;IAAEC,SAAS,EAAEC;EAAgB,CAAC,GAAGrB,QAAQ,CAC7D,UAAU,EACVM,UAAU,CAACgB,WACb,CAAC;;EAED;EACA,MAAM;IAAEJ,IAAI,EAAEK,aAAa;IAAEH,SAAS,EAAEI;EAAc,CAAC,GAAGxB,QAAQ,CAChE,CAAC,iBAAiB,EAAEW,WAAW,CAAC,EAChC,MAAML,UAAU,CAACmB,cAAc,CAACd,WAAW,CAAC,EAC5C;IAAEe,OAAO,EAAEf,WAAW,CAACgB,MAAM,IAAI;EAAE,CACrC,CAAC;;EAED;EACA,MAAM;IAAET,IAAI,EAAEU;EAAe,CAAC,GAAG5B,QAAQ,CACvC,CAAC,iBAAiB,EAAEgB,gBAAgB,CAAC,EACrC,MAAMV,UAAU,CAACuB,iBAAiB,CAACb,gBAAiB,CAAC,EACrD;IAAEU,OAAO,EAAE,CAAC,CAACV;EAAiB,CAChC,CAAC;;EAED;EACA,MAAM;IAAEE,IAAI,EAAEY;EAAkB,CAAC,GAAG9B,QAAQ,CAC1C,+BAA+B,EAC/BM,UAAU,CAACyB,8BACb,CAAC;;EAED;EACA,MAAM;IAAEb,IAAI,EAAEc;EAAM,CAAC,GAAGhC,QAAQ,CAC9B,qBAAqB,EACrBM,UAAU,CAAC2B,qBACb,CAAC;EAED,MAAMC,UAAU,GAAIC,MAAc,IAAK;IACrC,MAAMC,WAAW,GAAG,IAAIrB,GAAG,CAACF,aAAa,CAAC;IAC1C,IAAIuB,WAAW,CAACC,GAAG,CAACF,MAAM,CAAC,EAAE;MAC3BC,WAAW,CAACE,MAAM,CAACH,MAAM,CAAC;IAC5B,CAAC,MAAM;MACLC,WAAW,CAACG,GAAG,CAACJ,MAAM,CAAC;IACzB;IACArB,gBAAgB,CAACsB,WAAW,CAAC;EAC/B,CAAC;EAED,MAAMI,gBAAgB,GAAIC,QAA4B,IAAK;IACzD,IAAI,CAACA,QAAQ,EAAE,OAAO,IAAI;IAE1B,MAAMC,QAAgC,GAAG;MACvC,CAAC,EAAE,cAAc;MACjB,CAAC,EAAE,qCAAqC;MACxC,CAAC,EAAE,eAAe;MAClB,CAAC,EAAE,iCAAiC;MACpC,CAAC,EAAE;IACL,CAAC;IAED,oBACElC,OAAA;MAAMmC,SAAS,EAAE,SAASD,QAAQ,CAACD,QAAQ,CAAC,IAAI,iBAAiB,EAAG;MAAAG,QAAA,GAAC,GAClE,EAACH,QAAQ;IAAA;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEX,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAACC,IAAkB,EAAEC,KAAa,GAAG,CAAC,KAAK;IACpE,MAAMC,UAAU,GAAGvC,aAAa,CAACwB,GAAG,CAACa,IAAI,CAACG,EAAE,CAAC;IAC7C,MAAMC,WAAW,GAAGJ,IAAI,CAACN,QAAQ,IAAIM,IAAI,CAACN,QAAQ,CAACjB,MAAM,GAAG,CAAC;IAC7D,MAAM4B,UAAU,GAAGvC,gBAAgB,KAAKkC,IAAI,CAACG,EAAE;IAE/C,oBACE7C,OAAA;MAAmBmC,SAAS,EAAE,GAAGQ,KAAK,GAAG,CAAC,GAAG,MAAM,GAAG,EAAE,EAAG;MAAAP,QAAA,gBACzDpC,OAAA;QACEmC,SAAS,EAAE,0EACTY,UAAU,GAAG,6CAA6C,GAAG,EAAE,EAC9D;QACHC,OAAO,EAAEA,CAAA,KAAM;UACb,IAAIF,WAAW,EAAE;YACfpB,UAAU,CAACgB,IAAI,CAACG,EAAE,CAAC;UACrB;UACApC,mBAAmB,CAACiC,IAAI,CAACG,EAAE,CAAC;QAC9B,CAAE;QAAAT,QAAA,gBAEFpC,OAAA;UAAKmC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,GAC9CU,WAAW,GACVF,UAAU,gBACR5C,OAAA,CAACJ,eAAe;YAACuC,SAAS,EAAC;UAA0C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAExExC,OAAA,CAACL,gBAAgB;YAACwC,SAAS,EAAC;UAA0C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CACzE,gBAEDxC,OAAA;YAAKmC,SAAS,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAC5B,eAEDxC,OAAA;YAAMmC,SAAS,EAAE,oBACfO,IAAI,CAACO,IAAI,KAAK,UAAU,GAAG,2BAA2B,GACtDP,IAAI,CAACO,IAAI,KAAK,aAAa,GAAG,2BAA2B,GACzD,2BAA2B,EAC1B;YAAAb,QAAA,EACAM,IAAI,CAACQ;UAAI;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENxC,OAAA;UAAKmC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,GAC9CJ,gBAAgB,CAACU,IAAI,CAACT,QAAQ,CAAC,eAChCjC,OAAA;YAAMmC,SAAS,EAAE,SACfO,IAAI,CAACO,IAAI,KAAK,UAAU,GAAG,eAAe,GAC1CP,IAAI,CAACO,IAAI,KAAK,aAAa,GAAG,iBAAiB,GAC/C,iCAAiC,EAChC;YAAAb,QAAA,EACAM,IAAI,CAACO;UAAI;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELI,UAAU,IAAIE,WAAW,iBACxB9C,OAAA;QAAKmC,SAAS,EAAC,MAAM;QAAAC,QAAA,EAClBM,IAAI,CAACN,QAAQ,CAACe,GAAG,CAAEC,KAAK,IAAKX,kBAAkB,CAACW,KAAK,EAAET,KAAK,GAAG,CAAC,CAAC;MAAC;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CACN;IAAA,GAhDOE,IAAI,CAACG,EAAE;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAiDZ,CAAC;EAEV,CAAC;EAED,IAAI3B,eAAe,EAAE;IACnB,oBACEb,OAAA;MAAKmC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDpC,OAAA;QAAKmC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpC,OAAA;UAAKmC,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxDxC,OAAA;UAAGmC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACExC,OAAA;IAAKmC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBpC,OAAA;MAAAoC,QAAA,eACEpC,OAAA;QAAKmC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCpC,OAAA,CAACP,eAAe;UAAC0C,SAAS,EAAC;QAA+B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DxC,OAAA;UAAAoC,QAAA,gBACEpC,OAAA;YAAImC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/DxC,OAAA;YAAGmC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL,CAAAhB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEd,IAAI,kBACVV,OAAA;MAAKmC,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnEpC,OAAA;QAAKmC,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBpC,OAAA;UAAKmC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBpC,OAAA;YAAKmC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAC9CZ,KAAK,CAACd,IAAI,CAAC2C;UAAgB;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNxC,OAAA;QAAKmC,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBpC,OAAA;UAAKmC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBpC,OAAA;YAAKmC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAC9CZ,KAAK,CAACd,IAAI,CAAC4C;UAAmB;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNxC,OAAA;QAAKmC,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBpC,OAAA;UAAKmC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBpC,OAAA;YAAKmC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAC9CZ,KAAK,CAACd,IAAI,CAAC6C;UAAc;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNxC,OAAA;QAAKmC,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBpC,OAAA;UAAKmC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBpC,OAAA;YAAKmC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAC9CZ,KAAK,CAACd,IAAI,CAAC8C;UAAW;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDxC,OAAA;MAAKmC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDpC,OAAA;QAAKmC,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BpC,OAAA;UAAKmC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBpC,OAAA;YAAKmC,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BpC,OAAA;cAAKmC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDpC,OAAA;gBAAImC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5DxC,OAAA;gBAAKmC,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBpC,OAAA,CAACN,mBAAmB;kBAACyC,SAAS,EAAC;gBAA0E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5GxC,OAAA;kBACEiD,IAAI,EAAC,MAAM;kBACXQ,WAAW,EAAC,8BAAU;kBACtBtB,SAAS,EAAC,2GAA2G;kBACrHuB,KAAK,EAAEvD,WAAY;kBACnBwD,QAAQ,EAAGC,CAAC,IAAKxD,cAAc,CAACwD,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAChDjC,WAAW,CAACgB,MAAM,IAAI,CAAC;YAAA;YACtB;YACAnB,OAAA;cAAKmC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBpB,aAAa,gBACZhB,OAAA;gBAAKmC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BpC,OAAA;kBAAKmC,SAAS,EAAC;gBAAiC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,GACJ,CAAAzB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEL,IAAI,CAACoD,OAAO,CAAC3C,MAAM,MAAK,CAAC,gBAC1CnB,OAAA;gBAAKmC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAEhD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GAENzB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEL,IAAI,CAACoD,OAAO,CAACX,GAAG,CAAET,IAAI,iBACnC1C,OAAA;gBAEEmC,SAAS,EAAC,yFAAyF;gBACnGa,OAAO,EAAEA,CAAA,KAAMvC,mBAAmB,CAACiC,IAAI,CAACG,EAAE,CAAE;gBAAAT,QAAA,gBAE5CpC,OAAA;kBAAAoC,QAAA,gBACEpC,OAAA;oBAAKmC,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAEM,IAAI,CAACQ;kBAAI;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5DxC,OAAA;oBAAKmC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,MAAI,EAACM,IAAI,CAACG,EAAE;kBAAA;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eACNxC,OAAA;kBAAKmC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,GACzCJ,gBAAgB,CAACU,IAAI,CAACT,QAAQ,CAAC,eAChCjC,OAAA;oBAAMmC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEM,IAAI,CAACO;kBAAI;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC;cAAA,GAXDE,IAAI,CAACG,EAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYT,CACN;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;YAAA;YAEN;YACAxC,OAAA;cAAKmC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBzB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAED,IAAI,CAACqD,OAAO,CAACZ,GAAG,CAAET,IAAI,IAAKD,kBAAkB,CAACC,IAAI,CAAC;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxC,OAAA;QAAKmC,SAAS,EAAC,WAAW;QAAAC,QAAA,GAEvB5B,gBAAgB,KAAIY,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEV,IAAI,kBACvCV,OAAA;UAAKmC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBpC,OAAA;YAAKmC,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BpC,OAAA;cAAImC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCpC,OAAA;cAAAoC,QAAA,gBACEpC,OAAA;gBAAImC,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EACtChB,cAAc,CAACV,IAAI,CAACsD,QAAQ,CAACd;cAAI;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACLxC,OAAA;gBAAGmC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GAAC,MACpC,EAAChB,cAAc,CAACV,IAAI,CAACsD,QAAQ,CAACnB,EAAE;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACJxC,OAAA;gBAAKmC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAC9CJ,gBAAgB,CAACZ,cAAc,CAACV,IAAI,CAACsD,QAAQ,CAAC/B,QAAQ,CAAC,eACxDjC,OAAA;kBAAMmC,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAClChB,cAAc,CAACV,IAAI,CAACsD,QAAQ,CAACf;gBAAI;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELpB,cAAc,CAACV,IAAI,CAACuD,aAAa,CAAC9C,MAAM,GAAG,CAAC,iBAC3CnB,OAAA;cAAAoC,QAAA,gBACEpC,OAAA;gBAAImC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,GAAC,yCACpC,EAAChB,cAAc,CAACV,IAAI,CAACwD,iBAAiB,EAAC,SAClD;cAAA;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxC,OAAA;gBAAKmC,SAAS,EAAC,WAAW;gBAAAC,QAAA,GACvBhB,cAAc,CAACV,IAAI,CAACuD,aAAa,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChB,GAAG,CAAEiB,GAAG,iBACrDpE,OAAA;kBAEEmC,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,gBAErDpC,OAAA;oBAAMmC,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAEgC,GAAG,CAAClB;kBAAI;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAChDR,gBAAgB,CAACoC,GAAG,CAACnC,QAAQ,CAAC;gBAAA,GAJ1BmC,GAAG,CAACvB,EAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKR,CACN,CAAC,EACDpB,cAAc,CAACV,IAAI,CAACwD,iBAAiB,GAAG,CAAC,iBACxClE,OAAA;kBAAGmC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,GAClC,EAAChB,cAAc,CAACV,IAAI,CAACwD,iBAAiB,GAAG,CAAC,EAAC,kBAC9C;gBAAA;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDxC,OAAA;UAAKmC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBpC,OAAA;YAAKmC,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BpC,OAAA;cAAImC,SAAS,EAAC,qDAAqD;cAAAC,QAAA,gBACjEpC,OAAA,CAACH,uBAAuB;gBAACsC,SAAS,EAAC;cAA+B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4DAEvE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACNxC,OAAA;YAAKmC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBd,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEZ,IAAI,CAAC2D,eAAe,gBACtCrE,OAAA;cAAKmC,SAAS,EAAC,WAAW;cAAAC,QAAA,GACvBd,iBAAiB,CAACZ,IAAI,CAAC2D,eAAe,CAACF,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAChB,GAAG,CAAEmB,IAAI,iBAC5DtE,OAAA;gBAEEmC,SAAS,EAAC,sFAAsF;gBAChGa,OAAO,EAAEA,CAAA,KAAMvC,mBAAmB,CAAC6D,IAAI,CAACzB,EAAE,CAAE;gBAAAT,QAAA,gBAE5CpC,OAAA;kBAAKmC,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BpC,OAAA;oBAAKmC,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,EACxDkC,IAAI,CAACpB;kBAAI;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACNxC,OAAA;oBAAKmC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEkC,IAAI,CAACrB;kBAAI;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,EACLR,gBAAgB,CAACsC,IAAI,CAACrC,QAAQ,CAAC;cAAA,GAV3BqC,IAAI,CAACzB,EAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWT,CACN,CAAC,EACDlB,iBAAiB,CAACZ,IAAI,CAAC6D,WAAW,GAAG,EAAE,iBACtCvE,OAAA;gBAAGmC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAAC,GAC9C,EAACd,iBAAiB,CAACZ,IAAI,CAAC6D,WAAW,GAAG,EAAE,EAAC,kBAC5C;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,gBAENxC,OAAA;cAAGmC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UACJ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtC,EAAA,CAtVID,eAAyB;EAAA,QAM0BT,QAAQ,EAMLA,QAAQ,EAOjCA,QAAQ,EAOLA,QAAQ,EAMpBA,QAAQ;AAAA;AAAAgF,EAAA,GAhC5BvE,eAAyB;AAwV/B,eAAeA,eAAe;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}