{"ast": null, "code": "var getMapData = require('./_getMapData');\n\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction mapCacheGet(key) {\n  return getMapData(this, key).get(key);\n}\nmodule.exports = mapCacheGet;", "map": {"version": 3, "names": ["getMapData", "require", "mapCacheGet", "key", "get", "module", "exports"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/node_modules/lodash/_mapCacheGet.js"], "sourcesContent": ["var getMapData = require('./_getMapData');\n\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction mapCacheGet(key) {\n  return getMapData(this, key).get(key);\n}\n\nmodule.exports = mapCacheGet;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,GAAG,EAAE;EACxB,OAAOH,UAAU,CAAC,IAAI,EAAEG,GAAG,CAAC,CAACC,GAAG,CAACD,GAAG,CAAC;AACvC;AAEAE,MAAM,CAACC,OAAO,GAAGJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}