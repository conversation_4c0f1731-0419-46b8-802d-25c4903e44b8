import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import {
  CogIcon,
  BeakerIcon,
  ShieldCheckIcon,
  BellIcon,
  UserIcon,
  KeyIcon,
} from '@heroicons/react/24/outline';

interface SettingsForm {
  // AI 설정
  defaultAiModel: string;
  aiTemperature: number;
  maxTokens: number;
  confidenceThreshold: number;
  
  // 분석 설정
  autoVerification: boolean;
  verificationThreshold: number;
  includeReasoningByDefault: boolean;
  includeRecommendationsByDefault: boolean;
  
  // 알림 설정
  emailNotifications: boolean;
  analysisCompleteNotification: boolean;
  verificationRequiredNotification: boolean;
  
  // 사용자 설정
  userName: string;
  userEmail: string;
  
  // API 설정
  openaiApiKey: string;
  ollamaHost: string;
  customApiUrl: string;
  customApiKey: string;
}

const Settings: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'ai' | 'analysis' | 'notifications' | 'user' | 'api'>('ai');
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<SettingsForm>({
    defaultValues: {
      defaultAiModel: 'gpt-4',
      aiTemperature: 0.3,
      maxTokens: 2000,
      confidenceThreshold: 0.5,
      autoVerification: true,
      verificationThreshold: 0.7,
      includeReasoningByDefault: true,
      includeRecommendationsByDefault: true,
      emailNotifications: true,
      analysisCompleteNotification: true,
      verificationRequiredNotification: true,
      userName: 'AI Assistant',
      userEmail: '<EMAIL>',
      openaiApiKey: '',
      ollamaHost: 'http://localhost:11434',
      customApiUrl: '',
      customApiKey: '',
    },
  });

  const onSubmit = async (data: SettingsForm) => {
    try {
      // 실제 구현에서는 API 호출
      console.log('Settings saved:', data);
      toast.success('설정이 저장되었습니다!');
    } catch (error) {
      toast.error('설정 저장에 실패했습니다.');
    }
  };

  const tabs = [
    { id: 'ai', name: 'AI 모델', icon: BeakerIcon },
    { id: 'analysis', name: '분석 설정', icon: ShieldCheckIcon },
    { id: 'notifications', name: '알림', icon: BellIcon },
    { id: 'user', name: '사용자', icon: UserIcon },
    { id: 'api', name: 'API 키', icon: KeyIcon },
  ];

  const aiModelOptions = [
    { value: 'gpt-4', label: 'GPT-4' },
    { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
    { value: 'ollama', label: 'Ollama (로컬)' },
    { value: 'custom', label: '커스텀 모델' },
  ];

  return (
    <div className="space-y-6">
      {/* 헤더 */}
      <div>
        <div className="flex items-center">
          <CogIcon className="h-8 w-8 text-primary-600 mr-3" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">설정</h1>
            <p className="mt-1 text-sm text-gray-500">
              AI 분석 엔진과 시스템 설정을 관리하세요.
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 탭 네비게이션 */}
        <div className="lg:col-span-1">
          <nav className="space-y-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                  activeTab === tab.id
                    ? 'bg-primary-100 text-primary-700 border-primary-500'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <tab.icon className="h-5 w-5 mr-3" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* 설정 콘텐츠 */}
        <div className="lg:col-span-3">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* AI 모델 설정 */}
            {activeTab === 'ai' && (
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-medium text-gray-900">AI 모델 설정</h3>
                </div>
                <div className="card-body space-y-6">
                  <div>
                    <label className="form-label">기본 AI 모델</label>
                    <select className="form-select" {...register('defaultAiModel')}>
                      {aiModelOptions.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    <p className="mt-1 text-sm text-gray-500">
                      새로운 분석에서 기본으로 사용할 AI 모델을 선택하세요.
                    </p>
                  </div>

                  <div>
                    <label className="form-label">
                      Temperature: {watch('aiTemperature')}
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      className="w-full"
                      {...register('aiTemperature', { valueAsNumber: true })}
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      낮을수록 일관된 결과, 높을수록 창의적인 결과를 생성합니다.
                    </p>
                  </div>

                  <div>
                    <label className="form-label">최대 토큰 수</label>
                    <input
                      type="number"
                      min="500"
                      max="4000"
                      step="100"
                      className="form-input"
                      {...register('maxTokens', { valueAsNumber: true })}
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      AI 응답의 최대 길이를 제한합니다.
                    </p>
                  </div>

                  <div>
                    <label className="form-label">
                      신뢰도 임계값: {watch('confidenceThreshold')}
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      className="w-full"
                      {...register('confidenceThreshold', { valueAsNumber: true })}
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      이 값 이하의 신뢰도를 가진 분석은 추가 검토가 필요합니다.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* 분석 설정 */}
            {activeTab === 'analysis' && (
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-medium text-gray-900">분석 설정</h3>
                </div>
                <div className="card-body space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="form-label">자동 검증</label>
                      <p className="text-sm text-gray-500">
                        높은 신뢰도의 분석 결과를 자동으로 검증합니다.
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      {...register('autoVerification')}
                    />
                  </div>

                  <div>
                    <label className="form-label">
                      자동 검증 임계값: {watch('verificationThreshold')}
                    </label>
                    <input
                      type="range"
                      min="0.5"
                      max="1"
                      step="0.05"
                      className="w-full"
                      {...register('verificationThreshold', { valueAsNumber: true })}
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      이 값 이상의 검증 점수를 가진 분석을 자동 검증합니다.
                    </p>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="form-label">기본 분석 근거 포함</label>
                      <p className="text-sm text-gray-500">
                        새 분석에서 기본적으로 분석 근거를 포함합니다.
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      {...register('includeReasoningByDefault')}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="form-label">기본 권장사항 포함</label>
                      <p className="text-sm text-gray-500">
                        새 분석에서 기본적으로 보안 권장사항을 포함합니다.
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      {...register('includeRecommendationsByDefault')}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* 알림 설정 */}
            {activeTab === 'notifications' && (
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-medium text-gray-900">알림 설정</h3>
                </div>
                <div className="card-body space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="form-label">이메일 알림</label>
                      <p className="text-sm text-gray-500">
                        중요한 이벤트에 대한 이메일 알림을 받습니다.
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      {...register('emailNotifications')}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="form-label">분석 완료 알림</label>
                      <p className="text-sm text-gray-500">
                        AI 분석이 완료되면 알림을 받습니다.
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      {...register('analysisCompleteNotification')}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="form-label">검증 필요 알림</label>
                      <p className="text-sm text-gray-500">
                        수동 검증이 필요한 분석이 있을 때 알림을 받습니다.
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      {...register('verificationRequiredNotification')}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* 사용자 설정 */}
            {activeTab === 'user' && (
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-medium text-gray-900">사용자 정보</h3>
                </div>
                <div className="card-body space-y-6">
                  <div>
                    <label className="form-label">사용자 이름</label>
                    <input
                      type="text"
                      className="form-input"
                      {...register('userName', { required: '사용자 이름은 필수입니다' })}
                    />
                    {errors.userName && (
                      <p className="form-error">{errors.userName.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="form-label">이메일</label>
                    <input
                      type="email"
                      className="form-input"
                      {...register('userEmail', { 
                        required: '이메일은 필수입니다',
                        pattern: {
                          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                          message: '유효한 이메일 주소를 입력하세요'
                        }
                      })}
                    />
                    {errors.userEmail && (
                      <p className="form-error">{errors.userEmail.message}</p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* API 키 설정 */}
            {activeTab === 'api' && (
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-medium text-gray-900">API 키 설정</h3>
                </div>
                <div className="card-body space-y-6">
                  <div>
                    <label className="form-label">OpenAI API 키</label>
                    <input
                      type="password"
                      className="form-input"
                      placeholder="sk-..."
                      {...register('openaiApiKey')}
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      GPT 모델을 사용하기 위한 OpenAI API 키입니다.
                    </p>
                  </div>

                  <div>
                    <label className="form-label">Ollama 호스트</label>
                    <input
                      type="url"
                      className="form-input"
                      placeholder="http://localhost:11434"
                      {...register('ollamaHost')}
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      로컬 Ollama 서버의 URL입니다.
                    </p>
                  </div>

                  <div>
                    <label className="form-label">커스텀 API URL</label>
                    <input
                      type="url"
                      className="form-input"
                      placeholder="https://api.example.com/v1/chat"
                      {...register('customApiUrl')}
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      커스텀 AI API의 엔드포인트 URL입니다.
                    </p>
                  </div>

                  <div>
                    <label className="form-label">커스텀 API 키</label>
                    <input
                      type="password"
                      className="form-input"
                      placeholder="API 키를 입력하세요"
                      {...register('customApiKey')}
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      커스텀 AI API 인증을 위한 키입니다.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* 저장 버튼 */}
            <div className="flex justify-end">
              <button type="submit" className="btn-primary">
                설정 저장
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Settings;
