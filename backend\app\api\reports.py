"""
리포트 생성 관련 API 엔드포인트
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Response
from fastapi.responses import JSONResponse
from app.services.vulnerability_service import VulnerabilityService
from app.services.taxonomy_service import TaxonomyService
from app.models.taxonomy import TaxonomyNode
from app.utils.logger import setup_logger
import json
from datetime import datetime

logger = setup_logger(__name__)
router = APIRouter()

# 의존성 주입을 위한 서비스 인스턴스
def get_taxonomy_service() -> TaxonomyService:
    return TaxonomyService()

def get_vulnerability_service(
    taxonomy_service: TaxonomyService = Depends(get_taxonomy_service)
) -> VulnerabilityService:
    return VulnerabilityService(taxonomy_service)

@router.get("/taxonomy")
async def get_taxonomy_report(
    taxonomy_service: TaxonomyService = Depends(get_taxonomy_service)
):
    """취약점 분류 체계 전체 조회"""
    try:
        taxonomy = taxonomy_service.get_taxonomy()
        if not taxonomy:
            raise HTTPException(status_code=500, detail="분류 체계를 로드할 수 없습니다")
        
        return taxonomy
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"분류 체계 조회 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.get("/taxonomy/categories")
async def get_categories_report(
    taxonomy_service: TaxonomyService = Depends(get_taxonomy_service)
):
    """모든 카테고리 조회"""
    try:
        categories = taxonomy_service.get_all_categories()
        return {
            "total_count": len(categories),
            "categories": categories
        }
        
    except Exception as e:
        logger.error(f"카테고리 조회 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.get("/taxonomy/categories/{category_id}")
async def get_category_detail_report(
    category_id: str,
    taxonomy_service: TaxonomyService = Depends(get_taxonomy_service)
):
    """특정 카테고리 상세 정보"""
    try:
        category = taxonomy_service.get_category_by_id(category_id)
        if not category:
            raise HTTPException(status_code=404, detail="카테고리를 찾을 수 없습니다")
        
        subcategories = taxonomy_service.get_subcategories(category_id)
        hierarchy = taxonomy_service.get_node_hierarchy(category_id)
        
        return {
            "category": category,
            "subcategories": subcategories,
            "subcategory_count": len(subcategories),
            "hierarchy": hierarchy
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"카테고리 상세 조회 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.get("/taxonomy/search")
async def search_taxonomy(
    q: str,
    case_sensitive: bool = False,
    taxonomy_service: TaxonomyService = Depends(get_taxonomy_service)
):
    """분류 체계 검색"""
    try:
        if len(q.strip()) < 2:
            raise HTTPException(status_code=400, detail="검색어는 최소 2자 이상이어야 합니다")
        
        results = taxonomy_service.search_by_name(q, case_sensitive)
        
        return {
            "query": q,
            "total_count": len(results),
            "results": results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"분류 체계 검색 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.get("/taxonomy/high-priority")
async def get_high_priority_vulnerabilities(
    taxonomy_service: TaxonomyService = Depends(get_taxonomy_service)
):
    """높은 우선순위 취약점들 조회"""
    try:
        high_priority = taxonomy_service.get_high_priority_vulnerabilities()
        
        # 우선순위별로 그룹화
        priority_groups = {}
        for vuln in high_priority:
            priority = vuln.priority
            if priority not in priority_groups:
                priority_groups[priority] = []
            priority_groups[priority].append(vuln)
        
        return {
            "total_count": len(high_priority),
            "priority_groups": priority_groups,
            "vulnerabilities": high_priority
        }
        
    except Exception as e:
        logger.error(f"높은 우선순위 취약점 조회 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.get("/taxonomy/statistics")
async def get_taxonomy_statistics(
    taxonomy_service: TaxonomyService = Depends(get_taxonomy_service)
):
    """분류 체계 통계 정보"""
    try:
        stats = taxonomy_service.get_statistics()
        return stats
        
    except Exception as e:
        logger.error(f"분류 체계 통계 조회 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.get("/analysis-summary")
async def get_analysis_summary_report(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    vulnerability_service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """분석 결과 요약 리포트"""
    try:
        # 모든 분석 결과 조회
        all_results = vulnerability_service.get_all_analysis_results()
        
        # 날짜 필터링 (구현 예정)
        filtered_results = all_results
        
        # 통계 계산
        total_analyses = len(filtered_results)
        completed_analyses = len([r for r in filtered_results if r.status == "completed"])
        verified_analyses = len([r for r in filtered_results if r.is_verified])
        
        # 카테고리별 분포
        category_distribution = {}
        severity_distribution = {}
        
        for result in filtered_results:
            if result.primary_category:
                cat_name = result.primary_category.name
                category_distribution[cat_name] = category_distribution.get(cat_name, 0) + 1
            
            if result.predicted_severity:
                sev = result.predicted_severity
                severity_distribution[sev] = severity_distribution.get(sev, 0) + 1
        
        return {
            "summary": {
                "total_analyses": total_analyses,
                "completed_analyses": completed_analyses,
                "verified_analyses": verified_analyses,
                "completion_rate": completed_analyses / total_analyses if total_analyses > 0 else 0,
                "verification_rate": verified_analyses / completed_analyses if completed_analyses > 0 else 0
            },
            "distributions": {
                "categories": category_distribution,
                "severities": severity_distribution
            },
            "period": {
                "start_date": start_date,
                "end_date": end_date
            }
        }
        
    except Exception as e:
        logger.error(f"분석 요약 리포트 생성 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.get("/export/taxonomy")
async def export_taxonomy(
    format: str = "json",
    taxonomy_service: TaxonomyService = Depends(get_taxonomy_service)
):
    """분류 체계 내보내기"""
    try:
        taxonomy = taxonomy_service.get_taxonomy()
        if not taxonomy:
            raise HTTPException(status_code=500, detail="분류 체계를 로드할 수 없습니다")
        
        if format.lower() == "json":
            content = json.dumps(taxonomy.dict(), ensure_ascii=False, indent=2)
            media_type = "application/json"
            filename = f"vulnerability-taxonomy-{datetime.now().strftime('%Y%m%d')}.json"
        else:
            raise HTTPException(status_code=400, detail="지원되지 않는 형식입니다")
        
        return Response(
            content=content,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"분류 체계 내보내기 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")

@router.get("/export/analysis-results")
async def export_analysis_results(
    format: str = "json",
    vulnerability_service: VulnerabilityService = Depends(get_vulnerability_service)
):
    """분석 결과 내보내기"""
    try:
        results = vulnerability_service.get_all_analysis_results()
        
        if format.lower() == "json":
            # Pydantic 모델을 딕셔너리로 변환
            results_dict = [result.dict() for result in results]
            content = json.dumps(results_dict, ensure_ascii=False, indent=2, default=str)
            media_type = "application/json"
            filename = f"analysis-results-{datetime.now().strftime('%Y%m%d')}.json"
        else:
            raise HTTPException(status_code=400, detail="지원되지 않는 형식입니다")
        
        return Response(
            content=content,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"분석 결과 내보내기 중 오류: {str(e)}")
        raise HTTPException(status_code=500, detail="내부 서버 오류")
