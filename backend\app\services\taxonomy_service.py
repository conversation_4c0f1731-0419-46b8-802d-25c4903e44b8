"""
취약점 분류 체계 서비스
"""

import json
import os
from typing import List, Optional, Dict, Any
from app.models.taxonomy import VulnerabilityTaxonomy, TaxonomyNode, NodeType
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class TaxonomyService:
    """취약점 분류 체계 관리 서비스"""
    
    def __init__(self, taxonomy_file_path: str = "data/vulnerability-rating-taxonomy.json"):
        self.taxonomy_file_path = taxonomy_file_path
        self._taxonomy: Optional[VulnerabilityTaxonomy] = None
        self._load_taxonomy()
    
    def _load_taxonomy(self) -> None:
        """분류 체계 파일 로드"""
        try:
            if not os.path.exists(self.taxonomy_file_path):
                logger.error(f"분류 체계 파일을 찾을 수 없습니다: {self.taxonomy_file_path}")
                return
            
            with open(self.taxonomy_file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
            
            # 부모 ID 설정
            self._set_parent_ids(data['content'])
            
            self._taxonomy = VulnerabilityTaxonomy(**data)
            logger.info(f"분류 체계 로드 완료: {len(self._taxonomy.content)}개 카테고리")
            
        except Exception as e:
            logger.error(f"분류 체계 로드 실패: {str(e)}")
            self._taxonomy = None
    
    def _set_parent_ids(self, nodes: List[Dict[str, Any]], parent_id: Optional[str] = None) -> None:
        """재귀적으로 부모 ID 설정"""
        for node in nodes:
            node['parent_id'] = parent_id
            if 'children' in node and node['children']:
                self._set_parent_ids(node['children'], node['id'])
    
    def get_taxonomy(self) -> Optional[VulnerabilityTaxonomy]:
        """전체 분류 체계 반환"""
        return self._taxonomy
    
    def get_all_categories(self) -> List[TaxonomyNode]:
        """모든 최상위 카테고리 반환"""
        if not self._taxonomy:
            return []
        return self._taxonomy.get_all_categories()
    
    def get_category_by_id(self, category_id: str) -> Optional[TaxonomyNode]:
        """ID로 카테고리 찾기"""
        if not self._taxonomy:
            return None
        return self._taxonomy.find_node_by_id(category_id)
    
    def get_subcategories(self, category_id: str) -> List[TaxonomyNode]:
        """특정 카테고리의 하위 카테고리들 반환"""
        if not self._taxonomy:
            return []
        return self._taxonomy.get_subcategories_by_category(category_id)
    
    def get_variants(self, subcategory_id: str) -> List[TaxonomyNode]:
        """특정 하위 카테고리의 변형들 반환"""
        if not self._taxonomy:
            return []
        return self._taxonomy.get_variants_by_subcategory(subcategory_id)
    
    def search_by_name(self, query: str, case_sensitive: bool = False) -> List[TaxonomyNode]:
        """이름으로 노드 검색"""
        if not self._taxonomy:
            return []
        
        results = []
        search_query = query if case_sensitive else query.lower()
        
        def search_recursive(nodes: List[TaxonomyNode]):
            for node in nodes:
                node_name = node.name if case_sensitive else node.name.lower()
                if search_query in node_name:
                    results.append(node)
                if node.children:
                    search_recursive(node.children)
        
        search_recursive(self._taxonomy.content)
        return results
    
    def get_nodes_by_priority(self, priority: int) -> List[TaxonomyNode]:
        """우선순위로 노드들 찾기"""
        if not self._taxonomy:
            return []
        return self._taxonomy.find_nodes_by_priority(priority)
    
    def get_high_priority_vulnerabilities(self) -> List[TaxonomyNode]:
        """높은 우선순위 취약점들 반환 (우선순위 1-2)"""
        if not self._taxonomy:
            return []
        
        high_priority = []
        high_priority.extend(self.get_nodes_by_priority(1))
        high_priority.extend(self.get_nodes_by_priority(2))
        return high_priority
    
    def get_path_to_node(self, node_id: str) -> List[str]:
        """노드까지의 경로 반환"""
        if not self._taxonomy:
            return []
        return self._taxonomy.get_path_to_node(node_id)
    
    def get_node_hierarchy(self, node_id: str) -> Dict[str, Any]:
        """노드의 계층 구조 정보 반환"""
        if not self._taxonomy:
            return {}
        
        node = self._taxonomy.find_node_by_id(node_id)
        if not node:
            return {}
        
        path = self.get_path_to_node(node_id)
        
        return {
            "node": node,
            "path": path,
            "depth": len(path),
            "parent_id": node.parent_id,
            "children_count": len(node.children),
            "has_variants": any(child.type == NodeType.VARIANT for child in node.children)
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """분류 체계 통계 정보 반환"""
        if not self._taxonomy:
            return {}
        
        categories = self._taxonomy.find_nodes_by_type(NodeType.CATEGORY)
        subcategories = self._taxonomy.find_nodes_by_type(NodeType.SUBCATEGORY)
        variants = self._taxonomy.find_nodes_by_type(NodeType.VARIANT)
        
        priority_stats = {}
        for priority in range(1, 6):
            priority_stats[f"priority_{priority}"] = len(self.get_nodes_by_priority(priority))
        
        return {
            "total_categories": len(categories),
            "total_subcategories": len(subcategories),
            "total_variants": len(variants),
            "total_nodes": len(categories) + len(subcategories) + len(variants),
            "priority_distribution": priority_stats,
            "metadata": self._taxonomy.metadata
        }
    
    def reload_taxonomy(self) -> bool:
        """분류 체계 다시 로드"""
        try:
            self._load_taxonomy()
            return self._taxonomy is not None
        except Exception as e:
            logger.error(f"분류 체계 재로드 실패: {str(e)}")
            return False
