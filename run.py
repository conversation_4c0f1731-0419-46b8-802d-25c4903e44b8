#!/usr/bin/env python3
"""
Bugcrowd AI 취약점 검증 툴 실행 스크립트
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path

def check_requirements():
    """필수 요구사항 확인"""
    print("🔍 시스템 요구사항 확인 중...")
    
    # Python 버전 확인
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 이상이 필요합니다.")
        sys.exit(1)
    
    print(f"✅ Python {sys.version.split()[0]} 확인됨")
    
    # 필수 파일 확인
    required_files = [
        "backend/requirements.txt",
        "frontend/package.json",
        "data/vulnerability-rating-taxonomy.json",
        ".env.example"
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ 필수 파일이 없습니다: {file_path}")
            sys.exit(1)
    
    print("✅ 필수 파일 확인 완료")

def setup_environment():
    """환경 설정"""
    print("🔧 환경 설정 중...")
    
    # .env 파일 생성 (없는 경우)
    if not Path(".env").exists():
        print("📝 .env 파일 생성 중...")
        subprocess.run(["cp", ".env.example", ".env"], check=True)
        print("⚠️  .env 파일을 편집하여 필요한 설정을 추가하세요.")
    
    # 로그 디렉토리 생성
    Path("logs").mkdir(exist_ok=True)
    Path("uploads").mkdir(exist_ok=True)
    
    print("✅ 환경 설정 완료")

def install_backend_dependencies():
    """백엔드 의존성 설치"""
    print("📦 백엔드 의존성 설치 중...")
    
    os.chdir("backend")
    try:
        # 가상환경 생성 (없는 경우)
        if not Path("venv").exists():
            print("🐍 Python 가상환경 생성 중...")
            subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        
        # 가상환경 활성화 및 의존성 설치
        if os.name == 'nt':  # Windows
            pip_path = "venv\\Scripts\\pip"
        else:  # Unix/Linux/macOS
            pip_path = "venv/bin/pip"
        
        print("📥 Python 패키지 설치 중...")
        subprocess.run([pip_path, "install", "-r", "requirements.txt"], check=True)
        
        print("✅ 백엔드 의존성 설치 완료")
    finally:
        os.chdir("..")

def install_frontend_dependencies():
    """프론트엔드 의존성 설치"""
    print("📦 프론트엔드 의존성 설치 중...")
    
    os.chdir("frontend")
    try:
        # Node.js 및 npm 확인
        try:
            subprocess.run(["node", "--version"], check=True, capture_output=True)
            subprocess.run(["npm", "--version"], check=True, capture_output=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Node.js와 npm이 설치되어 있어야 합니다.")
            print("   https://nodejs.org/ 에서 다운로드하세요.")
            sys.exit(1)
        
        print("📥 npm 패키지 설치 중...")
        subprocess.run(["npm", "install"], check=True)
        
        print("✅ 프론트엔드 의존성 설치 완료")
    finally:
        os.chdir("..")

def run_backend():
    """백엔드 서버 실행"""
    print("🚀 백엔드 서버 시작 중...")
    
    os.chdir("backend")
    try:
        if os.name == 'nt':  # Windows
            python_path = "venv\\Scripts\\python"
        else:  # Unix/Linux/macOS
            python_path = "venv/bin/python"
        
        # 환경 변수 설정
        env = os.environ.copy()
        env["PYTHONPATH"] = os.getcwd()
        
        subprocess.run([
            python_path, "-m", "uvicorn", 
            "app.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ], env=env)
    finally:
        os.chdir("..")

def run_frontend():
    """프론트엔드 서버 실행"""
    print("🚀 프론트엔드 서버 시작 중...")
    
    os.chdir("frontend")
    try:
        subprocess.run(["npm", "start"])
    finally:
        os.chdir("..")

def run_tests():
    """테스트 실행"""
    print("🧪 테스트 실행 중...")
    
    # 백엔드 테스트
    print("🔬 백엔드 테스트...")
    os.chdir("backend")
    try:
        if os.name == 'nt':  # Windows
            python_path = "venv\\Scripts\\python"
        else:  # Unix/Linux/macOS
            python_path = "venv/bin/python"
        
        subprocess.run([python_path, "-m", "pytest", "../tests/", "-v"], check=True)
        print("✅ 백엔드 테스트 통과")
    except subprocess.CalledProcessError:
        print("❌ 백엔드 테스트 실패")
    finally:
        os.chdir("..")
    
    # 프론트엔드 테스트
    print("🔬 프론트엔드 테스트...")
    os.chdir("frontend")
    try:
        subprocess.run(["npm", "test", "--", "--watchAll=false"], check=True)
        print("✅ 프론트엔드 테스트 통과")
    except subprocess.CalledProcessError:
        print("❌ 프론트엔드 테스트 실패")
    finally:
        os.chdir("..")

def run_docker():
    """Docker로 실행"""
    print("🐳 Docker 컨테이너 시작 중...")
    
    try:
        subprocess.run(["docker", "--version"], check=True, capture_output=True)
        subprocess.run(["docker-compose", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Docker와 Docker Compose가 설치되어 있어야 합니다.")
        sys.exit(1)
    
    subprocess.run(["docker-compose", "up", "--build"])

def main():
    """메인 함수"""
    parser = argparse.ArgumentParser(description="Bugcrowd AI 취약점 검증 툴")
    parser.add_argument("command", choices=[
        "setup", "backend", "frontend", "test", "docker", "all"
    ], help="실행할 명령")
    parser.add_argument("--skip-deps", action="store_true", help="의존성 설치 건너뛰기")
    
    args = parser.parse_args()
    
    print("🛡️  Bugcrowd AI 취약점 검증 툴")
    print("=" * 50)
    
    if args.command == "setup":
        check_requirements()
        setup_environment()
        if not args.skip_deps:
            install_backend_dependencies()
            install_frontend_dependencies()
        print("\n🎉 설정 완료! 이제 'python run.py all' 명령으로 실행하세요.")
    
    elif args.command == "backend":
        check_requirements()
        if not args.skip_deps:
            install_backend_dependencies()
        run_backend()
    
    elif args.command == "frontend":
        check_requirements()
        if not args.skip_deps:
            install_frontend_dependencies()
        run_frontend()
    
    elif args.command == "test":
        check_requirements()
        run_tests()
    
    elif args.command == "docker":
        check_requirements()
        run_docker()
    
    elif args.command == "all":
        check_requirements()
        setup_environment()
        if not args.skip_deps:
            install_backend_dependencies()
            install_frontend_dependencies()
        
        print("\n🚀 서버 시작 중...")
        print("백엔드: http://localhost:8000")
        print("프론트엔드: http://localhost:3000")
        print("\n종료하려면 Ctrl+C를 누르세요.")
        
        # 백엔드와 프론트엔드를 별도 프로세스로 실행
        import threading
        
        backend_thread = threading.Thread(target=run_backend)
        frontend_thread = threading.Thread(target=run_frontend)
        
        backend_thread.daemon = True
        frontend_thread.daemon = True
        
        backend_thread.start()
        time.sleep(5)  # 백엔드가 먼저 시작되도록 대기
        frontend_thread.start()
        
        try:
            backend_thread.join()
            frontend_thread.join()
        except KeyboardInterrupt:
            print("\n👋 서버를 종료합니다...")

if __name__ == "__main__":
    main()
