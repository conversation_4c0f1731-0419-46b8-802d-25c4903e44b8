"""
pytest 설정 및 공통 픽스처
"""

import pytest
import asyncio
import os
import sys
from unittest.mock import Mock, patch

# 프로젝트 루트를 Python 경로에 추가
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

@pytest.fixture(scope="session")
def event_loop():
    """세션 범위의 이벤트 루프"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def mock_taxonomy_service():
    """모의 분류 체계 서비스"""
    service = Mock()
    service.get_taxonomy.return_value = Mock(
        content=[
            Mock(
                id='cross_site_scripting_xss',
                name='Cross-Site Scripting (XSS)',
                type='category',
                priority=3,
                children=[]
            ),
            Mock(
                id='sql_injection',
                name='SQL Injection',
                type='category',
                priority=2,
                children=[]
            )
        ]
    )
    service.get_category_by_id.return_value = Mock(
        id='cross_site_scripting_xss',
        name='Cross-Site Scripting (XSS)',
        type='category',
        priority=3
    )
    return service

@pytest.fixture
def mock_vulnerability_service():
    """모의 취약점 서비스"""
    service = Mock()
    service.create_report.return_value = Mock(
        id='test-report-id',
        title='Test Report',
        description='Test Description'
    )
    service.get_report.return_value = Mock(
        id='test-report-id',
        title='Test Report',
        description='Test Description'
    )
    service.get_all_reports.return_value = []
    service.create_analysis_result.return_value = Mock(
        id='test-analysis-id',
        report_id='test-report-id',
        status='pending'
    )
    service.get_analysis_result.return_value = Mock(
        id='test-analysis-id',
        status='completed',
        confidence=0.85
    )
    return service

@pytest.fixture
def sample_vulnerability_data():
    """샘플 취약점 데이터"""
    return {
        'title': 'Cross-Site Scripting in Search Form',
        'description': 'The search functionality is vulnerable to XSS attacks through the query parameter',
        'severity': 'medium',
        'affected_system': 'Web Application',
        'vulnerability_type': 'Cross-Site Scripting',
        'attack_vector': 'Network',
        'impact': 'Allows execution of arbitrary JavaScript code in user browsers',
        'steps_to_reproduce': '''
1. Navigate to the search page
2. Enter the following payload in the search box: <script>alert('XSS')</script>
3. Submit the form
4. Observe that the script executes
        '''.strip(),
        'proof_of_concept': '<script>alert("XSS Vulnerability")</script>',
        'reporter': 'Security Researcher',
        'tags': ['xss', 'web', 'javascript']
    }

@pytest.fixture
def sample_analysis_request():
    """샘플 분석 요청"""
    return {
        'report': {
            'title': 'Test XSS Vulnerability',
            'description': 'Test description for XSS vulnerability',
            'steps_to_reproduce': 'Test steps',
            'proof_of_concept': '<script>alert(1)</script>'
        },
        'ai_model': 'gpt-4',
        'include_reasoning': True,
        'include_recommendations': True
    }

@pytest.fixture
def mock_ai_response():
    """모의 AI 응답"""
    return {
        'classification': {
            'primary_category': {
                'id': 'cross_site_scripting_xss',
                'name': 'Cross-Site Scripting (XSS)',
                'type': 'category',
                'confidence_score': 0.9
            },
            'secondary_categories': [],
            'reasoning': 'The vulnerability involves script injection through user input'
        },
        'severity_assessment': {
            'severity': 'medium',
            'confidence': 0.8,
            'reasoning': 'XSS vulnerabilities typically have medium severity'
        },
        'recommendations': {
            'immediate_actions': [
                'Sanitize all user input',
                'Implement Content Security Policy'
            ],
            'short_term_fixes': [
                'Update input validation',
                'Add output encoding'
            ],
            'long_term_improvements': [
                'Security code review',
                'Developer training'
            ]
        },
        'analysis_metadata': {
            'confidence': 0.85,
            'keywords_identified': ['xss', 'script', 'injection'],
            'analysis_notes': 'High confidence XSS classification'
        }
    }

@pytest.fixture(autouse=True)
def mock_environment_variables():
    """환경 변수 모킹"""
    with patch.dict(os.environ, {
        'DATABASE_URL': 'sqlite:///test.db',
        'LOG_LEVEL': 'DEBUG',
        'OPENAI_API_KEY': 'test-openai-key',
        'OLLAMA_HOST': 'http://localhost:11434',
        'TAXONOMY_FILE_PATH': 'data/vulnerability-rating-taxonomy.json'
    }):
        yield

@pytest.fixture
def mock_file_system():
    """파일 시스템 모킹"""
    mock_taxonomy_data = {
        'metadata': {
            'version': '1.0',
            'last_updated': '2024-01-01'
        },
        'content': [
            {
                'id': 'cross_site_scripting_xss',
                'name': 'Cross-Site Scripting (XSS)',
                'type': 'category',
                'priority': 3,
                'children': []
            }
        ]
    }
    
    with patch('builtins.open'), \
         patch('json.load', return_value=mock_taxonomy_data), \
         patch('os.path.exists', return_value=True):
        yield

@pytest.fixture
def clean_database():
    """테스트용 깨끗한 데이터베이스"""
    # 실제 구현에서는 테스트 데이터베이스 설정
    yield
    # 테스트 후 정리

class AsyncMock:
    """비동기 함수 모킹을 위한 헬퍼 클래스"""
    
    def __init__(self, return_value=None, side_effect=None):
        self.return_value = return_value
        self.side_effect = side_effect
        self.call_count = 0
        self.call_args_list = []
    
    async def __call__(self, *args, **kwargs):
        self.call_count += 1
        self.call_args_list.append((args, kwargs))
        
        if self.side_effect:
            if isinstance(self.side_effect, Exception):
                raise self.side_effect
            elif callable(self.side_effect):
                return await self.side_effect(*args, **kwargs)
            else:
                return self.side_effect
        
        return self.return_value

@pytest.fixture
def async_mock():
    """비동기 모킹 팩토리"""
    return AsyncMock

# 테스트 마커 정의
pytest_plugins = []

def pytest_configure(config):
    """pytest 설정"""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )

def pytest_collection_modifyitems(config, items):
    """테스트 아이템 수정"""
    for item in items:
        # 비동기 테스트에 asyncio 마커 추가
        if asyncio.iscoroutinefunction(item.function):
            item.add_marker(pytest.mark.asyncio)
        
        # 파일 이름에 따른 마커 추가
        if "test_integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        elif "test_unit" in item.nodeid:
            item.add_marker(pytest.mark.unit)

# 테스트 데이터 상수
TEST_TAXONOMY_DATA = [
    {
        'id': 'cross_site_scripting_xss',
        'name': 'Cross-Site Scripting (XSS)',
        'type': 'category',
        'priority': 3
    },
    {
        'id': 'sql_injection',
        'name': 'SQL Injection',
        'type': 'category',
        'priority': 2
    },
    {
        'id': 'cross_site_request_forgery_csrf',
        'name': 'Cross-Site Request Forgery (CSRF)',
        'type': 'category',
        'priority': 3
    }
]

TEST_VULNERABILITY_SAMPLES = [
    {
        'title': 'XSS in search form',
        'description': 'Cross-site scripting vulnerability',
        'severity': 'medium',
        'type': 'xss'
    },
    {
        'title': 'SQL injection in login',
        'description': 'SQL injection vulnerability',
        'severity': 'high',
        'type': 'sql_injection'
    }
]
