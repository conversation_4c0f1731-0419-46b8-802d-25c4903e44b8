{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\aidesk\\\\frontend\\\\src\\\\pages\\\\Reports.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useQuery } from '@tanstack/react-query';\nimport { DocumentTextIcon, ChartBarIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';\nimport { reportsApi, analysisApi } from '../services/api.ts';\nimport { format, subDays } from 'date-fns';\nimport { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, PieChart, Pie, Cell, LineChart, Line, ResponsiveContainer } from 'recharts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Reports = () => {\n  _s();\n  const [dateRange, setDateRange] = useState({\n    start: format(subDays(new Date(), 30), 'yyyy-MM-dd'),\n    end: format(new Date(), 'yyyy-MM-dd')\n  });\n  const [reportType, setReportType] = useState('summary');\n\n  // 분석 요약 데이터\n  const {\n    data: analysisSummary,\n    isLoading: summaryLoading\n  } = useQuery(['analysis-summary', dateRange], () => reportsApi.getAnalysisSummary({\n    start_date: dateRange.start,\n    end_date: dateRange.end\n  }));\n\n  // 분류 체계 통계\n  const {\n    data: taxonomyStats\n  } = useQuery('taxonomy-statistics', reportsApi.getTaxonomyStatistics);\n\n  // 전체 분석 결과\n  const {\n    data: allAnalyses\n  } = useQuery('all-analyses', () => analysisApi.getAllAnalysisResults());\n\n  // 차트 색상\n  const COLORS = ['#3B82F6', '#EF4444', '#F59E0B', '#10B981', '#8B5CF6', '#F97316'];\n\n  // 심각도별 분포 데이터 준비\n  const severityData = React.useMemo(() => {\n    var _analysisSummary$data, _analysisSummary$data2;\n    if (!(analysisSummary !== null && analysisSummary !== void 0 && (_analysisSummary$data = analysisSummary.data) !== null && _analysisSummary$data !== void 0 && (_analysisSummary$data2 = _analysisSummary$data.distributions) !== null && _analysisSummary$data2 !== void 0 && _analysisSummary$data2.severities)) return [];\n    return Object.entries(analysisSummary.data.distributions.severities).map(([severity, count]) => ({\n      name: severity,\n      value: count,\n      color: severity === 'critical' ? '#EF4444' : severity === 'high' ? '#F97316' : severity === 'medium' ? '#F59E0B' : severity === 'low' ? '#3B82F6' : '#6B7280'\n    }));\n  }, [analysisSummary]);\n\n  // 카테고리별 분포 데이터 준비\n  const categoryData = React.useMemo(() => {\n    var _analysisSummary$data3, _analysisSummary$data4;\n    if (!(analysisSummary !== null && analysisSummary !== void 0 && (_analysisSummary$data3 = analysisSummary.data) !== null && _analysisSummary$data3 !== void 0 && (_analysisSummary$data4 = _analysisSummary$data3.distributions) !== null && _analysisSummary$data4 !== void 0 && _analysisSummary$data4.categories)) return [];\n    return Object.entries(analysisSummary.data.distributions.categories).slice(0, 10) // 상위 10개만\n    .map(([category, count]) => ({\n      name: category.length > 20 ? `${category.substring(0, 20)}...` : category,\n      fullName: category,\n      count: count\n    }));\n  }, [analysisSummary]);\n\n  // 시간별 분석 트렌드 데이터 (더미 데이터)\n  const trendData = React.useMemo(() => {\n    const days = [];\n    for (let i = 6; i >= 0; i--) {\n      const date = subDays(new Date(), i);\n      days.push({\n        date: format(date, 'MM/dd'),\n        analyses: Math.floor(Math.random() * 20) + 5,\n        verified: Math.floor(Math.random() * 15) + 3\n      });\n    }\n    return days;\n  }, []);\n  const handleExport = async (type, format = 'json') => {\n    try {\n      let response;\n      if (type === 'taxonomy') {\n        response = await reportsApi.exportTaxonomy(format);\n      } else {\n        response = await reportsApi.exportAnalysisResults(format);\n      }\n\n      // 파일 다운로드\n      const blob = new Blob([response.data], {\n        type: 'application/json'\n      });\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${type}-export-${format(new Date(), 'yyyy-MM-dd')}.${format}`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Export failed:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(DocumentTextIcon, {\n          className: \"h-8 w-8 text-primary-600 mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"\\uB9AC\\uD3EC\\uD2B8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-sm text-gray-500\",\n            children: \"\\uBD84\\uC11D \\uACB0\\uACFC\\uC640 \\uD1B5\\uACC4\\uB97C \\uD655\\uC778\\uD558\\uACE0 \\uB9AC\\uD3EC\\uD2B8\\uB97C \\uC0DD\\uC131\\uD558\\uC138\\uC694.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleExport('taxonomy'),\n          className: \"btn-outline\",\n          children: [/*#__PURE__*/_jsxDEV(ArrowDownTrayIcon, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), \"\\uBD84\\uB958 \\uCCB4\\uACC4 \\uB0B4\\uBCF4\\uB0B4\\uAE30\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleExport('analysis'),\n          className: \"btn-primary\",\n          children: [/*#__PURE__*/_jsxDEV(ArrowDownTrayIcon, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), \"\\uBD84\\uC11D \\uACB0\\uACFC \\uB0B4\\uBCF4\\uB0B4\\uAE30\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\uB9AC\\uD3EC\\uD2B8 \\uC720\\uD615\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: reportType,\n              onChange: e => setReportType(e.target.value),\n              className: \"form-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"summary\",\n                children: \"\\uC694\\uC57D \\uB9AC\\uD3EC\\uD2B8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"detailed\",\n                children: \"\\uC0C1\\uC138 \\uB9AC\\uD3EC\\uD2B8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"taxonomy\",\n                children: \"\\uBD84\\uB958 \\uCCB4\\uACC4 \\uB9AC\\uD3EC\\uD2B8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\uC2DC\\uC791 \\uB0A0\\uC9DC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              value: dateRange.start,\n              onChange: e => setDateRange(prev => ({\n                ...prev,\n                start: e.target.value\n              })),\n              className: \"form-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\uC885\\uB8CC \\uB0A0\\uC9DC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              value: dateRange.end,\n              onChange: e => setDateRange(prev => ({\n                ...prev,\n                end: e.target.value\n              })),\n              className: \"form-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), (analysisSummary === null || analysisSummary === void 0 ? void 0 : analysisSummary.data) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(ChartBarIcon, {\n              className: \"h-8 w-8 text-primary-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: analysisSummary.data.summary.total_analyses\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"\\uCD1D \\uBD84\\uC11D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-8 w-8 bg-success-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white text-sm font-bold\",\n                children: \"\\u2713\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: analysisSummary.data.summary.completed_analyses\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"\\uC644\\uB8CC\\uB41C \\uBD84\\uC11D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-8 w-8 bg-warning-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white text-sm font-bold\",\n                children: \"%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: [(analysisSummary.data.summary.completion_rate * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"\\uC644\\uB8CC\\uC728\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white text-sm font-bold\",\n                children: \"V\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: analysisSummary.data.summary.verified_analyses\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: \"\\uAC80\\uC99D\\uB41C \\uBD84\\uC11D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"\\uC2EC\\uAC01\\uB3C4 \\uBD84\\uD3EC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: severityData,\n                cx: \"50%\",\n                cy: \"50%\",\n                labelLine: false,\n                label: ({\n                  name,\n                  percent\n                }) => `${name} ${(percent * 100).toFixed(0)}%`,\n                outerRadius: 80,\n                fill: \"#8884d8\",\n                dataKey: \"value\",\n                children: severityData.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: entry.color\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"\\uC0C1\\uC704 \\uCDE8\\uC57D\\uC810 \\uCE74\\uD14C\\uACE0\\uB9AC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: categoryData,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"name\",\n                angle: -45,\n                textAnchor: \"end\",\n                height: 80\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                formatter: (value, name, props) => [value, props.payload.fullName]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"count\",\n                fill: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900\",\n          children: \"\\uCD5C\\uADFC 7\\uC77C \\uBD84\\uC11D \\uD2B8\\uB80C\\uB4DC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: 300,\n          children: /*#__PURE__*/_jsxDEV(LineChart, {\n            data: trendData,\n            children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n              strokeDasharray: \"3 3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n              dataKey: \"date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: \"analyses\",\n              stroke: \"#3B82F6\",\n              strokeWidth: 2,\n              name: \"\\uCD1D \\uBD84\\uC11D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: \"verified\",\n              stroke: \"#10B981\",\n              strokeWidth: 2,\n              name: \"\\uAC80\\uC99D\\uB41C \\uBD84\\uC11D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this), (taxonomyStats === null || taxonomyStats === void 0 ? void 0 : taxonomyStats.data) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900\",\n          children: \"\\uBD84\\uB958 \\uCCB4\\uACC4 \\uD1B5\\uACC4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-primary-600\",\n              children: taxonomyStats.data.total_categories\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500\",\n              children: \"\\uCE74\\uD14C\\uACE0\\uB9AC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-secondary-600\",\n              children: taxonomyStats.data.total_subcategories\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500\",\n              children: \"\\uD558\\uC704 \\uCE74\\uD14C\\uACE0\\uB9AC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-success-600\",\n              children: taxonomyStats.data.total_variants\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500\",\n              children: \"\\uBCC0\\uD615\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-warning-600\",\n              children: taxonomyStats.data.total_nodes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500\",\n              children: \"\\uC804\\uCCB4 \\uB178\\uB4DC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(Reports, \"LiyhQkdtzdMjRQVfitWeBlB8PXM=\", false, function () {\n  return [useQuery, useQuery, useQuery];\n});\n_c = Reports;\nexport default Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");", "map": {"version": 3, "names": ["React", "useState", "useQuery", "DocumentTextIcon", "ChartBarIcon", "ArrowDownTrayIcon", "reportsApi", "analysisApi", "format", "subDays", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "Line<PERSON>hart", "Line", "ResponsiveContainer", "jsxDEV", "_jsxDEV", "Reports", "_s", "date<PERSON><PERSON><PERSON>", "setDateRange", "start", "Date", "end", "reportType", "setReportType", "data", "analysisSummary", "isLoading", "summaryLoading", "getAnalysisSummary", "start_date", "end_date", "taxonomyStats", "getTaxonomyStatistics", "allAnalyses", "getAllAnalysisResults", "COLORS", "severityData", "useMemo", "_analysisSummary$data", "_analysisSummary$data2", "distributions", "severities", "Object", "entries", "map", "severity", "count", "name", "value", "color", "categoryData", "_analysisSummary$data3", "_analysisSummary$data4", "categories", "slice", "category", "length", "substring", "fullName", "trendData", "days", "i", "date", "push", "analyses", "Math", "floor", "random", "verified", "handleExport", "type", "response", "exportTaxonomy", "exportAnalysisResults", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onChange", "e", "target", "prev", "summary", "total_analyses", "completed_analyses", "completion_rate", "toFixed", "verified_analyses", "width", "height", "cx", "cy", "labelLine", "label", "percent", "outerRadius", "fill", "dataKey", "entry", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angle", "textAnchor", "formatter", "props", "payload", "stroke", "strokeWidth", "total_categories", "total_subcategories", "total_variants", "total_nodes", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/aidesk/frontend/src/pages/Reports.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useQuery } from '@tanstack/react-query';\nimport {\n  DocumentTextIcon,\n  ChartBarIcon,\n  ArrowDownTrayIcon,\n  CalendarIcon,\n  FunnelIcon,\n} from '@heroicons/react/24/outline';\nimport { reportsApi, analysisApi } from '../services/api.ts';\nimport { format, subDays, startOfDay, endOfDay } from 'date-fns';\nimport { ko } from 'date-fns/locale';\nimport {\n  Bar<PERSON>hart,\n  Bar,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  <PERSON>Chart,\n  Pie,\n  Cell,\n  LineChart,\n  Line,\n  ResponsiveContainer,\n} from 'recharts';\n\nconst Reports: React.FC = () => {\n  const [dateRange, setDateRange] = useState({\n    start: format(subDays(new Date(), 30), 'yyyy-MM-dd'),\n    end: format(new Date(), 'yyyy-MM-dd'),\n  });\n  const [reportType, setReportType] = useState<'summary' | 'detailed' | 'taxonomy'>('summary');\n\n  // 분석 요약 데이터\n  const { data: analysisSummary, isLoading: summaryLoading } = useQuery(\n    ['analysis-summary', dateRange],\n    () => reportsApi.getAnalysisSummary({\n      start_date: dateRange.start,\n      end_date: dateRange.end,\n    })\n  );\n\n  // 분류 체계 통계\n  const { data: taxonomyStats } = useQuery(\n    'taxonomy-statistics',\n    reportsApi.getTaxonomyStatistics\n  );\n\n  // 전체 분석 결과\n  const { data: allAnalyses } = useQuery(\n    'all-analyses',\n    () => analysisApi.getAllAnalysisResults()\n  );\n\n  // 차트 색상\n  const COLORS = ['#3B82F6', '#EF4444', '#F59E0B', '#10B981', '#8B5CF6', '#F97316'];\n\n  // 심각도별 분포 데이터 준비\n  const severityData = React.useMemo(() => {\n    if (!analysisSummary?.data?.distributions?.severities) return [];\n    \n    return Object.entries(analysisSummary.data.distributions.severities).map(([severity, count]) => ({\n      name: severity,\n      value: count as number,\n      color: severity === 'critical' ? '#EF4444' :\n             severity === 'high' ? '#F97316' :\n             severity === 'medium' ? '#F59E0B' :\n             severity === 'low' ? '#3B82F6' : '#6B7280'\n    }));\n  }, [analysisSummary]);\n\n  // 카테고리별 분포 데이터 준비\n  const categoryData = React.useMemo(() => {\n    if (!analysisSummary?.data?.distributions?.categories) return [];\n    \n    return Object.entries(analysisSummary.data.distributions.categories)\n      .slice(0, 10) // 상위 10개만\n      .map(([category, count]) => ({\n        name: category.length > 20 ? `${category.substring(0, 20)}...` : category,\n        fullName: category,\n        count: count as number,\n      }));\n  }, [analysisSummary]);\n\n  // 시간별 분석 트렌드 데이터 (더미 데이터)\n  const trendData = React.useMemo(() => {\n    const days = [];\n    for (let i = 6; i >= 0; i--) {\n      const date = subDays(new Date(), i);\n      days.push({\n        date: format(date, 'MM/dd'),\n        analyses: Math.floor(Math.random() * 20) + 5,\n        verified: Math.floor(Math.random() * 15) + 3,\n      });\n    }\n    return days;\n  }, []);\n\n  const handleExport = async (type: 'taxonomy' | 'analysis', format: 'json' = 'json') => {\n    try {\n      let response;\n      if (type === 'taxonomy') {\n        response = await reportsApi.exportTaxonomy(format);\n      } else {\n        response = await reportsApi.exportAnalysisResults(format);\n      }\n      \n      // 파일 다운로드\n      const blob = new Blob([response.data], { type: 'application/json' });\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `${type}-export-${format(new Date(), 'yyyy-MM-dd')}.${format}`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Export failed:', error);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 헤더 */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center\">\n          <DocumentTextIcon className=\"h-8 w-8 text-primary-600 mr-3\" />\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">리포트</h1>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              분석 결과와 통계를 확인하고 리포트를 생성하세요.\n            </p>\n          </div>\n        </div>\n        <div className=\"flex items-center space-x-3\">\n          <button\n            onClick={() => handleExport('taxonomy')}\n            className=\"btn-outline\"\n          >\n            <ArrowDownTrayIcon className=\"h-4 w-4 mr-2\" />\n            분류 체계 내보내기\n          </button>\n          <button\n            onClick={() => handleExport('analysis')}\n            className=\"btn-primary\"\n          >\n            <ArrowDownTrayIcon className=\"h-4 w-4 mr-2\" />\n            분석 결과 내보내기\n          </button>\n        </div>\n      </div>\n\n      {/* 필터 및 설정 */}\n      <div className=\"card\">\n        <div className=\"card-body\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"form-label\">리포트 유형</label>\n              <select\n                value={reportType}\n                onChange={(e) => setReportType(e.target.value as any)}\n                className=\"form-select\"\n              >\n                <option value=\"summary\">요약 리포트</option>\n                <option value=\"detailed\">상세 리포트</option>\n                <option value=\"taxonomy\">분류 체계 리포트</option>\n              </select>\n            </div>\n            <div>\n              <label className=\"form-label\">시작 날짜</label>\n              <input\n                type=\"date\"\n                value={dateRange.start}\n                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}\n                className=\"form-input\"\n              />\n            </div>\n            <div>\n              <label className=\"form-label\">종료 날짜</label>\n              <input\n                type=\"date\"\n                value={dateRange.end}\n                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}\n                className=\"form-input\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 요약 통계 */}\n      {analysisSummary?.data && (\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <div className=\"flex items-center\">\n                <ChartBarIcon className=\"h-8 w-8 text-primary-500\" />\n                <div className=\"ml-4\">\n                  <div className=\"text-2xl font-bold text-gray-900\">\n                    {analysisSummary.data.summary.total_analyses}\n                  </div>\n                  <div className=\"text-sm text-gray-500\">총 분석</div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <div className=\"flex items-center\">\n                <div className=\"h-8 w-8 bg-success-500 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white text-sm font-bold\">✓</span>\n                </div>\n                <div className=\"ml-4\">\n                  <div className=\"text-2xl font-bold text-gray-900\">\n                    {analysisSummary.data.summary.completed_analyses}\n                  </div>\n                  <div className=\"text-sm text-gray-500\">완료된 분석</div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <div className=\"flex items-center\">\n                <div className=\"h-8 w-8 bg-warning-500 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white text-sm font-bold\">%</span>\n                </div>\n                <div className=\"ml-4\">\n                  <div className=\"text-2xl font-bold text-gray-900\">\n                    {(analysisSummary.data.summary.completion_rate * 100).toFixed(1)}%\n                  </div>\n                  <div className=\"text-sm text-gray-500\">완료율</div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <div className=\"flex items-center\">\n                <div className=\"h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white text-sm font-bold\">V</span>\n                </div>\n                <div className=\"ml-4\">\n                  <div className=\"text-2xl font-bold text-gray-900\">\n                    {analysisSummary.data.summary.verified_analyses}\n                  </div>\n                  <div className=\"text-sm text-gray-500\">검증된 분석</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 차트 섹션 */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* 심각도 분포 파이 차트 */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"text-lg font-medium text-gray-900\">심각도 분포</h3>\n          </div>\n          <div className=\"card-body\">\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <PieChart>\n                <Pie\n                  data={severityData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n                  outerRadius={80}\n                  fill=\"#8884d8\"\n                  dataKey=\"value\"\n                >\n                  {severityData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip />\n              </PieChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n\n        {/* 카테고리별 분포 바 차트 */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"text-lg font-medium text-gray-900\">상위 취약점 카테고리</h3>\n          </div>\n          <div className=\"card-body\">\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={categoryData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis \n                  dataKey=\"name\" \n                  angle={-45}\n                  textAnchor=\"end\"\n                  height={80}\n                />\n                <YAxis />\n                <Tooltip \n                  formatter={(value, name, props) => [value, props.payload.fullName]}\n                />\n                <Bar dataKey=\"count\" fill=\"#3B82F6\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n      </div>\n\n      {/* 시간별 트렌드 */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"text-lg font-medium text-gray-900\">최근 7일 분석 트렌드</h3>\n        </div>\n        <div className=\"card-body\">\n          <ResponsiveContainer width=\"100%\" height={300}>\n            <LineChart data={trendData}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis dataKey=\"date\" />\n              <YAxis />\n              <Tooltip />\n              <Legend />\n              <Line \n                type=\"monotone\" \n                dataKey=\"analyses\" \n                stroke=\"#3B82F6\" \n                strokeWidth={2}\n                name=\"총 분석\"\n              />\n              <Line \n                type=\"monotone\" \n                dataKey=\"verified\" \n                stroke=\"#10B981\" \n                strokeWidth={2}\n                name=\"검증된 분석\"\n              />\n            </LineChart>\n          </ResponsiveContainer>\n        </div>\n      </div>\n\n      {/* 분류 체계 통계 */}\n      {taxonomyStats?.data && (\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"text-lg font-medium text-gray-900\">분류 체계 통계</h3>\n          </div>\n          <div className=\"card-body\">\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-primary-600\">\n                  {taxonomyStats.data.total_categories}\n                </div>\n                <div className=\"text-sm text-gray-500\">카테고리</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-secondary-600\">\n                  {taxonomyStats.data.total_subcategories}\n                </div>\n                <div className=\"text-sm text-gray-500\">하위 카테고리</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-success-600\">\n                  {taxonomyStats.data.total_variants}\n                </div>\n                <div className=\"text-sm text-gray-500\">변형</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-warning-600\">\n                  {taxonomyStats.data.total_nodes}\n                </div>\n                <div className=\"text-sm text-gray-500\">전체 노드</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Reports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SACEC,gBAAgB,EAChBC,YAAY,EACZC,iBAAiB,QAGZ,6BAA6B;AACpC,SAASC,UAAU,EAAEC,WAAW,QAAQ,oBAAoB;AAC5D,SAASC,MAAM,EAAEC,OAAO,QAA8B,UAAU;AAEhE,SACEC,QAAQ,EACRC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,SAAS,EACTC,IAAI,EACJC,mBAAmB,QACd,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElB,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC;IACzC4B,KAAK,EAAErB,MAAM,CAACC,OAAO,CAAC,IAAIqB,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,YAAY,CAAC;IACpDC,GAAG,EAAEvB,MAAM,CAAC,IAAIsB,IAAI,CAAC,CAAC,EAAE,YAAY;EACtC,CAAC,CAAC;EACF,MAAM,CAACE,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAsC,SAAS,CAAC;;EAE5F;EACA,MAAM;IAAEiC,IAAI,EAAEC,eAAe;IAAEC,SAAS,EAAEC;EAAe,CAAC,GAAGnC,QAAQ,CACnE,CAAC,kBAAkB,EAAEyB,SAAS,CAAC,EAC/B,MAAMrB,UAAU,CAACgC,kBAAkB,CAAC;IAClCC,UAAU,EAAEZ,SAAS,CAACE,KAAK;IAC3BW,QAAQ,EAAEb,SAAS,CAACI;EACtB,CAAC,CACH,CAAC;;EAED;EACA,MAAM;IAAEG,IAAI,EAAEO;EAAc,CAAC,GAAGvC,QAAQ,CACtC,qBAAqB,EACrBI,UAAU,CAACoC,qBACb,CAAC;;EAED;EACA,MAAM;IAAER,IAAI,EAAES;EAAY,CAAC,GAAGzC,QAAQ,CACpC,cAAc,EACd,MAAMK,WAAW,CAACqC,qBAAqB,CAAC,CAC1C,CAAC;;EAED;EACA,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;;EAEjF;EACA,MAAMC,YAAY,GAAG9C,KAAK,CAAC+C,OAAO,CAAC,MAAM;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IACvC,IAAI,EAACd,eAAe,aAAfA,eAAe,gBAAAa,qBAAA,GAAfb,eAAe,CAAED,IAAI,cAAAc,qBAAA,gBAAAC,sBAAA,GAArBD,qBAAA,CAAuBE,aAAa,cAAAD,sBAAA,eAApCA,sBAAA,CAAsCE,UAAU,GAAE,OAAO,EAAE;IAEhE,OAAOC,MAAM,CAACC,OAAO,CAAClB,eAAe,CAACD,IAAI,CAACgB,aAAa,CAACC,UAAU,CAAC,CAACG,GAAG,CAAC,CAAC,CAACC,QAAQ,EAAEC,KAAK,CAAC,MAAM;MAC/FC,IAAI,EAAEF,QAAQ;MACdG,KAAK,EAAEF,KAAe;MACtBG,KAAK,EAAEJ,QAAQ,KAAK,UAAU,GAAG,SAAS,GACnCA,QAAQ,KAAK,MAAM,GAAG,SAAS,GAC/BA,QAAQ,KAAK,QAAQ,GAAG,SAAS,GACjCA,QAAQ,KAAK,KAAK,GAAG,SAAS,GAAG;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACpB,eAAe,CAAC,CAAC;;EAErB;EACA,MAAMyB,YAAY,GAAG5D,KAAK,CAAC+C,OAAO,CAAC,MAAM;IAAA,IAAAc,sBAAA,EAAAC,sBAAA;IACvC,IAAI,EAAC3B,eAAe,aAAfA,eAAe,gBAAA0B,sBAAA,GAAf1B,eAAe,CAAED,IAAI,cAAA2B,sBAAA,gBAAAC,sBAAA,GAArBD,sBAAA,CAAuBX,aAAa,cAAAY,sBAAA,eAApCA,sBAAA,CAAsCC,UAAU,GAAE,OAAO,EAAE;IAEhE,OAAOX,MAAM,CAACC,OAAO,CAAClB,eAAe,CAACD,IAAI,CAACgB,aAAa,CAACa,UAAU,CAAC,CACjEC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAAA,CACbV,GAAG,CAAC,CAAC,CAACW,QAAQ,EAAET,KAAK,CAAC,MAAM;MAC3BC,IAAI,EAAEQ,QAAQ,CAACC,MAAM,GAAG,EAAE,GAAG,GAAGD,QAAQ,CAACE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAAGF,QAAQ;MACzEG,QAAQ,EAAEH,QAAQ;MAClBT,KAAK,EAAEA;IACT,CAAC,CAAC,CAAC;EACP,CAAC,EAAE,CAACrB,eAAe,CAAC,CAAC;;EAErB;EACA,MAAMkC,SAAS,GAAGrE,KAAK,CAAC+C,OAAO,CAAC,MAAM;IACpC,MAAMuB,IAAI,GAAG,EAAE;IACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMC,IAAI,GAAG/D,OAAO,CAAC,IAAIqB,IAAI,CAAC,CAAC,EAAEyC,CAAC,CAAC;MACnCD,IAAI,CAACG,IAAI,CAAC;QACRD,IAAI,EAAEhE,MAAM,CAACgE,IAAI,EAAE,OAAO,CAAC;QAC3BE,QAAQ,EAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;QAC5CC,QAAQ,EAAEH,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;MAC7C,CAAC,CAAC;IACJ;IACA,OAAOP,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,YAAY,GAAG,MAAAA,CAAOC,IAA6B,EAAExE,MAAc,GAAG,MAAM,KAAK;IACrF,IAAI;MACF,IAAIyE,QAAQ;MACZ,IAAID,IAAI,KAAK,UAAU,EAAE;QACvBC,QAAQ,GAAG,MAAM3E,UAAU,CAAC4E,cAAc,CAAC1E,MAAM,CAAC;MACpD,CAAC,MAAM;QACLyE,QAAQ,GAAG,MAAM3E,UAAU,CAAC6E,qBAAqB,CAAC3E,MAAM,CAAC;MAC3D;;MAEA;MACA,MAAM4E,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,QAAQ,CAAC/C,IAAI,CAAC,EAAE;QAAE8C,IAAI,EAAE;MAAmB,CAAC,CAAC;MACpE,MAAMM,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;MACfI,IAAI,CAACI,QAAQ,GAAG,GAAGd,IAAI,WAAWxE,MAAM,CAAC,IAAIsB,IAAI,CAAC,CAAC,EAAE,YAAY,CAAC,IAAItB,MAAM,EAAE;MAC9EmF,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;IACjC,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC;EACF,CAAC;EAED,oBACE5E,OAAA;IAAK8E,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB/E,OAAA;MAAK8E,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD/E,OAAA;QAAK8E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC/E,OAAA,CAACrB,gBAAgB;UAACmG,SAAS,EAAC;QAA+B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DnF,OAAA;UAAA+E,QAAA,gBACE/E,OAAA;YAAI8E,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzDnF,OAAA;YAAG8E,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnF,OAAA;QAAK8E,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C/E,OAAA;UACEoF,OAAO,EAAEA,CAAA,KAAM7B,YAAY,CAAC,UAAU,CAAE;UACxCuB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAEvB/E,OAAA,CAACnB,iBAAiB;YAACiG,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sDAEhD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnF,OAAA;UACEoF,OAAO,EAAEA,CAAA,KAAM7B,YAAY,CAAC,UAAU,CAAE;UACxCuB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAEvB/E,OAAA,CAACnB,iBAAiB;YAACiG,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sDAEhD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnF,OAAA;MAAK8E,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnB/E,OAAA;QAAK8E,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB/E,OAAA;UAAK8E,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD/E,OAAA;YAAA+E,QAAA,gBACE/E,OAAA;cAAO8E,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5CnF,OAAA;cACEkC,KAAK,EAAE1B,UAAW;cAClB6E,QAAQ,EAAGC,CAAC,IAAK7E,aAAa,CAAC6E,CAAC,CAACC,MAAM,CAACrD,KAAY,CAAE;cACtD4C,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAEvB/E,OAAA;gBAAQkC,KAAK,EAAC,SAAS;gBAAA6C,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvCnF,OAAA;gBAAQkC,KAAK,EAAC,UAAU;gBAAA6C,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCnF,OAAA;gBAAQkC,KAAK,EAAC,UAAU;gBAAA6C,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNnF,OAAA;YAAA+E,QAAA,gBACE/E,OAAA;cAAO8E,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3CnF,OAAA;cACEwD,IAAI,EAAC,MAAM;cACXtB,KAAK,EAAE/B,SAAS,CAACE,KAAM;cACvBgF,QAAQ,EAAGC,CAAC,IAAKlF,YAAY,CAACoF,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEnF,KAAK,EAAEiF,CAAC,CAACC,MAAM,CAACrD;cAAM,CAAC,CAAC,CAAE;cAC5E4C,SAAS,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnF,OAAA;YAAA+E,QAAA,gBACE/E,OAAA;cAAO8E,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3CnF,OAAA;cACEwD,IAAI,EAAC,MAAM;cACXtB,KAAK,EAAE/B,SAAS,CAACI,GAAI;cACrB8E,QAAQ,EAAGC,CAAC,IAAKlF,YAAY,CAACoF,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEjF,GAAG,EAAE+E,CAAC,CAACC,MAAM,CAACrD;cAAM,CAAC,CAAC,CAAE;cAC1E4C,SAAS,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL,CAAAxE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAED,IAAI,kBACpBV,OAAA;MAAK8E,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnE/E,OAAA;QAAK8E,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB/E,OAAA;UAAK8E,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB/E,OAAA;YAAK8E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC/E,OAAA,CAACpB,YAAY;cAACkG,SAAS,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDnF,OAAA;cAAK8E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/E,OAAA;gBAAK8E,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC9CpE,eAAe,CAACD,IAAI,CAAC+E,OAAO,CAACC;cAAc;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACNnF,OAAA;gBAAK8E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnF,OAAA;QAAK8E,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB/E,OAAA;UAAK8E,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB/E,OAAA;YAAK8E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC/E,OAAA;cAAK8E,SAAS,EAAC,sEAAsE;cAAAC,QAAA,eACnF/E,OAAA;gBAAM8E,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNnF,OAAA;cAAK8E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/E,OAAA;gBAAK8E,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC9CpE,eAAe,CAACD,IAAI,CAAC+E,OAAO,CAACE;cAAkB;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACNnF,OAAA;gBAAK8E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnF,OAAA;QAAK8E,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB/E,OAAA;UAAK8E,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB/E,OAAA;YAAK8E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC/E,OAAA;cAAK8E,SAAS,EAAC,sEAAsE;cAAAC,QAAA,eACnF/E,OAAA;gBAAM8E,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNnF,OAAA;cAAK8E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/E,OAAA;gBAAK8E,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAC9C,CAACpE,eAAe,CAACD,IAAI,CAAC+E,OAAO,CAACG,eAAe,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GACnE;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNnF,OAAA;gBAAK8E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnF,OAAA;QAAK8E,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB/E,OAAA;UAAK8E,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB/E,OAAA;YAAK8E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC/E,OAAA;cAAK8E,SAAS,EAAC,mEAAmE;cAAAC,QAAA,eAChF/E,OAAA;gBAAM8E,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNnF,OAAA;cAAK8E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/E,OAAA;gBAAK8E,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC9CpE,eAAe,CAACD,IAAI,CAAC+E,OAAO,CAACK;cAAiB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACNnF,OAAA;gBAAK8E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDnF,OAAA;MAAK8E,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD/E,OAAA;QAAK8E,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB/E,OAAA;UAAK8E,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B/E,OAAA;YAAI8E,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACNnF,OAAA;UAAK8E,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB/E,OAAA,CAACF,mBAAmB;YAACiG,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAAjB,QAAA,eAC5C/E,OAAA,CAACP,QAAQ;cAAAsF,QAAA,gBACP/E,OAAA,CAACN,GAAG;gBACFgB,IAAI,EAAEY,YAAa;gBACnB2E,EAAE,EAAC,KAAK;gBACRC,EAAE,EAAC,KAAK;gBACRC,SAAS,EAAE,KAAM;gBACjBC,KAAK,EAAEA,CAAC;kBAAEnE,IAAI;kBAAEoE;gBAAQ,CAAC,KAAK,GAAGpE,IAAI,IAAI,CAACoE,OAAO,GAAG,GAAG,EAAER,OAAO,CAAC,CAAC,CAAC,GAAI;gBACvES,WAAW,EAAE,EAAG;gBAChBC,IAAI,EAAC,SAAS;gBACdC,OAAO,EAAC,OAAO;gBAAAzB,QAAA,EAEdzD,YAAY,CAACQ,GAAG,CAAC,CAAC2E,KAAK,EAAEC,KAAK,kBAC7B1G,OAAA,CAACL,IAAI;kBAAuB4G,IAAI,EAAEE,KAAK,CAACtE;gBAAM,GAAnC,QAAQuE,KAAK,EAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAsB,CACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnF,OAAA,CAACT,OAAO;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnF,OAAA;QAAK8E,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB/E,OAAA;UAAK8E,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B/E,OAAA;YAAI8E,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACNnF,OAAA;UAAK8E,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB/E,OAAA,CAACF,mBAAmB;YAACiG,KAAK,EAAC,MAAM;YAACC,MAAM,EAAE,GAAI;YAAAjB,QAAA,eAC5C/E,OAAA,CAACd,QAAQ;cAACwB,IAAI,EAAE0B,YAAa;cAAA2C,QAAA,gBAC3B/E,OAAA,CAACV,aAAa;gBAACqH,eAAe,EAAC;cAAK;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCnF,OAAA,CAACZ,KAAK;gBACJoH,OAAO,EAAC,MAAM;gBACdI,KAAK,EAAE,CAAC,EAAG;gBACXC,UAAU,EAAC,KAAK;gBAChBb,MAAM,EAAE;cAAG;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACFnF,OAAA,CAACX,KAAK;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTnF,OAAA,CAACT,OAAO;gBACNuH,SAAS,EAAEA,CAAC5E,KAAK,EAAED,IAAI,EAAE8E,KAAK,KAAK,CAAC7E,KAAK,EAAE6E,KAAK,CAACC,OAAO,CAACpE,QAAQ;cAAE;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACFnF,OAAA,CAACb,GAAG;gBAACqH,OAAO,EAAC,OAAO;gBAACD,IAAI,EAAC;cAAS;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnF,OAAA;MAAK8E,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB/E,OAAA;QAAK8E,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1B/E,OAAA;UAAI8E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACNnF,OAAA;QAAK8E,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB/E,OAAA,CAACF,mBAAmB;UAACiG,KAAK,EAAC,MAAM;UAACC,MAAM,EAAE,GAAI;UAAAjB,QAAA,eAC5C/E,OAAA,CAACJ,SAAS;YAACc,IAAI,EAAEmC,SAAU;YAAAkC,QAAA,gBACzB/E,OAAA,CAACV,aAAa;cAACqH,eAAe,EAAC;YAAK;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCnF,OAAA,CAACZ,KAAK;cAACoH,OAAO,EAAC;YAAM;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxBnF,OAAA,CAACX,KAAK;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACTnF,OAAA,CAACT,OAAO;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXnF,OAAA,CAACR,MAAM;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACVnF,OAAA,CAACH,IAAI;cACH2D,IAAI,EAAC,UAAU;cACfgD,OAAO,EAAC,UAAU;cAClBS,MAAM,EAAC,SAAS;cAChBC,WAAW,EAAE,CAAE;cACfjF,IAAI,EAAC;YAAM;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACFnF,OAAA,CAACH,IAAI;cACH2D,IAAI,EAAC,UAAU;cACfgD,OAAO,EAAC,UAAU;cAClBS,MAAM,EAAC,SAAS;cAChBC,WAAW,EAAE,CAAE;cACfjF,IAAI,EAAC;YAAQ;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL,CAAAlE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEP,IAAI,kBAClBV,OAAA;MAAK8E,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB/E,OAAA;QAAK8E,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1B/E,OAAA;UAAI8E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACNnF,OAAA;QAAK8E,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB/E,OAAA;UAAK8E,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD/E,OAAA;YAAK8E,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/E,OAAA;cAAK8E,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EACjD9D,aAAa,CAACP,IAAI,CAACyG;YAAgB;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACNnF,OAAA;cAAK8E,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNnF,OAAA;YAAK8E,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/E,OAAA;cAAK8E,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnD9D,aAAa,CAACP,IAAI,CAAC0G;YAAmB;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACNnF,OAAA;cAAK8E,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACNnF,OAAA;YAAK8E,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/E,OAAA;cAAK8E,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EACjD9D,aAAa,CAACP,IAAI,CAAC2G;YAAc;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACNnF,OAAA;cAAK8E,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACNnF,OAAA;YAAK8E,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/E,OAAA;cAAK8E,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EACjD9D,aAAa,CAACP,IAAI,CAAC4G;YAAW;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACNnF,OAAA;cAAK8E,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjF,EAAA,CAnWID,OAAiB;EAAA,QAQwCvB,QAAQ,EASrCA,QAAQ,EAMVA,QAAQ;AAAA;AAAA6I,EAAA,GAvBlCtH,OAAiB;AAqWvB,eAAeA,OAAO;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}